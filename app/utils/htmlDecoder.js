/**
 * HTML实体解码工具函数
 */

/**
 * 解码HTML实体字符串
 * @param {string} text - 包含HTML实体的字符串
 * @returns {string} 解码后的字符串
 */
export function decodeHtmlEntities(text) {
  if (!text || typeof text !== 'string') return text;
  
  // 在uni-app环境中，我们需要手动处理常见的HTML实体
  const entityMap = {
    '&lt;': '<',
    '&gt;': '>',
    '&amp;': '&',
    '&quot;': '"',
    '&#39;': "'",
    '&apos;': "'",
    '&nbsp;': ' ',
    '&copy;': '©',
    '&reg;': '®',
    '&trade;': '™',
    '&hellip;': '…',
    '&mdash;': '—',
    '&ndash;': '–',
    '&ldquo;': '"',
    '&rdquo;': '"',
    '&bull;': '•',
    '&middot;': '·',
    '&sect;': '§',
    '&para;': '¶',
    '&dagger;': '†',
    '&Dagger;': '‡',
    '&permil;': '‰',
    '&lsaquo;': '‹',
    '&rsaquo;': '›',
    '&euro;': '€',
    '&pound;': '£',
    '&yen;': '¥',
    '&cent;': '¢',
    '&curren;': '¤'
  };
  
  let decodedText = text;
  
  // 替换命名实体
  for (const entity in entityMap) {
    const regex = new RegExp(entity, 'g');
    decodedText = decodedText.replace(regex, entityMap[entity]);
  }
  
  // 处理数字实体 &#数字; 格式
  decodedText = decodedText.replace(/&#(\d+);/g, (match, dec) => {
    return String.fromCharCode(dec);
  });
  
  // 处理十六进制数字实体 &#x十六进制; 格式
  decodedText = decodedText.replace(/&#x([0-9a-fA-F]+);/g, (match, hex) => {
    return String.fromCharCode(parseInt(hex, 16));
  });
  
  return decodedText;
}

/**
 * 递归处理对象中的HTML实体
 * @param {any} obj - 需要处理的对象
 * @returns {any} 处理后的对象
 */
export function decodeObjectHtmlEntities(obj) {
  if (typeof obj === 'string') {
    return decodeHtmlEntities(obj);
  } else if (Array.isArray(obj)) {
    return obj.map(item => decodeObjectHtmlEntities(item));
  } else if (obj && typeof obj === 'object') {
    const decoded = {};
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        decoded[key] = decodeObjectHtmlEntities(obj[key]);
      }
    }
    return decoded;
  }
  return obj;
}

/**
 * 处理巡检项数据中的HTML实体
 * @param {Array} inspectionItems - 巡检项数组
 * @returns {Array} 处理后的巡检项数组
 */
export function decodeInspectionItems(inspectionItems) {
  if (!Array.isArray(inspectionItems)) return inspectionItems;
  
  return inspectionItems.map(item => {
    const decodedItem = { ...item };
    
    // 解码检查项内容
    if (decodedItem.content) {
      decodedItem.content = decodeHtmlEntities(decodedItem.content);
    }
    
    // 解码选项
    if (decodedItem.options && Array.isArray(decodedItem.options)) {
      decodedItem.options = decodedItem.options.map(option => decodeHtmlEntities(option));
    }
    
    // 解码结果
    if (decodedItem.result) {
      decodedItem.result = decodeHtmlEntities(decodedItem.result);
    }
    
    // 解码备注
    if (decodedItem.remark) {
      decodedItem.remark = decodeHtmlEntities(decodedItem.remark);
    }
    
    return decodedItem;
  });
}
