import config from '@/config'
import { isExternal } from '@/utils/validate'

/**
 * 获取图片的完整URL
 * @param {String} path 图片路径
 * @returns {String} 完整的图片URL
 */
export function getImageUrl(path) {
  if (!path) {
    return ''
  }
  // 去除可能存在的空格
  path = path.trim()
  // 如果是外部链接，直接返回
  if (isExternal(path)) {
    return path
  }
  // 添加基础API路径
  return config.baseUrl + path
}

/**
 * 处理图片列表（逗号分隔的图片路径）
 * @param {String} images 逗号分隔的图片路径
 * @returns {Array} 图片URL数组
 */
export function getImageList(images) {
  if (!images) {
    return []
  }
  // 分割图片路径
  const imageList = images.split(',').filter(item => item.trim() !== '')
  // 处理每个图片路径
  return imageList.map(item => getImageUrl(item))
}

/**
 * 获取图片列表中的第一张图片
 * @param {String} images 逗号分隔的图片路径
 * @returns {String} 第一张图片的URL
 */
export function getFirstImage(images) {
  if (!images) {
    return ''
  }
  const imageList = getImageList(images)
  return imageList.length > 0 ? imageList[0] : ''
}
