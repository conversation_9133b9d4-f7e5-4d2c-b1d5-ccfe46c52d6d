import config from '@/config'

/**
 * 图片URL处理工具类
 * 用于统一处理图片URL的相对路径和完整URL转换
 */

/**
 * 获取相对路径（去掉baseUrl前缀）
 * @param {String} fullUrl 完整的图片URL
 * @returns {String} 相对路径
 */
export function getRelativePath(fullUrl) {
  if (!fullUrl) return '';
  
  const baseUrl = config.baseUrl;
  if (fullUrl.startsWith(baseUrl)) {
    return fullUrl.substring(baseUrl.length);
  }
  return fullUrl;
}

/**
 * 获取完整URL（添加baseUrl前缀）
 * @param {String} relativePath 相对路径
 * @returns {String} 完整的图片URL
 */
export function getFullUrl(relativePath) {
  if (!relativePath) return '';
  
  // 如果已经是完整URL，直接返回
  if (relativePath.startsWith('http://') || relativePath.startsWith('https://')) {
    return relativePath;
  }
  
  return config.baseUrl + relativePath;
}

/**
 * 批量转换图片URL数组为相对路径
 * @param {Array} urlArray 图片URL数组
 * @returns {Array} 相对路径数组
 */
export function convertToRelativePaths(urlArray) {
  if (!Array.isArray(urlArray)) return [];
  
  return urlArray.map(url => getRelativePath(url));
}

/**
 * 批量转换相对路径数组为完整URL
 * @param {Array} pathArray 相对路径数组
 * @returns {Array} 完整URL数组
 */
export function convertToFullUrls(pathArray) {
  if (!Array.isArray(pathArray)) return [];
  
  return pathArray.map(path => getFullUrl(path));
}

/**
 * 处理巡检项目中的图片URL
 * 将存储的相对路径转换为显示用的完整URL
 * @param {Array} inspectionItems 巡检项目数组
 * @returns {Array} 处理后的巡检项目数组
 */
export function processInspectionItemsForDisplay(inspectionItems) {
  if (!Array.isArray(inspectionItems)) return [];
  
  return inspectionItems.map(item => {
    const processedItem = { ...item };
    
    if (processedItem.photos && Array.isArray(processedItem.photos)) {
      // 为显示创建完整URL数组，但保持原始的相对路径不变
      processedItem.displayPhotos = convertToFullUrls(processedItem.photos);
    }
    
    return processedItem;
  });
}

/**
 * 处理巡检项目中的图片URL用于保存
 * 将完整URL转换为相对路径进行存储
 * @param {Array} inspectionItems 巡检项目数组
 * @returns {Array} 处理后的巡检项目数组
 */
export function processInspectionItemsForSave(inspectionItems) {
  if (!Array.isArray(inspectionItems)) return [];
  
  return inspectionItems.map(item => {
    const processedItem = { ...item };
    
    if (processedItem.photos && Array.isArray(processedItem.photos)) {
      // 转换为相对路径进行存储
      processedItem.photos = convertToRelativePaths(processedItem.photos);
    }
    
    // 移除显示用的字段
    delete processedItem.displayPhotos;
    delete processedItem.fileList;
    delete processedItem.uploadedFiles;
    delete processedItem.showRemark;
    
    return processedItem;
  });
}
