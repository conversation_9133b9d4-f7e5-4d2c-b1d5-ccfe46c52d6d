<template>
  <view class="container">
    <!-- 顶部导航栏 -->
    <view class="header" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="header-content">
        <text class="header-title">E1030新能源智慧运维系统</text>
        <view class="header-right">
          <view class="refresh-icon" @click="refreshPage">
            <uni-icons type="refresh" size="18" color="#FFFFFF"></uni-icons>
          </view>
        </view>
      </view>
    </view>

    <!-- 内容区域 -->
    <scroll-view scroll-y class="content" :style="{ paddingTop: (statusBarHeight + 44) + 'px' }">
      <!-- 电站概览 -->
      <view class="station-overview-section">
        <view class="section-header">
          <text class="section-title">电站概览</text>
          <view class="station-search-container">
            <uni-search-bar @input="searchStations" @clear="resetSearch" @cancel="resetSearch" v-model="searchKeyword" placeholder="搜索电站"
              :radius="20" bgColor="#f5f5f5" :cancelButton="searchKeyword ? 'always' : 'none'" cancelText="取消"
              :focus="false" clearButton="auto" />
          </view>
        </view>

        <!-- 分类筛选 -->
        <scroll-view scroll-x class="type-filter-scroll">
          <view class="type-filter-list">
            <view v-for="(type, index) in ownerTypes" :key="index" class="type-filter-item"
              :class="{ active: currentStationType === type }" @click="filterStationsByType(type)">
              <text class="type-text">{{ type }}</text>
              <text class="type-count">{{ getStationCountByType(type) }}</text>
            </view>
          </view>
        </scroll-view>

        <!-- 电站列表 -->
        <view class="station-list">
          <view class="station-list-item" v-for="(item, index) in filteredStations" :key="index" @click="goToStationDetail(item.id)">
            <view class="station-list-left">
              <image :src="getFirstImage(item.images)" mode="aspectFill" class="station-image" @error="() => item.useDefaultImage = true"
                v-if="item.images && item.images !== '' && !item.useDefaultImage"></image>
              <image :src="defaultStationImage" mode="aspectFill" v-else class="station-image"></image>
            </view>
            <view class="station-list-content">
              <view class="station-list-header">
                <text class="station-name">{{ item.name }}</text>
                <uni-icons type="right" size="12" color="#c0c0c0" class="arrow-icon"></uni-icons>
              </view>

              <view class="station-list-info">
                <view class="info-row">
                  <view class="info-item">
                    <text class="info-label">直流容量</text>
                    <text class="info-value">{{ item.directCurrent }}<text class="info-unit">MW</text></text>
                  </view>
                  <view class="info-item">
                    <text class="info-label">交流容量</text>
                    <text class="info-value">{{ item.capacity }}<text class="info-unit">MW</text></text>
                  </view>
                </view>

                <view class="info-row">
                  <view class="info-item">
                    <text class="info-label">投资类型</text>
                    <text class="info-value">{{ item.type }}</text>
                  </view>
                  <view class="info-item">
                    <text class="info-label">并网类型</text>
                    <text class="info-value">{{ item.gridType }}</text>
                  </view>
                </view>

                <view class="station-address-container">
                  <uni-icons type="location" size="10" color="#909399"></uni-icons>
                  <text class="station-address">{{ formatDistrict(item) }}</text>
                </view>
              </view>
            </view>
          </view>

          <!-- 无数据提示 -->
          <view v-if="filteredStations.length === 0" class="empty-tip">
            <image src="/static/images/empty/data_is_empty.png" mode="aspectFit" class="empty-image"></image>
            <text class="empty-text">暂无电站数据</text>
          </view>

          <!-- 暂无更多电站提示 -->
          <view v-if="filteredStations.length > 0" class="no-more-tip">
            <text class="no-more-text">暂无更多电站</text>
          </view>

        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script>

import { getDictData } from '@/api/system/dictData'
import { listStation } from '@/api/system/station'
import { getFirstImage } from '@/utils/imageUtils'

export default {
  data() {
    return {
      statusBarHeight: 0,
      // 电站类型
      stationTypes: [],
      // 投资类型（用于顶部筛选）
      ownerTypes: ['全部'],
      // 并网类型
      gridTypes: [],
      // 字典数据
      dictData: {},
      defaultStationImage: '/static/images/solar-power-station.png',
      searchKeyword: '',
      currentStationType: '全部',
      stations: [],
      filteredStations: [],
      // 搜索防抖定时器
      searchTimer: null
    }
  },
  onLoad() {
    // 设置状态栏高度
    this.statusBarHeight = uni.getSystemInfoSync().statusBarHeight;
    // 获取电站字典数据
    this.getStationDicts();
  },
  onUnload() {
    // 清理搜索定时器
    if (this.searchTimer) {
      clearTimeout(this.searchTimer);
      this.searchTimer = null;
    }
  },
  methods: {
    // 获取电站字典数据
    async getStationDicts() {
      try {
        const response = await getDictData();
        // 使用字典插件解析字典数据
        this.dictData = this.$dict.parseDictData(response.data);
        // 设置电站类型列表
        this.stationTypes = this.dictData.station_type;
        // 设置投资类型列表，添加'全部'选项（用于顶部筛选）
        this.ownerTypes = ['全部', ...this.dictData.station_owner_type.map(item => item.label)];
        // 设置并网类型列表
        this.gridTypes = this.dictData.station_grid_type;
        // 更新电站列表中的字典标签
        this.updateStationDictLabels();
        // 获取电站数据
        this.getStationList();
      } catch (error) {
        uni.showToast({
          title: '获取字典数据失败',
          icon: 'none'
        });
      }
    },

    // 获取电站列表
    async getStationList() {
      try {
        const response = await listStation({});
        this.stations = response.data.map(station => ({
          id: station.stationId,
          name: station.stationName,
          address: station.address,
          directCurrent: station.dcCapacity,
          capacity: station.acCapacity,
          type: station.ownerType, // 原始值，后续会转换为标签
          image: station.images ? getFirstImage(station.images) : '',
          images: station.images || '',
          useDefaultImage: !station.images || station.images === '',
          networkTime: station.gridTime,
          gridType: station.gridType, // 原始值，后续会转换为标签
          status: station.status,
          priceType: station.priceType,
          stationType: station.stationType // 添加电站类型字段
        }));

        // 如果字典数据已加载，则更新电站字典标签
        if (Object.keys(this.dictData).length > 0) {
          this.updateStationDictLabels();
        } else {
          this.filteredStations = [...this.stations];
        }
      } catch (error) {
        console.error('获取电站列表失败:', error);
        uni.showToast({
          title: '获取电站列表失败',
          icon: 'none'
        });
      }
    },

    // 更新电站列表中的字典标签
    updateStationDictLabels() {
      if (this.stations.length === 0 || !this.dictData.station_type) {
        return;
      }

      this.stations = this.stations.map(station => ({
        ...station,
        // 转换投资类型的值为标签
        type: this.$dict.getDictLabel('station_owner_type', station.type, station.type),
        // 转换并网类型的值为标签
        gridType: this.$dict.getDictLabel('station_grid_type', station.gridType, station.gridType)
      }));

      // 应用当前的筛选条件
      this.applyFilters();
    },

    // 格式化地址显示
    formatDistrict(station) {
      return station.address || '暂无地址';
    },

    // 搜索电站（即时搜索，带防抖）
    searchStations(e) {
      const keyword = e.value || e || '';
      this.searchKeyword = keyword;

      // 清除之前的定时器
      if (this.searchTimer) {
        clearTimeout(this.searchTimer);
      }

      // 设置新的定时器，300ms后执行搜索
      this.searchTimer = setTimeout(() => {
        this.applyFilters();
      }, 300);
    },

    // 重置搜索
    resetSearch() {
      // 清除搜索定时器
      if (this.searchTimer) {
        clearTimeout(this.searchTimer);
        this.searchTimer = null;
      }

      this.searchKeyword = '';
      this.applyFilters();
    },

    // 获取每种类型的电站数量（考虑搜索条件）
    getStationCountByType(type) {
      // 获取基础数据源（考虑搜索条件）
      let baseStations = this.stations;

      // 如果有搜索关键词，先按搜索条件过滤
      if (this.searchKeyword) {
        baseStations = this.stations.filter(station => {
          return station.name.includes(this.searchKeyword);
        });
      }

      if (type === '全部') {
        return baseStations.length;
      } else {
        // 根据投资类型进行筛选
        return baseStations.filter(station => station.type === type).length;
      }
    },

    // 按类型筛选电站
    filterStationsByType(type) {
      this.currentStationType = type;
      this.applyFilters();
    },

    // 应用所有筛选条件（类型筛选 + 搜索）
    applyFilters() {
      let result = [...this.stations];

      // 先按类型筛选
      if (this.currentStationType !== '全部') {
        result = result.filter(station => station.type === this.currentStationType);
      }

      // 再按搜索关键词筛选
      if (this.searchKeyword) {
        result = result.filter(station => {
          return station.name.includes(this.searchKeyword);
        });
      }

      this.filteredStations = result;
    },

    // 刷新界面
    refreshPage() {
      // 保存当前的搜索和筛选状态
      const currentSearchKeyword = this.searchKeyword;
      const currentStationType = this.currentStationType;

      this.getStationList().then(() => {
        // 恢复搜索和筛选状态
        this.searchKeyword = currentSearchKeyword;
        this.currentStationType = currentStationType;
        this.applyFilters();
      });

      uni.showToast({
        title: '刷新成功',
        icon: 'none'
      });
    },

    // 跳转到电站详情
    goToStationDetail(id) {
      uni.navigateTo({
        url: `/pages/station/detail?id=${id}`
      })
    },
    goToStationList() {
      uni.navigateTo({
        url: '/pages/station/list'
      })
    },

    goToDeviceList() {
      uni.navigateTo({
        url: '/pages/device/list'
      })
    },
    goToDeviceDetail(id) {
      uni.navigateTo({
        url: `/pages/device/detail?id=${id}`
      })
    },
    goToFunction(path) {
      uni.navigateTo({
        url: path
      })
    },
    goToAlertList() {
      uni.navigateTo({
        url: '/pages/alert/list'
      })
    },
    goToAlertDetail(id) {
      uni.navigateTo({
        url: `/pages/alert/detail?id=${id}`
      })
    },

    // 获取第一张图片
    getFirstImage(images) {
      return getFirstImage(images);
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f7fa;
}

/* 顶部导航栏 */
.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: linear-gradient(135deg, #1a56b3 0%, #2d7dd2 100%);
  box-shadow: 0 2px 8px rgba(26, 86, 179, 0.15);
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 43px;
  padding: 0 15px;
}

.header-title {
  font-size: 18px;
  font-weight: 600;
  color: #FFFFFF;
  letter-spacing: 0.5px;
}

.header-right {
  width: 32px;
}

.refresh-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 6px;
}

/* 内容区域 */
.content {
  flex: 1;
  background-color: #ffffff;
}

/* 分类筛选 */
.type-filter-scroll {
  margin: 6px 0;
  white-space: nowrap;
}

.type-filter-list {
  display: flex;
  padding: 5px 0;
}

.type-filter-item {
  padding: 6px 15px;
  margin-right: 10px;
  font-size: 14px;
  color: #666666;
}

.type-filter-item.active {
  color: #ffffff;
}

/* 电站样式 */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 40px;
}

.station-search-container {
  flex: 1;
  max-width: 220px;
  margin-left: 10px;
}

/* 自定义搜索框样式 */
:deep(.uni-searchbar) {
  padding: 0;
}

:deep(.uni-searchbar__box) {
  height: 26px;
}

:deep(.uni-input-placeholder) {
  font-size: 12px;
}

:deep(.uni-searchbar__cancel) {
  font-size: 13px;
  color: #1a56b3;
  padding-left: 5px;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #333333;
}

/* 电站概览 */
.station-overview-section {
  background-color: #ffffff;
  margin: 0;
  padding: 15px;
}

/* 分类筛选 */
.type-filter-scroll {
  margin: 6px 0;
  white-space: nowrap;
}

.type-filter-list {
  display: flex;
  padding: 5px 0;
  border-bottom: 1px solid #f0f0f0;
}

.type-filter-item {
  padding: 8px 6px 12px 2px;
  margin-right: 20px;
  color: #909399;
  display: flex;
  align-items: center;
  position: relative;
  transition: all 0.3s ease;
}

.type-text {
  font-size: 14px;
  font-weight: normal;
  transition: all 0.3s ease;
}

.type-filter-item.active {
  color: #1a56b3;
}

.type-filter-item.active .type-text {
  font-weight: bold;
}

.type-filter-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 4%;
  width: 30px;
  height: 3px;
  background-color: #1a56b3;
  border-radius: 3px;
}

.type-count {
  margin-left: 6px;
  font-size: 11px;
  color: #909399;
  background-color: #f5f5f5;
  border-radius: 10px;
  padding: 2px 4px;
  min-width: 12px;
  text-align: center;
  transition: all 0.3s ease;
}

.type-filter-item.active .type-count {
  background-color: rgba(26, 86, 179, 0.1);
  color: #1a56b3;
}

/* 电站列表样式 */
.station-list {
  background-color: #ffffff;
}

.station-list-item {
  display: flex;
  align-items: flex-start;
  background: #ffffff;
  padding: 14px 8px;
  position: relative;
  border-bottom: 1px solid #eaeef1;
  min-height: 70px;
}

.station-list-item:last-child {
  border-bottom: none;
}

.station-list-left {
  width: 50px;
  height: 60px;
  margin-right: 12px;
  flex-shrink: 0;
  margin-top: 2px;
}

.station-image {
  width: 50px;
  height: 60px;
  border-radius: 4px;
  object-fit: cover;
}

.station-list-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
}

.station-list-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 10px;
  height: 20px;
}

.arrow-icon {
  margin-left: 8px;
  flex-shrink: 0;
}

.station-name {
  font-size: 15px;
  font-weight: 600;
  color: #333333;
  flex: 1;
  line-height: 20px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.status-0 {
  background-color: rgba(103, 194, 58, 0.1);
  color: #67c23a;
}

.status-1 {
  background-color: rgba(230, 162, 60, 0.1);
  color: #e6a23c;
}

.station-list-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.info-row {
  display: flex;
  margin-bottom: 8px;
  height: 16px;
  align-items: center;
}

.info-item {
  display: flex;
  align-items: baseline;
  width: 50%;
  min-height: 16px;
}

.info-label {
  font-size: 11px;
  color: #999999;
  margin-right: 4px;
  white-space: nowrap;
  width: 50px;
  flex-shrink: 0;
}

.info-value {
  font-size: 11px;
  color: #333333;
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.info-unit {
  font-size: 10px;
  font-weight: normal;
  margin-left: 1px;
}

.station-address-container {
  display: flex;
  align-items: center;
  margin-top: 6px;
  height: 14px;
}

.station-address {
  font-size: 10px;
  color: #909399;
  margin-left: 3px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 14px;
}

.empty-state {
  padding: 30px 0;
  display: flex;
  justify-content: center;
  align-items: center;
}

.empty-text {
  font-size: 14px;
  color: #909399;
}

/* 查看全部按钮 */
.view-all-section {
  padding: 0 15px 10px 15px;
}

.view-all-btn {
  background-color: #1a56b3;
  color: #ffffff;
  border-radius: 8px;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px 0;
  border: none;
}

.view-all-btn text {
  margin-left: 5px;
}

/* 无数据提示 */
.empty-tip {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 50px 0;
}

.empty-image {
  width: 100px;
  height: 100px;
  margin-bottom: 15px;
}

.empty-text {
  font-size: 14px;
  color: #909399;
}

/* 暂无更多电站提示 */
.no-more-tip {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px 0;
  margin-top: 10px;
}

.no-more-text {
  font-size: 12px;
  color: #c0c4cc;
  position: relative;
}
</style>
