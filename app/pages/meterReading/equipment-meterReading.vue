<template>
  <view class="container">
    <!-- 顶部导航栏 -->
    <view class="header" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="header-content">
        <view class="back-icon" @click="goBack">
          <uni-icons type="left" size="18" color="#FFFFFF"></uni-icons>
        </view>
        <view class="header-title">设备电量统计</view>
        <view style="width: 20px;"></view>
      </view>
    </view>

    <!-- 内容区域 -->
    <scroll-view scroll-y class="content" :style="{ paddingTop: (statusBarHeight + 44) + 'px' }">
      <view v-if="equipmentInfo" class="meter-reading-detail">
        <!-- 任务状态标识 -->
        <view class="status-banner status-meter-reading">
          <view class="status-content">
            <view class="status-left">
              <view class="status-icon">
                <uni-icons type="gear-filled" size="18" color="#FFFFFF"></uni-icons>
              </view>
              <text class="status-text">统计中</text>
            </view>
            <!-- 操作按钮 -->
            <view class="action-buttons-top">
              <button class="action-button-top cancel" @click="goBack">
                取消
              </button>
              <button class="action-button-top save" @click="handleSave">
                保存
              </button>
            </view>
          </view>
        </view>

        <!-- 设备信息区域 -->
        <view class="detail-card">
          <view class="card-header">
            <uni-icons type="gear" size="16" color="#1a56b3"></uni-icons>
            <text class="card-title">设备信息</text>
          </view>
          <view class="card-content">
            <view class="info-item">
              <text class="info-label">设备名称</text>
              <text class="info-value">{{ equipmentInfo.equipmentName }}</text>
            </view>
            <view class="info-item" v-if="equipmentInfo.equipmentCode">
              <text class="info-label">设备编号</text>
              <text class="info-value">{{ equipmentInfo.equipmentCode }}</text>
            </view>
            <view class="info-item">
              <text class="info-label">设备类型</text>
              <text class="info-value">{{ getEquipmentTypeLabel(equipmentInfo.equipmentType) }}</text>
            </view>
            <view class="info-item" v-if="equipmentInfo.model">
              <text class="info-label">设备型号</text>
              <text class="info-value">{{ equipmentInfo.model }}</text>
            </view>
          </view>
        </view>

        <!-- 电量统计信息表单 -->
        <view class="detail-card">
          <view class="card-header">
            <uni-icons type="checkmarkempty" size="16" color="#1a56b3"></uni-icons>
            <text class="card-title">电量统计信息</text>
          </view>
          <view class="card-content">
            <!-- 上月反向有功 -->
            <view class="form-item">
              <view class="form-label">
                <uni-icons type="gear" size="14" color="#1a56b3"></uni-icons>
                <text class="label-text">上月反向有功(kWh)</text>
                <text class="required">*</text>
              </view>
              <uni-easyinput
                v-model="meterReadingData.lastMonthReverseActive"
                type="number"
                placeholder="请输入上月反向有功电量"
                class="form-input"
              />
            </view>

            <!-- 反向有功总 -->
            <view class="form-item">
              <view class="form-label">
                <uni-icons type="gear" size="14" color="#1a56b3"></uni-icons>
                <text class="label-text">反向有功总(kWh)</text>
                <text class="required">*</text>
              </view>
              <uni-easyinput
                v-model="meterReadingData.totalReverseActive"
                type="number"
                placeholder="请输入反向有功总电量"
                class="form-input"
              />
            </view>

            <!-- 统计照片 -->
            <view class="form-item">
              <view class="form-label">
                <uni-icons type="camera-filled" size="14" color="#1a56b3"></uni-icons>
                <text class="label-text">统计照片</text>
                <text class="required">*</text>
              </view>
              <uni-file-picker
                v-model="photoList"
                file-mediatype="image"
                mode="grid"
                :limit="6"
                :image-styles="imageStyles"
                :auto-upload="false"
                @select="(e) => selectPhotos(e)"
                @delete="(e) => deletePhoto(e)"
                :source-type="['camera']"
              />
            </view>

            <!-- 统计备注 -->
            <view class="form-item">
              <view class="form-label">
                <uni-icons type="chatbubble-filled" size="14" color="#1a56b3"></uni-icons>
                <text class="label-text">统计备注</text>
              </view>
              <textarea
                class="remark-textarea"
                placeholder="请输入统计备注信息（选填）"
                v-model="meterReadingData.meterReadingRemark"
                maxlength="200"
              ></textarea>
            </view>
          </view>
        </view>
      </view>

      <loading-container
        v-else
        :loading="loading"
        :error="!loading && !equipmentInfo"
        loadingText="正在加载设备信息..."
        errorText="获取设备信息失败"
        @retry="refreshData"
      />
    </scroll-view>

    <!-- 隐藏的canvas用于水印处理 -->
    <canvas
      v-if="waterMarkParams.display"
      canvas-id="watermarkCanvas"
      :style="canvasStyle"
    ></canvas>
  </view>
</template>

<script>
import { getMeterReading, getMeterReadingEquipments, updateEquipmentMeterReading } from '@/api/system/meterReading'
import { getEquipment } from '@/api/system/equipment'
import { getDictData } from '@/api/system/dictData'
import { uploadFile } from '@/api/system/common'
import LoadingContainer from '@/components/LoadingContainer'
import { getRelativePath, getFullUrl } from '@/utils/imageUrlUtils'
import { addInspectionWatermark } from '@/utils/watermark'

export default {
  components: {
    LoadingContainer
  },
  data() {
    return {
      statusBarHeight: 0,
      meterReadingId: null,
      equipmentId: null,
      meterReadingInfo: null,
      equipmentInfo: null,
      meterReadingEquipment: null,
      meterReadingData: {
        lastMonthReverseActive: '',
        totalReverseActive: '',
        photos: '',
        meterReadingRemark: ''
      },
      photoList: [],
      photoUrls: [],
      imageStyles: {
        width: 80,
        height: 80,
        border: {
          radius: '4px'
        }
      },
      loading: true,
      dictData: {},
      userId: null,
      waterMarkParams: {
        display: false,
        canvasWidth: 300,
        canvasHeight: 225,
      }
    }
  },
  computed: {
    canvasStyle() {
      return {
        position: 'fixed',
        left: '9999px',
        width: this.waterMarkParams.canvasWidth + 'px',
        height: this.waterMarkParams.canvasHeight + 'px',
      }
    },

  },
  onLoad(options) {
    // 设置状态栏高度
    this.statusBarHeight = uni.getSystemInfoSync().statusBarHeight;

    // 获取当前登录用户ID
    this.userId = this.$store.state.user.userId;

    if (options.meterReadingId && options.equipmentId) {
      this.meterReadingId = options.meterReadingId;
      this.equipmentId = options.equipmentId;
      this.getDicts();
    } else {
      this.loading = false;
      uni.showToast({
        title: '缺少必要参数',
        icon: 'none'
      });
    }
  },
  methods: {
    // 获取字典数据
    async getDicts() {
      try {
        const response = await getDictData();
        this.dictData = this.$dict.parseDictData(response.data);
        this.loadData();
      } catch (error) {
        console.error('获取字典数据失败:', error);
        uni.showToast({
          title: '获取字典数据失败',
          icon: 'none'
        });
        this.loading = false;
      }
    },

    // 加载数据
    async loadData() {
      try {
        // 并行加载数据
        const [meterReadingResponse, equipmentResponse, meterReadingEquipmentsResponse] = await Promise.all([
          getMeterReading(this.meterReadingId),
          getEquipment(this.equipmentId),
          getMeterReadingEquipments(this.meterReadingId)
        ]);

        this.meterReadingInfo = meterReadingResponse.data;
        this.equipmentInfo = equipmentResponse.data;

        // 查找当前设备的电量统计记录
        const equipmentList = meterReadingEquipmentsResponse.data || [];
        this.meterReadingEquipment = equipmentList.find(item =>
          item.equipmentId.toString() === this.equipmentId.toString()
        );

        // 加载已有数据
        if (this.meterReadingEquipment) {
          this.loadExistingMeterReadingData();
        }
      } catch (error) {
        console.error('加载数据失败:', error);
        uni.showToast({
          title: '加载数据失败',
          icon: 'none'
        });
      } finally {
        this.loading = false;
      }
    },

    // 加载已有的电量统计数据
    loadExistingMeterReadingData() {
      if (this.meterReadingEquipment) {
        this.meterReadingData = {
          lastMonthReverseActive: this.meterReadingEquipment.lastMonthReverseActive || '',
          totalReverseActive: this.meterReadingEquipment.totalReverseActive || '',
          photos: this.meterReadingEquipment.photos || '',
          meterReadingRemark: this.meterReadingEquipment.meterReadingRemark || ''
        };

        // 处理电量统计照片
        if (this.meterReadingData.photos) {
          const photoUrls = this.meterReadingData.photos.split(',').filter(url => url.trim());
          this.photoList = photoUrls.map((url, index) => ({
            url: getFullUrl(url),
            name: `meter_reading_${index}.jpg`,
            extname: 'jpg',
            size: 0
          }));
          this.photoUrls = photoUrls;
        }


      }
    },

    // 获取设备类型标签
    getEquipmentTypeLabel(type) {
      if (!type || !this.dictData.equipment_type) return '未知类型';
      return this.$dict.getDictLabel('equipment_type', type, '未知类型');
    },

    // 选择电量统计照片
    selectPhotos(e) {
      this.uploadImages(e.tempFiles, 'photos');
    },

    // 删除电量统计照片
    deletePhoto(e) {
      const index = e.index;
      this.photoUrls.splice(index, 1);
      this.meterReadingData.photos = this.photoUrls.join(',');
    },

    // 上传图片
    async uploadImages(tempFiles, type) {
      if (tempFiles.length === 0) return;

      uni.showLoading({
        title: '正在上传...',
        mask: true
      });

      try {
        const uploadPromises = tempFiles.map(async (file) => {
          // 生成自定义文件名：DL_{设备名称}_{时间戳}
          const timestamp = this.generateTimestamp();
          const equipmentName = this.equipmentInfo.equipmentName || 'Unknown';
          const cleanEquipmentName = equipmentName.replace(/[^\u4e00-\u9fa5a-zA-Z0-9_]/g, '_');
          const customFileName = `DL_${cleanEquipmentName}_${timestamp}`;

          // 准备水印信息
          const watermarkInfo = {
            taskName: this.meterReadingInfo?.meterReadingName || '设备电量统计',
            equipmentName: this.equipmentInfo?.equipmentName || '未知设备',
            time: this.formatDateTime(new Date())
          };

          // 为图片添加水印
          const watermarkedImagePath = await addInspectionWatermark(file.path, watermarkInfo, this);

          // 构建formData
          const formData = {
            customFileName: customFileName
          };

          // 上传图片
          const res = await uploadFile(watermarkedImagePath, formData);

          if (res.code === 200) {
            return res.url;
          } else {
            throw new Error(res.msg || '上传失败');
          }
        });

        const uploadedUrls = await Promise.all(uploadPromises);

        // 更新照片数组
        this.photoUrls = [...this.photoUrls, ...uploadedUrls.map(url => getRelativePath(url))];
        this.meterReadingData.photos = this.photoUrls.join(',');

        uni.hideLoading();
        uni.showToast({
          title: '图片上传成功',
          icon: 'success'
        });
      } catch (error) {
        uni.hideLoading();
        console.error('图片上传失败:', error);
        uni.showToast({
          title: '图片上传失败',
          icon: 'none'
        });
      }
    },

    // 生成时间戳
    generateTimestamp() {
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, '0');
      const day = String(now.getDate()).padStart(2, '0');
      const hours = String(now.getHours()).padStart(2, '0');
      const minutes = String(now.getMinutes()).padStart(2, '0');
      const seconds = String(now.getSeconds()).padStart(2, '0');
      return `${year}${month}${day}${hours}${minutes}${seconds}`;
    },

    // 格式化日期时间
    formatDateTime(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },

    // 验证表单完整性
    validateForm() {
      const errors = [];

      const lastMonth = this.meterReadingData.lastMonthReverseActive;
      if (!lastMonth && lastMonth !== 0 && lastMonth !== '0') {
        errors.push('请输入上月反向有功电量');
      }

      const total = this.meterReadingData.totalReverseActive;
      if (!total && total !== 0 && total !== '0') {
        errors.push('请输入反向有功总电量');
      }

      if (!this.meterReadingData.photos) {
        errors.push('请上传电量统计照片');
      }

      return errors;
    },

    // 保存电量统计数据
    async handleSave() {
      try {
        // 检查必填项是否完整
        const errors = this.validateForm();

        if (errors.length > 0) {
          // 显示详细的错误信息
          let message = '以下信息需要完善：\n\n';
          errors.forEach((error, index) => {
            message += `${index + 1}. ${error}\n`;
          });

          message += '\n是否仍要保存当前进度？';

          // 显示确认对话框，允许用户选择是否继续保存
          const result = await new Promise((resolve) => {
            uni.showModal({
              title: '电量统计信息不完整',
              content: message,
              showCancel: true,
              cancelText: '继续编辑',
              confirmText: '仍要保存',
              success: (res) => {
                resolve(res.confirm);
              },
              fail: () => {
                resolve(false);
              }
            });
          });

          // 如果用户选择不保存，直接返回
          if (!result) {
            return;
          }
        }

        uni.showLoading({
          title: '保存中...'
        });

        const meterReadingData = {
          meterReadingId: parseInt(this.meterReadingId),
          equipmentId: parseInt(this.equipmentId),
          userId: parseInt(this.userId),
          lastMonthReverseActive: this.meterReadingData.lastMonthReverseActive ? parseFloat(this.meterReadingData.lastMonthReverseActive) : null,
          totalReverseActive: this.meterReadingData.totalReverseActive ? parseFloat(this.meterReadingData.totalReverseActive) : null,
          photos: this.meterReadingData.photos || null,
          meterReadingRemark: this.meterReadingData.meterReadingRemark || null,
          meterReadingTime: this.formatDateTime(new Date())
        };

        const response = await updateEquipmentMeterReading(meterReadingData);

        if (response.code === 200) {
          // 检查响应数据中的success字段
          if (response.data && response.data.success === false) {
            throw new Error(response.data.message || '保存失败');
          }

          uni.hideLoading();

          // 根据完成状态显示不同的提示信息
          const isCompleted = errors.length === 0;
          const toastTitle = isCompleted ? '电量统计保存成功' : '进度已保存';
          const toastIcon = isCompleted ? 'success' : 'none';

          uni.showToast({
            title: toastTitle,
            icon: toastIcon,
            duration: 2000,
            success: () => {
              setTimeout(() => {
                // 获取页面栈
                const pages = getCurrentPages();
                // 获取上一个页面
                const prevPage = pages[pages.length - 2];
                // 如果上一个页面存在且有refreshData方法，调用它刷新数据
                if (prevPage && prevPage.$vm && prevPage.$vm.refreshData) {
                  prevPage.$vm.refreshData();
                }
                // 返回上一页
                uni.navigateBack();
              }, 1000);
            }
          });
        } else {
          throw new Error(response.msg || '保存失败');
        }
      } catch (error) {
        uni.hideLoading();
        console.error('保存电量统计数据失败:', error);
        uni.showToast({
          title: error.message || '保存失败',
          icon: 'none'
        });
      }
    },

    // 返回上一页
    goBack() {
      // 获取页面栈
      const pages = getCurrentPages();
      // 获取上一个页面
      const prevPage = pages[pages.length - 2];
      // 如果上一个页面存在且有refreshData方法，调用它刷新数据
      if (prevPage && prevPage.$vm && prevPage.$vm.refreshData) {
        prevPage.$vm.refreshData();
      }
      uni.navigateBack();
    },

    // 刷新数据
    refreshData() {
      this.loading = true;
      this.getDicts();
      uni.showToast({
        title: '刷新中',
        icon: 'loading',
        duration: 500
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #ffffff;
}

/* 顶部导航栏 */
.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: linear-gradient(135deg, #1a56b3 0%, #3a7bd5 100%);
  padding: 10px 15px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.back-icon {
  padding: 5px;
}

.header-title {
  color: #ffffff;
  font-size: 18px;
  font-weight: bold;
}

/* 内容区域 */
.content {
  flex: 1;
}

.meter-reading-detail {
  padding: 0;
}

/* 状态横幅 */
.status-banner {
  padding: 15px 16px;
  color: #ffffff;
  position: relative;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.status-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-left {
  display: flex;
  align-items: center;
}

.status-icon {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
}

.status-text {
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0.5px;
}

/* 上方操作按钮 */
.action-buttons-top {
  display: flex;
  gap: 10px;
}

.action-button-top {
  background-color: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.4);
  color: #ffffff;
  font-size: 13px;
  padding: 4px 12px;
  border-radius: 15px;
  height: auto;
  line-height: 1.5;
  font-weight: normal;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.action-button-top:active {
  transform: scale(0.95);
  background-color: rgba(255, 255, 255, 0.3);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.action-button-top.cancel {
  background-color: rgba(255, 255, 255, 0.1);
}

.action-button-top.save {
  background-color: rgba(255, 255, 255, 0.2);
}

/* 分区样式 */
.detail-card {
  background-color: #ffffff;
  margin-bottom: 10px;
  position: relative;
}

.card-header {
  display: flex;
  align-items: center;
  padding: 15px 16px 10px;
  position: relative;
}

.card-title {
  font-size: 16px;
  font-weight: bold;
  margin-left: 8px;
  color: #1a56b3;
}

.card-content {
  padding: 5px 16px 15px;
}

/* 基本信息样式 */
.info-item {
  display: flex;
  margin-bottom: 10px;
  align-items: center;
  position: relative;
  border-bottom: 1px solid #f9f9f9;
  padding-bottom: 10px;
}

.info-item:last-child {
  margin-bottom: 0;
  border-bottom: none;
  padding-bottom: 0;
}

.info-label {
  width: 80px;
  color: #666;
  font-size: 14px;
  flex-shrink: 0;
  position: relative;
  padding-left: 10px;
}

.info-label::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 14px;
  background-color: #1a56b3;
  opacity: 0.6;
  border-radius: 1.5px;
}

.info-value {
  flex: 1;
  color: #333;
  font-size: 14px;
  word-break: break-all;
  font-weight: 500;
}

/* 表单样式 */
.form-item {
  margin-bottom: 24px;
  position: relative;
}

.form-label {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  padding: 8px 0;
}

.label-text {
  font-size: 15px;
  color: #333;
  font-weight: 600;
  margin-left: 8px;
  letter-spacing: 0.3px;
}

.required {
  color: #ff4757;
  margin-left: 6px;
  font-weight: bold;
  font-size: 16px;
}

/* 单选按钮样式 */
.radio-label {
  display: flex;
  align-items: center;
  margin-right: 20px;
  margin-bottom: 12px;
  padding: 8px 12px;
  border-radius: 6px;
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
}

.radio-label:hover {
  background-color: #f0f7ff;
  border-color: #409eff;
}

.radio-label text {
  margin-left: 8px;
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

/* 备注输入框样式 */
.remark-textarea {
  width: 100%;
  min-height: 100px;
  padding: 12px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  font-size: 14px;
  color: #333;
  background-color: #fafbfc;
  resize: none;
  transition: all 0.3s ease;
  line-height: 1.5;
}

.remark-textarea:focus {
  border-color: #409eff;
  background-color: #fff;
  outline: none;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
}

/* 文件上传组件样式优化 */
:deep(.uni-file-picker__container) {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

:deep(.file-picker__box) {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

:deep(.file-picker__box:hover) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

:deep(.is-add) {
  border: 2px dashed #409eff;
  background-color: #f0f7ff;
  border-radius: 8px;
  transition: all 0.3s ease;
}

:deep(.is-add:hover) {
  border-color: #1a56b3;
  background-color: #e6f2ff;
}

/* 状态样式 */
.status-meter-reading {
  background: linear-gradient(135deg, #409eff 0%, #64b0ff 100%);
}
</style>
