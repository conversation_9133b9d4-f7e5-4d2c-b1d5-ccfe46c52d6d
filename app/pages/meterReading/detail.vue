<template>
  <view class="container">
    <!-- 顶部导航栏 -->
    <view class="header" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="header-content">
        <view class="back-icon" @click="goBack">
          <uni-icons type="left" size="18" color="#FFFFFF"></uni-icons>
        </view>
        <view class="header-title">电量统计详情</view>
        <view class="header-right">
          <view class="refresh-icon" @click="refreshData">
            <uni-icons type="refresh" size="18" color="#FFFFFF"></uni-icons>
          </view>
        </view>
      </view>
    </view>

    <!-- 内容区域 -->
    <scroll-view scroll-y class="content" :style="{ paddingTop: (statusBarHeight + 44) + 'px' }">
      <view v-if="meterReadingInfo" class="meter-reading-detail">
        <!-- 任务状态标识 -->
        <view class="status-banner" :class="getStatusClass(meterReadingInfo.status)">
          <view class="status-content">
            <view class="status-left">
              <view class="status-icon">
                <uni-icons :type="getStatusIcon(meterReadingInfo.status)" size="18" color="#FFFFFF"></uni-icons>
              </view>
              <text class="status-text">{{ getStatusLabel(meterReadingInfo.status) }}</text>
            </view>
            <!-- 操作按钮 -->
            <view class="action-buttons-top" v-if="canPerformAction && (meterReadingInfo.status === '0' || meterReadingInfo.status === '2')">
              <button class="action-button-top" type="primary" @click="handleReceive" v-if="meterReadingInfo.status === '0'">
                接收任务
              </button>
              <button class="action-button-top" type="success" @click="handleExecute" v-if="meterReadingInfo.status === '2'">
                执行任务
              </button>
            </view>
            <!-- 非负责人提示 -->
            <view class="action-tip" v-if="!canPerformAction && (meterReadingInfo.status === '0' || meterReadingInfo.status === '2')">
              <text class="action-tip-text">仅负责人可操作</text>
            </view>
          </view>
        </view>

        <!-- 基本信息卡片 -->
        <view class="detail-card">
          <view class="card-header">
            <uni-icons type="info-filled" size="16" color="#2979ff"></uni-icons>
            <text class="card-title">基本信息</text>
          </view>
          <view class="card-content">
            <view class="info-item">
              <text class="info-label">任务名称</text>
              <text class="info-value">{{ meterReadingInfo.meterReadingName }}</text>
            </view>
            <view class="info-item">
              <text class="info-label">所属电站</text>
              <text class="info-value">{{ meterReadingInfo.station ? meterReadingInfo.station.stationName : '未指定' }}</text>
            </view>
            <view class="info-item">
              <text class="info-label">负责人员</text>
              <text class="info-value">{{ meterReadingInfo.assignChargeUserName || '未指定' }}</text>
            </view>
            <view class="info-item">
              <text class="info-label">统计人员</text>
              <text class="info-value">{{ meterReadingInfo.assignUserName || '未指定' }}</text>
            </view>
            <view class="info-item">
              <text class="info-label">计划时间</text>
              <text class="info-value">{{ formatDate(meterReadingInfo.planStartTime) }} - {{ formatDate(meterReadingInfo.planEndTime) }}</text>
            </view>
            <view class="info-item">
              <text class="info-label">下发时间</text>
              <text class="info-value">{{ meterReadingInfo.createTime }}</text>
            </view>
            <view class="info-item" v-if="meterReadingInfo.meterReadingUserName">
              <text class="info-label">实际人员</text>
              <text class="info-value">{{ meterReadingInfo.meterReadingUserName }}</text>
            </view>
            <view class="info-item" v-if="meterReadingInfo.receiveTime">
              <text class="info-label">接收时间</text>
              <text class="info-value">{{ meterReadingInfo.receiveTime }}</text>
            </view>
            <view class="info-item" v-if="meterReadingInfo.submitTime">
              <text class="info-label">提交时间</text>
              <text class="info-value">{{ meterReadingInfo.submitTime }}</text>
            </view>
            <view class="info-item" v-if="meterReadingInfo.reviewTime">
              <text class="info-label">审核时间</text>
              <text class="info-value">{{ meterReadingInfo.reviewTime }}</text>
            </view>
            <view class="info-item" v-if="meterReadingInfo.reviewRemark">
              <text class="info-label">审核备注</text>
              <text class="info-value">{{ meterReadingInfo.reviewRemark }}</text>
            </view>
          </view>
        </view>

        <!-- 设备电量统计列表 -->
        <view class="detail-card" v-if="equipmentList.length > 0">
          <view class="card-header">
            <uni-icons type="list" size="16" color="#2979ff"></uni-icons>
            <text class="card-title">统计内容</text>
          </view>
          <view class="card-content">
            <view class="equipment-item" v-for="(equipment, index) in equipmentList" :key="index">
              <view class="equipment-header">
                <view class="equipment-name">{{ equipment.equipment.equipmentName }}</view>
                <view class="equipment-status" :class="getEquipmentStatusClass(equipment)">
                  <uni-icons :type="getEquipmentStatusIcon(equipment)" size="12" color="#FFFFFF"></uni-icons>
                  <text>{{ getEquipmentStatusText(equipment) }}</text>
                </view>
              </view>
              <!-- 电量统计详情 -->
              <view class="meter-reading-details" v-if="equipment.meterReadingTime">
                <!-- 电量统计信息紧凑显示 -->
                <view class="meter-reading-result-row">
                  <view class="result-main">
                    <view class="meter-reading-info-compact">
                      <text class="meter-reading-time-text">{{ equipment.meterReadingTime }}</text>
                      <view class="meter-reading-data" v-if="equipment.lastMonthReverseActive || equipment.totalReverseActive">
                        <text class="last-month-text" v-if="equipment.lastMonthReverseActive">上月反向有功: {{ equipment.lastMonthReverseActive }}kWh</text>
                        <text class="total-text" v-if="equipment.totalReverseActive">反向有功总: {{ equipment.totalReverseActive }}kWh</text>
                      </view>
                      <text class="meter-reading-remark-text" v-if="equipment.meterReadingRemark">{{ equipment.meterReadingRemark }}</text>
                    </view>
                  </view>
                </view>

                <!-- 电量统计照片 -->
                <view class="meter-reading-photo-row" v-if="equipment.photos">
                  <view class="photo-row-content">
                    <text class="photo-row-title">统计照片</text>
                    <view class="photo-row-images">
                      <view
                        v-for="(photo, photoIndex) in getPhotoList(equipment.photos).slice(0, 3)"
                        :key="photoIndex"
                        class="photo-container"
                        @click="previewPhotos(equipment.photos, photoIndex)"
                      >
                        <image
                          :src="photo"
                          mode="aspectFit"
                          class="meter-reading-photo-compact"
                        />
                        <view
                          v-if="photoIndex === 2 && getPhotoList(equipment.photos).length > 3"
                          class="photo-more-indicator"
                        >
                          +{{ getPhotoList(equipment.photos).length - 3 }}
                        </view>
                      </view>
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 空设备状态 -->
        <view class="detail-card" v-if="!loading && meterReadingInfo && equipmentList.length === 0">
          <view class="card-header">
            <uni-icons type="list" size="16" color="#2979ff"></uni-icons>
            <text class="card-title">设备电量统计情况</text>
          </view>
          <view class="card-content">
            <view class="empty-equipment">
              <uni-icons type="info-filled" size="48" color="#cccccc"></uni-icons>
              <text>暂无关联设备</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 使用LoadingContainer组件 -->
      <loading-container
        v-if="loading || !meterReadingInfo"
        :loading="loading"
        :error="!loading && !meterReadingInfo"
        loadingText="正在加载电量统计详情..."
        errorText="加载电量统计详情失败"
        :showRetry="true"
        @retry="getMeterReadingDetail"
        height="300"
      />
    </scroll-view>
  </view>
</template>

<script>
import { getMeterReading, getMeterReadingEquipments } from '@/api/system/meterReading';
import { getDictData } from '@/api/system/dictData';
import LoadingContainer from '@/components/LoadingContainer'
import { getImageUrl, getImageList } from '@/utils/imageUtils';

export default {
  name: 'MeterReadingDetail',
  components: {
    LoadingContainer
  },
  data() {
    return {
      statusBarHeight: 0,
      meterReadingId: null,
      meterReadingInfo: null,
      equipmentList: [],
      loading: false,
      userId: null,
      dictData: {}
    }
  },
  computed: {
    // 判断当前用户是否可以执行操作
    canPerformAction() {
      if (!this.meterReadingInfo || !this.userId) return false;

      // 检查是否为负责人
      const assignChargeUsers = this.meterReadingInfo.assignChargeUser ? this.meterReadingInfo.assignChargeUser.split(',') : [];
      return assignChargeUsers.includes(String(this.userId));
    }
  },
  onLoad(options) {
    // 设置状态栏高度
    this.statusBarHeight = uni.getSystemInfoSync().statusBarHeight;

    if (options.id) {
      this.meterReadingId = options.id;
    }

    // 获取当前登录用户ID
    this.userId = this.$store.state.user.userId;

    // 获取字典数据
    this.getDicts();
  },
  methods: {
    // 获取字典数据
    async getDicts() {
      try {
        const response = await getDictData();
        // 使用字典插件解析字典数据
        this.dictData = this.$dict.parseDictData(response.data);
        // 获取电量统计详情
        this.getMeterReadingDetail();
      } catch (error) {
        console.error('获取字典数据失败:', error);
        uni.showToast({
          title: '获取字典数据失败',
          icon: 'none'
        });
        this.loading = false;
      }
    },

    // 获取电量统计详情
    getMeterReadingDetail() {
      if (!this.meterReadingId) {
        uni.showToast({
          title: '缺少电量统计任务ID',
          icon: 'none'
        });
        return;
      }

      this.loading = true;

      Promise.all([
        getMeterReading(this.meterReadingId),
        getMeterReadingEquipments(this.meterReadingId)
      ]).then(([meterReadingResponse, equipmentResponse]) => {
        this.meterReadingInfo = meterReadingResponse.data;
        this.equipmentList = equipmentResponse.data || [];
        this.loading = false;
      }).catch(error => {
        console.error('获取电量统计详情失败:', error);
        uni.showToast({
          title: '获取电量统计详情失败',
          icon: 'none'
        });
        this.loading = false;
      });
    },

    // 刷新数据
    refreshData() {
      this.getMeterReadingDetail();
    },

    // 返回上一页
    goBack() {
      uni.navigateBack();
    },

    // 接收任务
    handleReceive() {
      uni.navigateTo({
        url: `/pages/meterReading/receive?id=${this.meterReadingId}`
      });
    },

    // 执行任务
    handleExecute() {
      uni.navigateTo({
        url: `/pages/meterReading/execute?id=${this.meterReadingId}`
      });
    },

    // 获取状态标签
    getStatusLabel(status) {
      if (!status) return '未知';
      return this.$dict.getDictLabel('meter_reading_status', status, '未知');
    },

    // 获取状态样式类
    getStatusClass(status) {
      const statusClassMap = {
        '0': 'status-pending',    // 待处理
        '2': 'status-received',   // 进行中
        '4': 'status-submitted',  // 已完成
        '6': 'status-reviewed'    // 已审核
      };
      return statusClassMap[status] || 'status-unknown';
    },

    // 获取状态图标
    getStatusIcon(status) {
      const statusIconMap = {
        '0': 'notification-filled',
        '2': 'staff-filled',
        '4': 'checkbox-filled',
        '6': 'medal-filled'
      };
      return statusIconMap[status] || 'info-filled';
    },

    // 获取设备类型标签
    getEquipmentTypeLabel(type) {
      if (!type || !this.dictData.equipment_type) return '未知类型';
      return this.$dict.getDictLabel('equipment_type', type, '未知类型');
    },



    // 判断设备是否已开始电量统计
    hasStartedMeterReading(equipment) {
      // 检查是否有任何电量统计信息已经开始填写
      if (equipment.lastMonthReverseActive) return true;
      if (equipment.totalReverseActive) return true;
      if (equipment.photos) return true;
      if (equipment.meterReadingRemark && equipment.meterReadingRemark.trim() !== '') return true;
      return false;
    },

    // 判断设备电量统计内容是否完整
    isMeterReadingContentComplete(equipment) {
      // 判断设备是否电量统计完成的依据是：lastMonthReverseActive、totalReverseActive、photos是否都填写完整
      return !!(equipment.lastMonthReverseActive &&
                equipment.totalReverseActive &&
                equipment.photos);
    },

    // 获取设备电量统计状态
    getEquipmentStatusClass(equipment) {
      if (!this.hasStartedMeterReading(equipment)) {
        return 'equipment-status-pending';
      } else if (this.isMeterReadingContentComplete(equipment)) {
        return 'equipment-status-completed';
      } else {
        return 'equipment-status-incomplete';
      }
    },

    // 获取设备状态图标
    getEquipmentStatusIcon(equipment) {
      if (!this.hasStartedMeterReading(equipment)) {
        return 'notification-filled';
      } else if (this.isMeterReadingContentComplete(equipment)) {
        return 'checkbox-filled';
      } else {
        return 'info-filled';
      }
    },

    // 获取设备状态文本
    getEquipmentStatusText(equipment) {
      if (!this.hasStartedMeterReading(equipment)) {
        return '待统计';
      } else if (this.isMeterReadingContentComplete(equipment)) {
        return '已完成';
      } else {
        return '未完整';
      }
    },

    // 格式化日期
    formatDate(dateString) {
      if (!dateString) return '未设置';
      const date = new Date(dateString);
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
    },

    // 获取图片URL
    getImageUrl(path) {
      return getImageUrl(path);
    },

    // 获取电量统计照片列表
    getPhotoList(photos) {
      if (!photos) return [];
      return getImageList(photos);
    },

    // 预览电量统计照片
    previewPhotos(photos, currentIndex) {
      if (!photos) return;
      const urls = getImageList(photos);
      uni.previewImage({
        urls: urls,
        current: currentIndex
      });
    },

    // 预览图片（保留兼容性）
    previewImage(url) {
      uni.previewImage({
        urls: [url],
        current: url
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #ffffff;
}

/* 顶部导航栏 */
.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: linear-gradient(135deg, #1a56b3 0%, #3a7bd5 100%);
  padding: 10px 15px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.back-icon {
  padding: 5px;
}

.header-title {
  color: #ffffff;
  font-size: 18px;
  font-weight: bold;
}

/* 内容区域 */
.content {
  flex: 1;
}

.meter-reading-detail {
  padding: 0;
}

/* 状态横幅 */
.status-banner {
  padding: 15px 16px;
  color: #ffffff;
  position: relative;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.status-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-left {
  display: flex;
  align-items: center;
}

.status-icon {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
}

.status-text {
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0.5px;
}

/* 上方操作按钮 */
.action-buttons-top {
  display: flex;
  gap: 10px;
}

.action-button-top {
  background-color: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.4);
  color: #ffffff;
  font-size: 13px;
  padding: 4px 12px;
  border-radius: 15px;
  height: auto;
  line-height: 1.5;
  font-weight: normal;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.action-button-top:active {
  transform: scale(0.95);
  background-color: rgba(255, 255, 255, 0.3);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* 非负责人提示 */
.action-tip {
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 15px;
  padding: 4px 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.action-tip-text {
  color: #ffffff;
  font-size: 13px;
  opacity: 0.9;
}

/* 分区样式 */
.detail-card {
  background-color: #ffffff;
  margin-bottom: 10px;
  position: relative;
}

.card-header {
  display: flex;
  align-items: center;
  padding: 15px 16px 10px;
  position: relative;
}

.card-title {
  font-size: 16px;
  font-weight: bold;
  margin-left: 8px;
  color: #1a56b3;
}

.card-content {
  padding: 5px 16px 15px;
}

/* 设备列表卡片的内容区域特殊处理 */
.detail-card:nth-child(3) .card-content {
  padding: 8px 16px;
}

/* 信息项样式 */
.info-item {
  display: flex;
  margin-bottom: 10px;
  align-items: center;
  position: relative;
  border-bottom: 1px solid #f9f9f9;
  padding-bottom: 10px;
}

.info-item:last-child {
  margin-bottom: 0;
  border-bottom: none;
  padding-bottom: 0;
}

.info-label {
  width: 90px;
  color: #666;
  font-size: 14px;
  flex-shrink: 0;
  position: relative;
  padding-left: 10px;
}

.info-label::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 14px;
  background-color: #1a56b3;
  opacity: 0.6;
  border-radius: 1.5px;
}

.info-value {
  flex: 1;
  color: #333;
  font-size: 14px;
  word-break: break-all;
  font-weight: 500;
}

/* 设备列表样式 */
.equipment-item {
  border-bottom: 1px solid #f0f0f0;
  position: relative;
  background-color: #ffffff;
  margin-bottom: 10px;
}

.equipment-item:last-child {
  margin-bottom: 0;
  border-bottom: none;
}

.equipment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f5f5f5;
}

.equipment-name {
  font-size: 15px;
  font-weight: bold;
  color: #333;
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  position: relative;
  padding-left: 12px;
}

.equipment-name::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 16px;
  background-color: #1a56b3;
  border-radius: 2px;
}

.equipment-status {
  display: flex;
  align-items: center;
  padding: 2px 6px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
  color: #FFFFFF;
}

.equipment-status text {
  margin-left: 4px;
}

.equipment-info {
  margin-bottom: 8px;
}

.equipment-code {
  color: #666666;
  font-size: 12px;
}

/* 电量统计详情 */
.meter-reading-details {
  padding: 8px 0;
  margin-top: 8px;
}

/* 电量统计结果行样式 */
.meter-reading-result-row {
  width: 100%;
  margin-bottom: 8px;
  display: flex;
  align-items: flex-start;
  padding: 0;
}

.result-main {
  display: flex;
  flex-direction: column;
  flex: 1;
  gap: 2px;
  min-width: 0;
}

.meter-reading-info-compact {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.meter-reading-time-text {
  font-size: 14px;
  color: #1a56b3;
  font-weight: 600;
  line-height: 1.4;
  margin-bottom: 6px;
  position: relative;
  padding-left: 12px;
}
.meter-reading-data {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  margin-top: 4px;
  margin-left: 12px;
}

.last-month-text {
  font-size: 12px;
  color: #1a56b3;
  background-color: #f0f7ff;
  padding: 2px 6px;
  border-radius: 4px;
  line-height: 1.3;
  font-weight: 500;
}

.total-text {
  font-size: 12px;
  color: #d97706;
  background-color: #fff8f0;
  padding: 2px 6px;
  border-radius: 4px;
  line-height: 1.3;
  font-weight: 500;
}

.meter-reading-remark-text {
  font-size: 12px;
  color: #666;
  line-height: 1.4;
  word-wrap: break-word;
  margin-top: 6px;
  margin-left: 12px;
  padding-left: 8px;
  border-left: 2px solid #e0e0e0;
  font-style: italic;
}

/* 电量统计照片行样式 */
.meter-reading-photo-row {
  width: 100%;
  margin-top: 6px;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  padding: 0;
}

.meter-reading-photo-row:last-child {
  margin-bottom: 0;
}

.photo-row-content {
  display: flex;
  align-items: center;
  width: 100%;
  gap: 12px;
}

.photo-row-title {
  font-size: 12px;
  font-weight: 500;
  color: #000000;
  min-width: 50px;
  flex-shrink: 0;
  position: relative;
  padding-left: 12px;
}

.photo-row-images {
  display: flex;
  gap: 8px;
  flex: 1;
  flex-wrap: wrap;
}

.photo-container {
  position: relative;
  cursor: pointer;
}

.meter-reading-photo-compact {
  width: 60px;
  height: 45px;
  border-radius: 4px;
  border: 1px solid #e0e0e0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
  transition: all 0.2s ease;
  cursor: pointer;
  position: relative;
}

.photo-more-indicator {
  position: absolute;
  top: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  font-size: 10px;
  padding: 2px 4px;
  border-radius: 0 4px 0 4px;
  line-height: 1;
}

/* 设备状态颜色 */
.equipment-status-pending {
  background-color: #f7b84b;
}

.equipment-status-incomplete {
  background-color: #3498db;
}

.equipment-status-completed {
  background-color: #27ae60;
}

/* 状态标签 */
.status-tag {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 4px;
  color: #ffffff;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
}

.status-pending {
  background: linear-gradient(135deg, #e6a23c 0%, #f0b95a 100%);
}

.status-received {
  background: linear-gradient(135deg, #1a56b3 0%, #3a7bd5 100%);
}

.status-submitted {
  background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%);
}

.status-reviewed {
  background: linear-gradient(135deg, #606266 0%, #909399 100%);
}

.status-unknown {
  background: linear-gradient(135deg, #f56c6c 0%, #f78989 100%);
}

.empty-equipment {
  text-align: center;
  padding: 20px 0;
  color: #999;
  font-size: 14px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 10px;
}
</style>
