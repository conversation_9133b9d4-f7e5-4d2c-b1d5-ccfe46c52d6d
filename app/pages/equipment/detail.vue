<template>
  <view class="container">
    <!-- 顶部导航栏 -->
    <view class="header">
      <view class="header-content">
        <view class="back-icon" @click="goBack">
          <uni-icons type="back" size="22" color="#ffffff"></uni-icons>
        </view>
        <text class="header-title">设备详情</text>
        <view class="header-right"></view>
      </view>
    </view>

    <!-- 内容区域 -->
    <scroll-view scroll-y class="content-scroll">
      <view v-if="equipmentInfo">
        <!-- 基本信息卡片 -->
        <view class="detail-card">
          <view class="card-header">
            <uni-icons type="info-filled" size="16" color="#2979ff"></uni-icons>
            <text class="card-title">基本信息</text>
          </view>
          <view class="card-content">
            <view class="info-item">
              <text class="info-label">设备名称</text>
              <text class="info-value">{{ equipmentInfo.equipmentName }}</text>
            </view>
            <view class="info-item">
              <text class="info-label">设备编号</text>
              <text class="info-value">{{ equipmentInfo.equipmentCode }}</text>
            </view>
            <view class="info-item">
              <text class="info-label">设备类型</text>
              <text class="info-value">{{ getTypeLabel(equipmentInfo.equipmentType) }}</text>
            </view>
            <view class="info-item">
              <text class="info-label">设备型号</text>
              <text class="info-value">{{ equipmentInfo.model }}</text>
            </view>
            <view class="info-item">
              <text class="info-label">生产厂家</text>
              <text class="info-value">{{ equipmentInfo.manufacturer || '未知' }}</text>
            </view>
            <view class="info-item">
              <text class="info-label">设备数量</text>
              <text class="info-value">{{ equipmentInfo.quantity }}</text>
            </view>
            <view class="info-item">
              <text class="info-label">所属电站</text>
              <text class="info-value">{{ equipmentInfo.station ? equipmentInfo.station.stationName : '闲置设备' }}</text>
            </view>
            <view class="info-item">
              <text class="info-label">设备状态</text>
              <text class="info-value" :class="{'status-normal-text': equipmentInfo.status === '0', 'status-error-text': equipmentInfo.status === '1'}">{{ getStatusLabel(equipmentInfo.status) }}</text>
            </view>
            <view class="info-item">
              <text class="info-label">创建时间</text>
              <text class="info-value">{{ equipmentInfo.createTime }}</text>
            </view>
            <view class="info-item" v-if="equipmentInfo.remark">
              <text class="info-label">备注</text>
              <text class="info-value">{{ equipmentInfo.remark }}</text>
            </view>
          </view>
        </view>

        <!-- 巡检记录卡片 -->
        <view class="detail-card">
          <view class="card-header">
            <uni-icons type="calendar" size="16" color="#2979ff"></uni-icons>
            <text class="card-title">巡检记录</text>
          </view>
          <view class="card-content">
            <view v-if="inspectionHistoryLoading" class="loading-container">
              <text class="loading-text">加载中...</text>
            </view>
            <view v-else-if="inspectionHistory.length > 0" class="inspection-history-list">
              <view
                class="inspection-history-item"
                v-for="(item, index) in inspectionHistory"
                :key="index"
              >
                <view class="history-item-header" @click="goToInspectionDetail(item)">
                  <text class="inspection-name">{{ item.inspection.inspectionName }}</text>
                  <view class="status-tag" :class="getInspectionStatusClass(item.inspection.status)">
                    <text class="status-text">{{ getInspectionStatusLabel(item.inspection.status) }}</text>
                  </view>
                </view>
                <view class="history-item-info">
                  <text class="inspection-time">{{ formatTime(item.inspectionTime) }}</text>
                </view>

                <!-- 检查项结果展示 -->
                <view class="inspection-items" v-if="getInspectionItems(item).length > 0">
                  <view class="inspection-item" v-for="(checkItem, checkIndex) in getInspectionItems(item)" :key="checkIndex">
                    <view class="inspection-item-row">
                      <text class="inspection-item-index">{{ checkIndex + 1 }}</text>
                      <text class="inspection-item-content">{{ checkItem.content }}</text>
                      <view class="inspection-result">
                        <view class="result-tag" :class="getResultClass(checkItem)">
                          <text class="result-text">{{ getInspectionResult(checkItem) }}</text>
                        </view>
                      </view>
                    </view>
                    <!-- 图片展示 -->
                    <view class="result-photos" v-if="checkItem.photos && checkItem.photos.length > 0">
                      <image
                        v-for="(photo, photoIndex) in checkItem.photos.slice(0, 3)"
                        :key="photoIndex"
                        :src="getImageUrl(photo)"
                        class="result-photo"
                        mode="aspectFill"
                        @click="previewImage(checkItem.photos, photoIndex)"
                      />
                      <view v-if="checkItem.photos.length > 3" class="more-photos">
                        <text>+{{ checkItem.photos.length - 3 }}</text>
                      </view>
                    </view>
                  </view>
                </view>
              </view>
            </view>
            <view v-else class="empty-state">
              <uni-icons type="info" size="24" color="#cccccc"></uni-icons>
              <text class="empty-text">暂无巡检记录</text>
            </view>
          </view>
        </view>
      </view>

      <loading-container
        v-else
        :loading="loading"
        :error="!loading && !equipmentInfo"
        loadingText="正在加载设备详情..."
        errorText="获取设备详情失败"
        @retry="refreshData"
      />
    </scroll-view>
  </view>
</template>

<script>
import { getDictData } from '@/api/system/dictData'
import { getEquipment, getEquipmentInspectionHistory } from '@/api/system/equipment'
import LoadingContainer from '@/components/LoadingContainer'
import { getImageUrl } from '@/utils/imageUtils'
import { decodeInspectionItems } from '@/utils/htmlDecoder'

export default {
  name: "detail",
  components: {
    LoadingContainer
  },
  data() {
    return {
      equipmentId: null,
      equipmentInfo: null,
      stationName: '',
      loading: true,
      attachmentList: [],
      // 字典数据
      dictData: {},
      // 巡检记录相关
      inspectionHistory: [],
      inspectionHistoryLoading: false
    }
  },
  onLoad(options) {
    // 设置状态栏高度
    this.statusBarHeight = uni.getSystemInfoSync().statusBarHeight;

    if (options.id) {
      this.equipmentId = options.id;
      this.getDicts();
    } else {
      uni.showToast({
        title: '参数错误',
        icon: 'none'
      });
      setTimeout(() => {
        uni.navigateBack();
      }, 1500);
    }
  },
  methods: {
    // 获取字典数据
    async getDicts() {
      try {
        const response = await getDictData();
        this.dictData = this.$dict.parseDictData(response.data);
        this.getEquipmentDetail();
      } catch (error) {
        console.error('获取字典数据失败:', error);
        uni.showToast({
          title: '获取字典数据失败',
          icon: 'none'
        });
        this.loading = false;
      }
    },

    // 获取设备详情
    async getEquipmentDetail() {
      try {
        const response = await getEquipment(this.equipmentId);
        this.equipmentInfo = response.data;

        // 如果有附件，处理附件列表
        if (this.equipmentInfo.attachments) {
          this.attachmentList = this.equipmentInfo.attachments.split(',');
        }

        // 获取巡检记录
        await this.getInspectionHistory();
      } catch (error) {
        console.error('获取设备详情失败:', error);
        uni.showToast({
          title: '获取设备详情失败',
          icon: 'none'
        });
      } finally {
        this.loading = false;
      }
    },

    // 获取巡检记录
    async getInspectionHistory() {
      this.inspectionHistoryLoading = true;
      try {

        const response = await getEquipmentInspectionHistory(this.equipmentId);

        // 过滤出已完成和已审核的任务，并按时间倒序排列
        this.inspectionHistory = (response.data || [])
            .filter(item =>
            item.inspection.status === '4' || item.inspection.status === '6'
          )
          .sort((a, b) => {
            const timeA = new Date(b.inspectionTime || b.inspection.createTime);
            const timeB = new Date(a.inspectionTime || a.inspection.createTime);
            return timeA - timeB;
          });

      } catch (error) {
        console.error('获取巡检记录失败:', error);
        this.inspectionHistory = [];
      } finally {
        this.inspectionHistoryLoading = false;
      }
    },

    // 获取类型标签
    getTypeLabel(value) {
      return this.$dict.getDictLabel('equipment_type', value, '未知');
    },

    // 获取状态标签
    getStatusLabel(value) {
      return this.$dict.getDictLabel('equipment_status', value, '未知');
    },

    // 获取文件名
    getFileName(url) {
      if (!url) return '';
      const parts = url.split('/');
      return parts[parts.length - 1];
    },

    // 预览文件
    previewFile(url) {
      // 根据文件类型决定预览方式
      const fileName = this.getFileName(url);
      const fileExt = fileName.split('.').pop().toLowerCase();

      // 图片类型
      const imageExts = ['jpg', 'jpeg', 'png', 'gif', 'bmp'];
      if (imageExts.includes(fileExt)) {
        uni.previewImage({
          urls: [url],
          current: url
        });
        return;
      }

      // 其他类型文件下载
      uni.showLoading({
        title: '正在打开文件...'
      });

      uni.downloadFile({
        url: url,
        success: (res) => {
          uni.hideLoading();
          if (res.statusCode === 200) {
            uni.openDocument({
              filePath: res.tempFilePath,
              success: () => {
                console.log('打开文档成功');
              },
              fail: (err) => {
                console.error('打开文档失败', err);
                uni.showToast({
                  title: '打开文档失败',
                  icon: 'none'
                });
              }
            });
          }
        },
        fail: () => {
          uni.hideLoading();
          uni.showToast({
            title: '文件下载失败',
            icon: 'none'
          });
        }
      });
    },

    // 返回上一页
    goBack() {
      uni.navigateBack();
    },

    // 跳转到巡检详情
    goToInspectionDetail(item) {
      uni.navigateTo({
        url: `/pages/inspection/detail?id=${item.inspection.inspectionId}`
      });
    },

    // 获取巡检状态样式类
    getInspectionStatusClass(status) {
      switch (status) {
        case '0': return 'status-pending';
        case '2': return 'status-received';
        case '4': return 'status-completed';
        case '6': return 'status-reviewed';
        default: return 'status-pending';
      }
    },

    // 获取巡检状态标签
    getInspectionStatusLabel(status) {
      switch (status) {
        case '0': return '待接收';
        case '2': return '进行中';
        case '4': return '已完成';
        case '6': return '已审核';
        default: return '未知';
      }
    },

    // 格式化时间
    formatTime(timeStr) {
      if (!timeStr) return '';
      const date = new Date(timeStr);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },

    // 刷新数据
    refreshData() {
      this.loading = true;
      this.getDicts();
      uni.showToast({
        title: '刷新中',
        icon: 'loading',
        duration: 500
      });
    },

    // 获取检查项
    getInspectionItems(item) {
      if (!item.inspectionContent) return [];

      try {
        // 尝试解析JSON字符串
        const inspectionItems = JSON.parse(item.inspectionContent);
        if (Array.isArray(inspectionItems)) {
          // 解码HTML实体
          return decodeInspectionItems(inspectionItems);
        }
        return [];
      } catch (e) {
        console.error('解析检查项失败:', e);
        return [];
      }
    },

    // 判断检查项是否有结果
    hasInspectionResult(checkItem) {
      return checkItem.result !== undefined && checkItem.result !== null && checkItem.result !== '';
    },

    // 获取检查结果显示文本
    getInspectionResult(checkItem) {
      if (!this.hasInspectionResult(checkItem)) return '';

      if (checkItem.type === 'radio' || !checkItem.type) {
        // radio类型，根据result下标获取选项文本
        const resultIndex = parseInt(checkItem.result);
        if (checkItem.options && checkItem.options[resultIndex]) {
          let resultText = checkItem.options[resultIndex];
          // 如果有备注，添加备注信息
          if (checkItem.remark) {
            resultText += `（${checkItem.remark}）`;
          }
          return resultText;
        }
        return checkItem.result;
      } else if (checkItem.type === 'text') {
        // text类型，直接返回文本值
        return checkItem.result;
      }

      return checkItem.result;
    },

    // 获取结果样式类
    getResultClass(checkItem) {
      if (!this.hasInspectionResult(checkItem)) return '';

      if (checkItem.type === 'radio' || !checkItem.type) {
        const resultIndex = parseInt(checkItem.result);
        if (resultIndex === 0) {
          return 'result-qualified'; // 合格
        } else {
          return 'result-unqualified'; // 不合格
        }
      }
      return 'result-normal';
    },

    // 获取图片URL
    getImageUrl(path) {
      return getImageUrl(path);
    },

    // 预览图片
    previewImage(photos, current) {
      const urls = photos.map(photo => getImageUrl(photo));
      uni.previewImage({
        urls: urls,
        current: current
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}

/* 顶部导航栏 */
.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background-color: #2979ff;
  padding: 10px 15px;
  padding-top: var(--status-bar-height);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.back-icon {
  padding: 5px;
}

.header-title {
  color: #ffffff;
  font-size: 18px;
  font-weight: bold;
}

.header-right {
  width: 22px;
}

/* 内容区域 */
.content-scroll {
  flex: 1;
  margin-top: calc(var(--status-bar-height) + 44px);
  height: calc(100vh - (var(--status-bar-height) + 44px));
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
}

/* 详细信息卡片 */
.detail-card {
  background-color: #ffffff;
  margin-bottom: 10px;
  position: relative;
}

.card-header {
  display: flex;
  align-items: center;
  padding: 10px 16px;
  position: relative;
}

.card-title {
  font-size: 15px;
  font-weight: bold;
  margin-left: 6px;
  color: #2979ff;
}

.card-content {
  padding: 2px 16px 10px;
}

/* 信息项样式 */
.info-item {
  display: flex;
  margin-bottom: 8px;
  align-items: center;
  position: relative;
  border-bottom: 1px solid #f9f9f9;
  padding-bottom: 8px;
}

.info-item:last-child {
  margin-bottom: 0;
  border-bottom: none;
  padding-bottom: 0;
}

.info-label {
  width: 80px;
  color: #666;
  font-size: 13px;
  flex-shrink: 0;
  position: relative;
  padding-left: 8px;
}

.info-label::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 2px;
  height: 12px;
  background-color: #2979ff;
  opacity: 0.6;
  border-radius: 1px;
}

.info-value {
  flex: 1;
  color: #333;
  font-size: 13px;
  word-break: break-all;
  font-weight: 500;
}

.status-normal-text {
  color: #67c23a;
}

.status-error-text {
  color: #e6a23c;
}

/* 巡检记录样式 */
.inspection-history-list {
  margin-top: 5px;
}

.inspection-history-item {
  padding: 12px 0;
  border-bottom: 1px solid #f5f5f5;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.inspection-history-item:last-child {
  border-bottom: none;
}

.inspection-history-item:active {
  background-color: #f8f9fa;
}

.history-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}

.inspection-name {
  font-size: 14px;
  color: #333;
  font-weight: 500;
  flex: 1;
  margin-right: 10px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.status-tag {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  flex-shrink: 0;
  color: #ffffff;
}

.status-tag.status-pending {
  background: linear-gradient(135deg, #e6a23c 0%, #f0b95a 100%);
}

.status-tag.status-received {
  background: linear-gradient(135deg, #1a56b3 0%, #3a7bd5 100%);
}

.status-tag.status-completed {
  background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%);
}

.status-tag.status-reviewed {
  background: linear-gradient(135deg, #606266 0%, #909399 100%);
}

.status-text {
  font-size: 12px;
}

.history-item-info {
  display: flex;
  align-items: center;
}

.inspection-time {
  font-size: 12px;
  color: #999;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
}

.empty-text {
  font-size: 14px;
  color: #999;
  margin-top: 8px;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
}

.loading-text {
  font-size: 14px;
  color: #999;
}

/* 检查项样式 */
.inspection-items {
  margin-top: 10px;
  padding-top: 10px;
  border-top: 1px solid #f0f0f0;
}

.inspection-item {
  margin-bottom: 12px;
}

.inspection-item:last-child {
  margin-bottom: 0;
}

.inspection-item-row {
  display: flex;
  align-items: flex-start;
  margin-bottom: 6px;
}

.inspection-item-index {
  width: 20px;
  height: 20px;
  background-color: #f5f5f5;
  color: #666;
  font-size: 12px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
  flex-shrink: 0;
}

.inspection-item-content {
  flex: 1;
  font-size: 13px;
  color: #333;
  line-height: 1.4;
  margin-right: 8px;
}

.inspection-result {
  flex-shrink: 0;
}

.result-tag {
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 11px;
  color: #ffffff;
}

.result-qualified {
  background-color: #67c23a;
}

.result-unqualified {
  background-color: #f56c6c;
}

.result-normal {
  background-color: #909399;
}

.result-text {
  font-size: 11px;
}

.result-photos {
  display: flex;
  align-items: center;
  margin-left: 28px;
  margin-top: 4px;
  gap: 4px;
}

.result-photo {
  width: 30px;
  height: 30px;
  border-radius: 4px;
  border: 1px solid #e0e0e0;
}

.more-photos {
  width: 30px;
  height: 30px;
  background-color: #f5f5f5;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #e0e0e0;
}

.more-photos text {
  font-size: 10px;
  color: #666;
}
</style>