<template>
  <view class="container">
    <!-- 顶部导航栏 -->
    <view class="header">
      <view class="header-content">
        <view class="back-icon" @click="goBack">
          <uni-icons type="back" size="22" color="#ffffff"></uni-icons>
        </view>
        <text class="header-title">设备列表</text>
        <view class="header-right"></view>
      </view>
    </view>

    <!-- 搜索栏 -->
    <view class="search-container">
      <view class="search-box">
        <uni-icons type="search" size="18" color="#999999"></uni-icons>
        <input class="search-input" type="text" v-model="searchKeyword" placeholder="搜索设备名称" @input="handleSearch" />
        <uni-icons v-if="searchKeyword" type="clear" size="18" color="#999999" @click="clearSearch"></uni-icons>
      </view>
      <view class="filter-box">
        <uni-data-select v-model="selectedTypeValue" :localdata="equipmentTypeOptions" placeholder="设备类型"
          @change="handleTypeChange" class="filter-select"></uni-data-select>
        <uni-data-select v-model="selectedStatusValue" :localdata="equipmentStatusOptions" placeholder="设备状态"
          @change="handleStatusChange" class="filter-select"></uni-data-select>
      </view>
    </view>

    <!-- 设备列表 -->
    <scroll-view scroll-y class="content-scroll" @scrolltolower="loadMore">
      <view v-if="equipmentList.length > 0">
        <view class="equipment-item" v-for="(item, index) in equipmentList" :key="index" @click="goToDetail(item)">
          <view class="equipment-header">
            <view class="equipment-title">
              <view class="name-container">
                <uni-icons type="gear-filled" size="16" color="#2979ff" class="equipment-icon"></uni-icons>
                <text class="equipment-name">{{ item.equipmentName }}</text>
              </view>
              <view class="equipment-tag"
                :class="{ 'status-normal': item.status === '0', 'status-error': item.status === '1' }">{{
                  getStatusLabel(item.status) }}</view>
            </view>
            <text class="equipment-code">编号: {{ item.equipmentCode }}</text>
          </view>
          <view class="equipment-info">
            <view class="info-item">
              <text class="info-label">设备类型:</text>
              <text class="info-value">{{ getTypeLabel(item.equipmentType) }}</text>
            </view>
            <view class="info-item">
              <text class="info-label">生产厂家:</text>
              <text class="info-value">{{ item.manufacturer || '暂无' }}</text>
            </view>
            <view class="info-item">
              <text class="info-label">设备型号:</text>
              <text class="info-value">{{ item.model }}</text>
            </view>
            <view class="info-item">
              <text class="info-label">设备数量:</text>
              <text class="info-value">{{ item.quantity || '暂无' }}</text>
            </view>
          </view>
        </view>

        <!-- 加载更多 -->
        <view class="load-more" v-if="hasMore">
          <text>加载更多...</text>
        </view>
        <view class="load-more" v-else>
          <text>没有更多数据了</text>
        </view>
      </view>

      <!-- 空状态 -->
      <view v-else-if="!loading" class="empty-container">
        <uni-icons type="info" size="64" color="#cccccc"></uni-icons>
        <text class="empty-text">暂无设备信息</text>
      </view>

      <!-- 加载中 -->
      <view v-if="loading" class="loading-container">
        <text>加载中...</text>
      </view>

    </scroll-view>
  </view>
</template>

<script>
import { getDictData } from '@/api/system/dictData'
import { listEquipment } from '@/api/system/equipment'

export default {
  name: "list",
  data() {
    return {
      stationId: null,
      searchKeyword: '',
      equipmentList: [],
      loading: true,
      pageNum: 1,
      pageSize: 10,
      total: 0,
      hasMore: true,
      // 设备类型选项
      equipmentTypeOptions: [],
      selectedTypeValue: '',
      // 设备状态选项
      equipmentStatusOptions: [],
      selectedStatusValue: '',
      // 字典数据
      dictData: {}
    }
  },
  onLoad(options) {
    // 设置状态栏高度
    this.statusBarHeight = uni.getSystemInfoSync().statusBarHeight;

    if (options.stationId) {
      this.stationId = options.stationId;
    }

    this.getDicts();
  },
  methods: {
    // 获取字典数据
    async getDicts() {
      try {
        const response = await getDictData();
        this.dictData = this.$dict.parseDictData(response.data);

        // 设置设备类型选项
        this.equipmentTypeOptions = [
          { text: '全部类型', value: '' },
          ...this.dictData.equipment_type.map(item => ({
            text: item.label,
            value: item.value
          }))
        ];

        // 设置设备状态选项
        this.equipmentStatusOptions = [
          { text: '全部状态', value: '' },
          ...this.dictData.equipment_status.map(item => ({
            text: item.label,
            value: item.value
          }))
        ];

        this.getEquipmentList();
      } catch (error) {
        console.error('获取字典数据失败:', error);
        uni.showToast({
          title: '获取字典数据失败',
          icon: 'none'
        });
        this.loading = false;
      }
    },

    // 获取设备列表
    async getEquipmentList(loadMore = false) {
      if (!loadMore) {
        this.loading = true;
        this.pageNum = 1;
      }

      try {
        const params = {
          pageNum: this.pageNum,
          pageSize: this.pageSize,
          equipmentName: this.searchKeyword,
          equipmentType: this.selectedTypeValue,
          status: this.selectedStatusValue
        };

        if (this.stationId) {
          params.stationId = this.stationId;
        }

        const response = await listEquipment(params);

        if (loadMore) {
          this.equipmentList = [...this.equipmentList, ...response.rows];
        } else {
          this.equipmentList = response.rows;
        }

        this.total = response.total;
        this.hasMore = this.equipmentList.length < this.total;
      } catch (error) {
        console.error('获取设备列表失败:', error);
        uni.showToast({
          title: '获取设备列表失败',
          icon: 'none'
        });
      } finally {
        this.loading = false;
      }
    },

    // 加载更多
    loadMore() {
      if (this.hasMore && !this.loading) {
        this.pageNum++;
        this.getEquipmentList(true);
      }
    },

    // 处理搜索
    handleSearch() {
      clearTimeout(this.searchTimer);
      this.searchTimer = setTimeout(() => {
        this.getEquipmentList();
      }, 500);
    },

    // 清除搜索
    clearSearch() {
      this.searchKeyword = '';
      this.getEquipmentList();
    },

    // 处理类型变化
    handleTypeChange(value) {
      this.selectedTypeValue = value;
      this.getEquipmentList();
    },

    // 处理状态变化
    handleStatusChange(value) {
      this.selectedStatusValue = value;
      this.getEquipmentList();
    },

    // 获取类型标签
    getTypeLabel(value) {
      return this.$dict.getDictLabel('equipment_type', value, '未知');
    },

    // 获取状态标签
    getStatusLabel(value) {
      return this.$dict.getDictLabel('equipment_status', value, '未知');
    },

    // 返回上一页
    goBack() {
      uni.navigateBack();
    },

    // 跳转到详情页
    goToDetail(item) {
      uni.navigateTo({
        url: `/pages/equipment/detail?id=${item.equipmentId}`
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}

/* 顶部导航栏 */
.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background-color: #2979ff;
  padding: 10px 15px;
  padding-top: var(--status-bar-height);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.back-icon {
  padding: 5px;
}

.header-title {
  color: #ffffff;
  font-size: 18px;
  font-weight: bold;
}

.header-right {
  width: 22px;
}

/* 搜索栏 */
.search-container {
  position: fixed;
  top: calc(var(--status-bar-height) + 44px);
  left: 0;
  right: 0;
  z-index: 999;
  background-color: #ffffff;
  padding: 10px 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.search-box {
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 20px;
  padding: 6px 12px;
  margin-bottom: 10px;
}

.search-input {
  flex: 1;
  height: 24px;
  margin: 0 8px;
  font-size: 14px;
}

.filter-box {
  display: flex;
  justify-content: space-between;
  gap: 10px;
}

.filter-select {
  flex: 1;
}

/* 内容区域 */
.content-scroll {
  flex: 1;
  padding-top: 10px;
  margin-top: calc(var(--status-bar-height) + 44px + 90px);
  height: calc(100vh - (var(--status-bar-height) + 44px + 90px));
}

.equipment-item {
  background-color: #ffffff;
  margin: 8px 12px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.equipment-header {
  padding: 10px 12px;
  border-bottom: 1px solid #f0f0f0;
}

.equipment-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 3px;
}

.name-container {
  display: flex;
  align-items: center;
  gap: 4px;
}

.equipment-icon {
  margin-right: 2px;
}

.equipment-name {
  font-size: 15px;
  font-weight: bold;
  color: #333333;
  line-height: 1.2;
}

.equipment-tag {
  font-size: 11px;
  padding: 1px 5px;
  border-radius: 3px;
  line-height: 1.2;
  min-width: 36px;
  text-align: center;
}

.status-normal {
  background-color: #e1f3d8;
  color: #67c23a;
}

.status-error {
  background-color: #fdf6ec;
  color: #e6a23c;
}

.equipment-code {
  font-size: 11px;
  color: #909399;
  line-height: 1.2;
}

.equipment-info {
  padding: 6px 12px 8px;
  display: flex;
  flex-wrap: wrap;
  row-gap: 4px;
  background-color: #fafafa;
}

.info-item {
  width: 50%;
  display: flex;
  align-items: center;
  min-height: 18px;
}

.info-label {
  font-size: 11px;
  color: #909399;
  margin-right: 4px;
  white-space: nowrap;
  min-width: 50px;
}

.info-value {
  font-size: 11px;
  color: #333333;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 加载更多 */
.load-more {
  text-align: center;
  padding: 15px 0;
  color: #909399;
  font-size: 14px;
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 300px;
}

.empty-text {
  margin-top: 15px;
  color: #909399;
  font-size: 14px;
}

/* 加载中 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100px;
}
</style>