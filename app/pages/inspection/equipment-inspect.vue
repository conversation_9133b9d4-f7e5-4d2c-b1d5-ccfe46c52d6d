<template>
  <view class="container">
    <!-- 顶部导航栏 -->
    <view class="header" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="header-content">
        <view class="back-icon" @click="goBack">
          <uni-icons type="left" size="18" color="#FFFFFF"></uni-icons>
        </view>
        <view class="header-title">设备巡检</view>
        <view style="width: 20px;"></view>
      </view>
    </view>

    <!-- 内容区域 -->
    <scroll-view scroll-y class="content" :style="{ paddingTop: (statusBarHeight + 44) + 'px' }">
      <view v-if="equipmentInfo && inspectionItems.length > 0" class="inspection-detail">
        <!-- 任务状态标识 -->
        <view class="status-banner status-inspecting">
          <view class="status-content">
            <view class="status-left">
              <view class="status-icon">
                <uni-icons type="checkbox" size="18" color="#FFFFFF"></uni-icons>
              </view>
              <text class="status-text">巡检中</text>
            </view>
            <!-- 操作按钮 -->
            <view class="action-buttons-top">
              <button class="action-button-top cancel" @click="goBack">
                取消
              </button>
              <button class="action-button-top save" @click="handleSave">
                保存
              </button>
            </view>
          </view>
        </view>

        <!-- 设备信息区域 -->
        <view class="detail-card">
          <view class="card-header">
            <uni-icons type="info-filled" size="16" color="#1a56b3"></uni-icons>
            <text class="card-title">设备信息</text>
          </view>
          <view class="card-content">
            <view class="info-item">
              <text class="info-label">设备名称</text>
              <text class="info-value">{{ equipmentInfo.equipmentName }}</text>
            </view>
            <view class="info-item">
              <text class="info-label">设备类型</text>
              <text class="info-value">{{ getEquipmentTypeLabel(equipmentInfo.equipmentType) }}</text>
            </view>
            <view class="info-item">
              <text class="info-label">设备状态</text>
              <text class="info-value">{{ getEquipmentStatusLabel(equipmentInfo.status) }}</text>
            </view>
          </view>
        </view>

        <!-- 巡检项目区域 -->
        <view class="detail-card">
          <view class="card-header">
            <uni-icons type="checkbox-filled" size="16" color="#1a56b3"></uni-icons>
            <text class="card-title">巡检项目</text>
          </view>
          <view class="card-content">
            <view class="inspection-item" v-for="(item, index) in inspectionItems" :key="index">
              <view class="item-header">
                <view class="item-left">
                  <view class="item-index">{{ index + 1 }}</view>
                  <text class="item-content">{{ item.content }}</text>
                </view>
                <view class="item-status">
                  <view class="status-indicator" :class="getItemStatusClass(item)">
                    <uni-icons
                      :type="getItemStatusIcon(item)"
                      :size="16"
                      :color="getItemStatusColor(item)"
                    ></uni-icons>
                  </view>
                </view>
              </view>
              <!-- 根据type渲染不同的输入控件 -->
              <!-- 单选类型 -->
              <view class="item-result" v-if="item.type === 'radio' || !item.type">
                <radio-group @change="(e) => handleRadioChange(index, e.detail.value)">
                  <label class="radio-label" v-for="(option, optionIndex) in (item.options || ['合格', '不合格'])" :key="optionIndex">
                    <radio :value="optionIndex.toString()" color="#67c23a" :checked="item.result === optionIndex.toString()" />
                    <text>{{ option }}</text>
                  </label>
                </radio-group>
              </view>
              <!-- 文本类型 -->
              <view class="item-result" v-else-if="item.type === 'text'">
                <textarea
                  class="text-input"
                  placeholder="请输入检查结果"
                  v-model="item.result"
                  maxlength="200"
                ></textarea>
              </view>
              <!-- 备注（在单选类型且选择了非第一个选项时显示） -->
              <view class="item-remark" v-if="(item.type === 'radio' || !item.type) && item.showRemark">
                <textarea
                  class="remark-input"
                  :placeholder="`请输入${item.options && item.options[parseInt(item.result)] || '不合格'}原因`"
                  v-model="item.remark"
                  maxlength="200"
                ></textarea>
              </view>

              <!-- 每个巡检项目的拍照上传区域 -->
              <view class="item-photos">
                <view class="photos-title">
                  <uni-icons type="camera-filled" size="14" color="#1a56b3"></uni-icons>
                  <text class="photos-text">拍照记录</text>
                </view>
                <uni-file-picker
                  v-model="item.fileList"
                  file-mediatype="image"
                  mode="grid"
                  :limit="6"
                  :image-styles="imageStyles"
                  :auto-upload="false"
                  @select="(e) => selectFiles(e, index)"
                  @delete="(e) => deleteFile(e, index)"
                  :source-type="['camera']"
                />
              </view>
            </view>
          </view>
        </view>

        <!-- 备注区域 -->
        <view class="detail-card">
          <view class="card-header">
            <uni-icons type="chatbubble-filled" size="16" color="#1a56b3"></uni-icons>
            <text class="card-title">巡检备注</text>
          </view>
          <view class="card-content">
            <textarea
              class="remark-textarea"
              placeholder="请输入巡检备注信息（选填）"
              v-model="remark"
              maxlength="500"
            ></textarea>
          </view>
        </view>
      </view>

      <loading-container
        v-else
        :loading="loading"
        :error="!loading && (!equipmentInfo || inspectionItems.length === 0)"
        loadingText="正在加载设备信息..."
        errorText="获取设备信息失败"
        @retry="refreshData"
      />
    </scroll-view>

    <!-- 隐藏的canvas用于水印处理 -->
    <canvas
      v-if="waterMarkParams.display"
      canvas-id="watermarkCanvas"
      :style="canvasStyle"
    ></canvas>
  </view>
</template>

<script>
import { getInspection, getInspectionEquipments, updateEquipmentInspection } from '@/api/system/inspection'
import { getEquipment } from '@/api/system/equipment'
import { getDictData } from '@/api/system/dictData'
import { uploadFile } from '@/api/system/common'
import LoadingContainer from '@/components/LoadingContainer'
import { getRelativePath, getFullUrl } from '@/utils/imageUrlUtils'
import { decodeInspectionItems } from '@/utils/htmlDecoder'
import { addInspectionWatermark } from '@/utils/watermark'

export default {
  components: {
    LoadingContainer
  },
  data() {
    return {
      statusBarHeight: 0,
      inspectionId: null,
      equipmentId: null,
      inspectionInfo: null,
      equipmentInfo: null,
      inspectionEquipment: null,
      inspectionItems: [],
      remark: '',
      imageStyles: {
        width: 80,
        height: 80,
        border: {
          radius: '4px'
        }
      },
      loading: true,
      dictData: {},
      userId: null,
      waterMarkParams: {
        display: false, // 控制 canvas 创建与销毁
        canvasWidth: 300, // 默认宽度
        canvasHeight: 225, // 默认高度
      }
    }
  },
  computed: {
    canvasStyle() {
      return {
        position: 'fixed', // 移除到屏幕外
        left: '9999px',
        width: this.waterMarkParams.canvasWidth + 'px',
        height: this.waterMarkParams.canvasHeight + 'px',
      }
    }
  },
  onLoad(options) {
    // 设置状态栏高度
    this.statusBarHeight = uni.getSystemInfoSync().statusBarHeight;

    // 获取当前登录用户ID
    this.userId = this.$store.state.user.userId;

    if (options.inspectionId && options.equipmentId) {
      this.inspectionId = options.inspectionId;
      this.equipmentId = options.equipmentId;
      this.getDicts();
    } else {
      this.loading = false;
      uni.showToast({
        title: '缺少必要参数',
        icon: 'none'
      });
    }
  },
  methods: {
    // 获取字典数据
    async getDicts() {
      try {
        const response = await getDictData();
        this.dictData = this.$dict.parseDictData(response.data);
        this.loadData();
      } catch (error) {
        console.error('获取字典数据失败:', error);
        uni.showToast({
          title: '获取字典数据失败',
          icon: 'none'
        });
        this.loading = false;
      }
    },

    // 加载数据
    async loadData() {
      try {
        // 并行加载数据
        const [inspectionResponse, equipmentResponse, inspectionEquipmentsResponse] = await Promise.all([
          getInspection(this.inspectionId),
          getEquipment(this.equipmentId),
          getInspectionEquipments(this.inspectionId)
        ]);

        this.inspectionInfo = inspectionResponse.data;
        this.equipmentInfo = equipmentResponse.data;

        // 查找当前设备的巡检记录
        const equipmentList = inspectionEquipmentsResponse.data || [];
        this.inspectionEquipment = equipmentList.find(item =>
          item.equipmentId.toString() === this.equipmentId.toString()
        );

        // 加载已有数据
        if (this.inspectionEquipment && this.inspectionEquipment.inspectionContent) {
          this.loadExistingInspectionData();
        }
      } catch (error) {
        console.error('加载数据失败:', error);
        uni.showToast({
          title: '加载数据失败',
          icon: 'none'
        });
      } finally {
        this.loading = false;
      }
    },

    // 加载已有的巡检数据
    loadExistingInspectionData() {
      try {
        if (this.inspectionEquipment.inspectionContent) {
          const content = JSON.parse(this.inspectionEquipment.inspectionContent);
          if (Array.isArray(content) && content.length > 0) {
            // 解码HTML实体
            const decodedContent = decodeInspectionItems(content);

            // 处理每个检查项，设置showRemark属性和初始化照片数组
            this.inspectionItems = decodedContent.map(item => {
              const processedItem = { ...item };

              // 如果是单选类型，根据result值设置showRemark
              if (processedItem.type === 'radio' || !processedItem.type) {
                // 如果选择了非第一个选项，显示备注区域
                processedItem.showRemark = processedItem.result && processedItem.result !== '0';
              }

              // 初始化每个巡检项目的照片相关数组
              if (!processedItem.photos) {
                processedItem.photos = [];
              }

              // 将已有的图片转换为uni-file-picker组件需要的格式
              processedItem.fileList = processedItem.photos.map((url, index) => {
                const fullUrl = getFullUrl(url);
                return {
                  url: fullUrl,
                  name: `image_${index}.jpg`,
                  extname: 'jpg',
                  size: 0
                };
              });

              // 初始化uploadedFiles数组
              processedItem.uploadedFiles = processedItem.photos.map((url, index) => {
                const fullUrl = getFullUrl(url);
                return {
                  url: fullUrl,
                  name: `image_${index}.jpg`,
                  extname: 'jpg',
                  size: 0,
                  serverUrl: fullUrl
                };
              });

              return processedItem;
            });
          }
        }

        if (this.inspectionEquipment.equipmentRemark) {
          this.remark = this.inspectionEquipment.equipmentRemark;
        }
      } catch (error) {
        console.error('加载已有巡检数据失败:', error);
      }
    },

    // 获取设备类型标签
    getEquipmentTypeLabel(type) {
      if (!type || !this.dictData.equipment_type) return '未知类型';
      return this.$dict.getDictLabel('equipment_type', type, '未知类型');
    },

    // 获取设备状态标签
    getEquipmentStatusLabel(status) {
      if (!status || !this.dictData.equipment_status) return '未知状态';
      return this.$dict.getDictLabel('equipment_status', status, '未知状态');
    },

    // 处理单选类型巡检结果变化
    handleRadioChange(index, value) {
      const item = this.inspectionItems[index];
      item.result = value;

      // 如果选择第一个选项（通常是通过/合格）
      if (value === '0') {
        item.remark = '';
        item.showRemark = false;
      }
      // 如果选择了其他选项，显示备注区域
      else {
        item.showRemark = true;
        // 如果没有备注，初始化一个空备注
        if (!item.remark) {
          item.remark = '';
        }
      }
    },

    // 选择文件回调
    selectFiles(e, itemIndex) {
      // 获取选择的文件
      const tempFiles = e.tempFiles || [];

      // 获取当前巡检项目
      const item = this.inspectionItems[itemIndex];
      if (!item) return;

      // 显示加载中提示
      if (tempFiles.length > 0) {
        uni.showLoading({
          title: '正在上传...',
          mask: true
        });
      }

      // 处理每个文件的上传
      const uploadPromises = tempFiles.map((file, fileIndex) => {
        return new Promise(async (resolve, reject) => {
          try {
            // 生成自定义文件名：XJ_{设备名称}_{时间戳}
            const timestamp = this.generateTimestamp();
            const equipmentName = this.equipmentInfo.equipmentName || 'Unknown';
            // 清理设备名称中的特殊字符，只保留中文、英文、数字和下划线
            const cleanEquipmentName = equipmentName.replace(/[^\u4e00-\u9fa5a-zA-Z0-9_]/g, '_');
            const customFileName = `XJ_${cleanEquipmentName}_${timestamp}`;

            // 准备水印信息
            const watermarkInfo = {
              taskName: this.inspectionInfo?.inspectionName || '设备巡检',
              equipmentName: this.equipmentInfo?.equipmentName || '未知设备',
              time: this.formatDateTime(new Date())
            };

            // 为图片添加水印
            const watermarkedImagePath = await addInspectionWatermark(file.path, watermarkInfo, this);

            // 构建formData，传递自定义文件名
            const formData = {
              customFileName: customFileName
            };

            // 使用添加水印后的图片进行上传
            const res = await uploadFile(watermarkedImagePath, formData);

            if (res.code === 200) {
              // 保存服务器返回的URL
              const uploadedFile = {
                ...file,
                serverUrl: res.url,
                customFileName: customFileName,
                hasWatermark: true
              };
              resolve(uploadedFile);
            } else {
              reject(new Error(res.msg || '上传失败'));
            }
          } catch (err) {
            console.error('文件上传失败:', err);
            reject(err);
          }
        });
      });

      // 等待所有文件上传完成
      Promise.all(uploadPromises)
        .then(uploadedFiles => {
          // 关闭加载提示
          uni.hideLoading();

          // 确保巡检项目有uploadedFiles属性
          if (!item.uploadedFiles) {
            item.uploadedFiles = [];
          }

          // 将新上传的文件添加到已上传文件列表
          item.uploadedFiles = [...item.uploadedFiles, ...uploadedFiles];

          // 确保巡检项目有photos属性
          if (!item.photos) {
            item.photos = [];
          }

          // 更新photos数组，用于最终提交（只保存相对路径）
          item.photos = item.uploadedFiles.map(file => getRelativePath(file.serverUrl));

          uni.showToast({
            title: '图片上传成功',
            icon: 'success'
          });
        })
        .catch(err => {
          // 关闭加载提示
          uni.hideLoading();

          console.error('文件上传失败:', err);
          uni.showToast({
            title: '图片上传失败',
            icon: 'none'
          });
        });
    },

    // 删除文件回调
    deleteFile(e, itemIndex) {
      const index = e.index;

      // 获取当前巡检项目
      const item = this.inspectionItems[itemIndex];
      if (!item || !item.uploadedFiles) return;

      // 如果是已上传的文件，从上传文件列表中删除
      if (index >= 0 && index < item.uploadedFiles.length) {
        item.uploadedFiles.splice(index, 1);
      }

      // 更新photos数组，用于最终提交（只保存相对路径）
      item.photos = item.uploadedFiles.map(file => getRelativePath(file.serverUrl));
    },

    // 判断单个检查项是否完成
    isItemCompleted(item) {
      // 检查是否有图片上传
      const hasPhotos = item.photos && item.photos.length > 0;

      // 对于单选类型
      if (item.type === 'radio' || !item.type) {
        // 如果没有选择任何选项，返回false
        if (!item.result) return false;
        // 如果选择了第一个选项（通常是通过/合格），需要有图片
        if (item.result === '0') return hasPhotos;
        // 如果选择了其他选项，需要有备注和图片
        return item.remark && item.remark.trim() !== '' && hasPhotos;
      }
      // 对于文本类型，检查是否已填写内容和上传图片
      else if (item.type === 'text') {
        return item.result && item.result.trim() !== '' && hasPhotos;
      }
      return false;
    },

    // 判断检查项是否有部分内容
    isItemPartiallyCompleted(item) {
      // 对于单选类型
      if (item.type === 'radio' || !item.type) {
        return !!item.result; // 只要选择了选项就算部分完成
      }
      // 对于文本类型
      else if (item.type === 'text') {
        return item.result && item.result.trim() !== ''; // 只要填写了内容就算部分完成
      }
      return false;
    },

    // 获取检查项状态图标
    getItemStatusIcon(item) {
      if (this.isItemCompleted(item)) {
        return 'checkmarkempty'; // 完成：勾选图标
      } else if (this.isItemPartiallyCompleted(item)) {
        return 'info'; // 部分完成：感叹号图标
      } else {
        return 'circle'; // 未开始：圆圈图标
      }
    },

    // 获取检查项状态颜色
    getItemStatusColor(item) {
      if (this.isItemCompleted(item)) {
        return '#67c23a'; // 完成：绿色
      } else if (this.isItemPartiallyCompleted(item)) {
        return '#e6a23c'; // 部分完成：黄色
      } else {
        return '#ddd'; // 未开始：灰色
      }
    },

    // 获取检查项状态样式类
    getItemStatusClass(item) {
      if (this.isItemCompleted(item)) {
        return 'completed'; // 完成
      } else if (this.isItemPartiallyCompleted(item)) {
        return 'incomplete'; // 部分完成
      } else {
        return 'pending'; // 未开始
      }
    },

    // 判断是否所有项目都已检查
    isAllItemsChecked() {
      return this.inspectionItems.every(item => this.isItemCompleted(item));
    },

    // 保存巡检结果
    async handleSave() {
      try {
        // 检查是否有未完成的项目
        if (!this.isAllItemsChecked()) {
          // 找出未完成的项目
          const incompleteItems = this.inspectionItems
            .map((item, index) => ({ item, index: index + 1 }))
            .filter(({ item }) => !this.isItemCompleted(item));

          let message = '以下检查项尚未完成：\n';
          incompleteItems.forEach(({ item, index }) => {
            const issues = [];

            // 检查单选项
            if (item.type === 'radio' || !item.type) {
              if (!item.result) {
                issues.push('未选择结果');
              } else if (item.result !== '0' && (!item.remark || item.remark.trim() === '')) {
                issues.push('需要填写备注');
              }
            }
            // 检查文本项
            else if (item.type === 'text') {
              if (!item.result || item.result.trim() === '') {
                issues.push('需要填写内容');
              }
            }

            // 检查图片
            if (!item.photos || item.photos.length === 0) {
              issues.push('需要上传图片');
            }

            if (issues.length > 0) {
              message += `${index}. ${item.content}：${issues.join('、')}\n`;
            }
          });

          message += '\n是否仍要保存当前进度？';

          // 显示确认对话框，允许用户选择是否继续保存
          const result = await new Promise((resolve) => {
            uni.showModal({
              title: '检查项未完成',
              content: message,
              showCancel: true,
              cancelText: '继续编辑',
              confirmText: '仍要保存',
              success: (res) => {
                resolve(res.confirm);
              },
              fail: () => {
                resolve(false);
              }
            });
          });

          // 如果用户选择不保存，直接返回
          if (!result) {
            return;
          }
        }

        uni.showLoading({
          title: '保存中...'
        });

        // 清理inspectionItems中的临时数据，只保留必要的字段
        const cleanedItems = this.inspectionItems.map(item => {
          // 创建一个新对象，只包含需要保存的字段
          const cleanedItem = {
            content: item.content,
            type: item.type,
            options: item.options,
            result: item.result,
            remark: item.remark,
            photos: item.photos || []
          };
          return cleanedItem;
        });

        // 构建巡检数据
        const inspectionData = {
          inspectionId: this.inspectionId,
          equipmentId: this.equipmentId,
          userId: this.userId,
          inspectionContent: JSON.stringify(cleanedItems),
          equipmentRemark: this.remark,
          inspectionTime: this.formatDateTime(new Date())
        };

        // 调用API保存巡检数据
        const response = await updateEquipmentInspection(inspectionData);

        if (response.code === 200) {
          uni.hideLoading();

          // 根据完成状态显示不同的提示信息
          const isAllCompleted = this.isAllItemsChecked();
          const toastTitle = isAllCompleted ? '巡检保存成功' : '进度已保存';
          const toastIcon = isAllCompleted ? 'success' : 'none';

          uni.showToast({
            title: toastTitle,
            icon: toastIcon,
            duration: 2000,
            success: () => {
              setTimeout(() => {
                // 获取页面栈
                const pages = getCurrentPages();
                // 获取上一个页面
                const prevPage = pages[pages.length - 2];
                // 如果上一个页面存在且有refreshData方法，调用它刷新数据
                if (prevPage && prevPage.$vm && prevPage.$vm.refreshData) {
                  prevPage.$vm.refreshData();
                }
                // 返回上一页
                uni.navigateBack();
              }, 1000);
            }
          });
        } else {
          throw new Error(response.msg || '保存失败');
        }
      } catch (error) {
        uni.hideLoading();
        console.error('保存巡检结果失败:', error);
        uni.showToast({
          title: error.message || '保存失败',
          icon: 'none'
        });
      }
    },

    // 返回上一页
    goBack() {
      // 获取页面栈
      const pages = getCurrentPages();
      // 获取上一个页面
      const prevPage = pages[pages.length - 2];
      // 如果上一个页面存在且有refreshData方法，调用它刷新数据
      if (prevPage && prevPage.$vm && prevPage.$vm.refreshData) {
        prevPage.$vm.refreshData();
      }
      uni.navigateBack();
    },

    // 刷新数据
    refreshData() {
      this.loading = true;
      this.getDicts();
      uni.showToast({
        title: '刷新中',
        icon: 'loading',
        duration: 500
      });
    },

    // 格式化日期时间为 yyyy-MM-dd HH:mm:ss 格式
    formatDateTime(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');

      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },

    // 生成时间戳格式：20250512105630
    generateTimestamp() {
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, '0');
      const day = String(now.getDate()).padStart(2, '0');
      const hours = String(now.getHours()).padStart(2, '0');
      const minutes = String(now.getMinutes()).padStart(2, '0');
      const seconds = String(now.getSeconds()).padStart(2, '0');

      return `${year}${month}${day}${hours}${minutes}${seconds}`;
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #ffffff;
}

/* 顶部导航栏 */
.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: linear-gradient(135deg, #1a56b3 0%, #3a7bd5 100%);
  padding: 10px 15px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.back-icon {
  padding: 5px;
}

.header-title {
  color: #ffffff;
  font-size: 18px;
  font-weight: bold;
}

/* 内容区域 */
.content {
  flex: 1;
}

.inspection-detail {
  padding: 0;
}

/* 状态横幅 */
.status-banner {
  padding: 15px 16px;
  color: #ffffff;
  position: relative;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.status-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-left {
  display: flex;
  align-items: center;
}

.status-icon {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
}

.status-text {
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0.5px;
}

/* 上方操作按钮 */
.action-buttons-top {
  display: flex;
  gap: 10px;
}

.action-button-top {
  background-color: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.4);
  color: #ffffff;
  font-size: 13px;
  padding: 4px 12px;
  border-radius: 15px;
  height: auto;
  line-height: 1.5;
  font-weight: normal;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.action-button-top:active {
  transform: scale(0.95);
  background-color: rgba(255, 255, 255, 0.3);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.action-button-top.cancel {
  background-color: rgba(255, 255, 255, 0.15);
}

.action-button-top.save {
  background-color: rgba(255, 255, 255, 0.25);
}



/* 分区样式 */
.detail-card {
  background-color: #ffffff;
  margin-bottom: 10px;
  position: relative;
}

.card-header {
  display: flex;
  align-items: center;
  padding: 15px 16px 10px;
  position: relative;
}

.card-title {
  font-size: 16px;
  font-weight: bold;
  margin-left: 8px;
  color: #1a56b3;
}

.card-content {
  padding: 5px 16px 15px;
}

/* 信息项样式 */
.info-item {
  display: flex;
  margin-bottom: 10px;
  align-items: center;
  position: relative;
  border-bottom: 1px solid #f9f9f9;
  padding-bottom: 10px;
}

.info-item:last-child {
  margin-bottom: 0;
  border-bottom: none;
  padding-bottom: 0;
}

.info-label {
  width: 80px;
  color: #666;
  font-size: 14px;
  flex-shrink: 0;
  position: relative;
  padding-left: 10px;
}

.info-label::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 14px;
  background-color: #1a56b3;
  opacity: 0.6;
  border-radius: 1.5px;
}

.info-value {
  flex: 1;
  color: #333;
  font-size: 14px;
  word-break: break-all;
  font-weight: 500;
}

/* 巡检项目样式 */
.inspection-item {
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #f5f5f5;
}

.inspection-item:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.item-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.item-index {
  width: 24px;
  height: 24px;
  background-color: #1a56b3;
  color: #ffffff;
  border-radius: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 14px;
  margin-right: 10px;
  flex-shrink: 0;
}

.item-content {
  font-size: 15px;
  color: #333;
  font-weight: 500;
  flex: 1;
}

.item-status {
  display: flex;
  align-items: center;
  margin-left: 10px;
}

.status-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.status-indicator.completed {
  background-color: rgba(103, 194, 58, 0.1);
}

.status-indicator.incomplete {
  background-color: rgba(230, 162, 60, 0.1);
}

.status-indicator.pending {
  background-color: rgba(221, 221, 221, 0.1);
}

.item-result {
  margin-left: 34px;
  margin-bottom: 10px;
}

.radio-label {
  margin-right: 20px;
  font-size: 14px;
}

.radio-label text {
  margin-left: 4px;
}

.item-remark {
  margin-left: 34px;
}

.remark-input, .text-input {
  width: 100%;
  height: 80px;
  background-color: #f9f9f9;
  border-radius: 4px;
  padding: 10px;
  font-size: 14px;
  color: #333;
}

.text-input {
  background-color: #f5f9ff;
  border: 1px solid #e0e9f7;
}

/* 巡检项目照片区域样式 */
.item-photos {
  margin-left: 34px;
  margin-top: 15px;
}

.photos-title {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.photos-text {
  font-size: 14px;
  color: #1a56b3;
  margin-left: 5px;
  font-weight: 500;
}

/* 文件上传组件样式 */
:deep(.uni-file-picker__container) {
  display: flex;
  flex-wrap: wrap;
}

:deep(.file-picker__box) {
  margin-right: 10px;
  margin-bottom: 10px;
}

:deep(.is-add) {
  border: 1px dashed #ddd;
  background-color: #f5f5f5;
}

/* 备注样式 */
.remark-textarea {
  width: 100%;
  height: 100px;
  background-color: #f9f9f9;
  border-radius: 4px;
  padding: 10px;
  font-size: 14px;
  color: #333;
}

/* 状态样式 */
.status-inspecting {
  background: linear-gradient(135deg, #409eff 0%, #64b0ff 100%);
}
</style>
