<template>
  <view class="container">
    <!-- 顶部导航栏 -->
    <view class="header" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="header-content">
        <view class="back-icon" @click="goBack">
          <uni-icons type="left" size="18" color="#FFFFFF"></uni-icons>
        </view>
        <view class="header-title">执行巡检任务</view>
        <view style="width: 20px;"></view>
      </view>
    </view>

    <!-- 内容区域 -->
    <scroll-view scroll-y class="content" :style="{ paddingTop: (statusBarHeight + 44) + 'px' }">
      <view v-if="inspectionInfo" class="inspection-execute">
        <!-- 任务状态标识 -->
        <view class="status-banner status-received">
          <view class="status-content">
            <view class="status-left">
              <view class="status-icon">
                <uni-icons type="staff-filled" size="18" color="#FFFFFF"></uni-icons>
              </view>
              <text class="status-text">执行中</text>
            </view>
            <!-- 操作按钮 -->
            <view class="action-buttons-top" v-if="equipmentList.length > 0">
              <button class="action-button-top" type="primary" @click="handleSubmit" :disabled="!isAllEquipmentInspected()">
                提交巡检
              </button>
            </view>
          </view>
        </view>

        <!-- 基本信息区域 -->
        <view class="detail-card">
          <view class="card-header">
            <uni-icons type="info-filled" size="16" color="#1a56b3"></uni-icons>
            <text class="card-title">基本信息</text>
          </view>
          <view class="card-content">
            <view class="info-item">
              <text class="info-label">任务名称</text>
              <text class="info-value">{{ inspectionInfo.inspectionName }}</text>
            </view>
            <view class="info-item">
              <text class="info-label">所属电站</text>
              <text class="info-value">{{ inspectionInfo.station ? inspectionInfo.station.stationName : '未知' }}</text>
            </view>
            <view class="info-item">
              <text class="info-label">计划时间</text>
              <text class="info-value">{{ inspectionInfo.assignStartTime }} - {{ inspectionInfo.assignEndTime }}</text>
            </view>
            <view class="info-item">
              <text class="info-label">实际巡检人员</text>
              <text class="info-value">{{ inspectionInfo.inspectorUserName || '未指定' }}</text>
            </view>
          </view>
        </view>

        <!-- 设备列表区域 -->
        <view class="detail-card">
          <view class="card-header">
            <uni-icons type="list" size="16" color="#1a56b3"></uni-icons>
            <text class="card-title">巡检设备</text>
            <text class="equipment-count">共 {{ equipmentList.length }} 台设备</text>
          </view>
          <view class="card-content">
            <view v-if="equipmentList.length > 0" class="equipment-list">
              <view class="equipment-item" v-for="(item, index) in equipmentList" :key="index" @click="goToEquipmentInspection(item)">
                <view class="equipment-header">
                  <text class="equipment-name">{{ item.equipment ? item.equipment.equipmentName : '未知设备' }}</text>
                  <view class="equipment-type" v-if="item.equipment">
                    {{ getEquipmentTypeLabel(item.equipment.equipmentType) }}
                  </view>
                </view>
                <view class="equipment-status">
                  <view class="status-tag" :class="getInspectionStatusClass(item)">
                    {{ getInspectionStatusLabel(item) }}
                  </view>
                  <uni-icons type="right" size="14" color="#999"></uni-icons>
                </view>
              </view>
            </view>
            <view v-else class="empty-equipment">
              <uni-icons type="info" size="36" color="#ddd"></uni-icons>
              <text>暂无关联设备</text>
            </view>
          </view>
        </view>
      </view>

      <loading-container
        v-else
        :loading="loading"
        :error="!loading && !inspectionInfo"
        loadingText="正在加载巡检任务..."
        errorText="获取巡检任务失败"
        @retry="refreshData"
      />
    </scroll-view>
  </view>
</template>

<script>
import { getInspection, getInspectionEquipments, submitInspection } from '@/api/system/inspection'
import { getDictData } from '@/api/system/dictData'
import LoadingContainer from '@/components/LoadingContainer'

export default {
  components: {
    LoadingContainer
  },
  data() {
    return {
      statusBarHeight: 0,
      inspectionId: null,
      inspectionInfo: null,
      equipmentList: [],
      loading: true,
      dictData: {}
    }
  },
  onLoad(options) {
    // 设置状态栏高度
    this.statusBarHeight = uni.getSystemInfoSync().statusBarHeight;

    if (options.id) {
      this.inspectionId = options.id;
      this.getDicts();
    } else {
      this.loading = false;
      uni.showToast({
        title: '缺少任务ID',
        icon: 'none'
      });
    }
  },
  methods: {
    // 获取字典数据
    async getDicts() {
      try {
        const response = await getDictData();
        this.dictData = this.$dict.parseDictData(response.data);
        this.getInspectionDetail();
      } catch (error) {
        console.error('获取字典数据失败:', error);
        uni.showToast({
          title: '获取字典数据失败',
          icon: 'none'
        });
        this.loading = false;
      }
    },

    // 获取巡检详情
    async getInspectionDetail() {
      try {
        const response = await getInspection(this.inspectionId);
        this.inspectionInfo = response.data;

        // 获取关联的设备列表
        this.getEquipmentList();
      } catch (error) {
        console.error('获取巡检详情失败:', error);
        uni.showToast({
          title: '获取巡检详情失败',
          icon: 'none'
        });
        this.loading = false;
      }
    },

    // 获取关联的设备列表
    async getEquipmentList() {
      try {
        const response = await getInspectionEquipments(this.inspectionId);
        this.equipmentList = response.data || [];
      } catch (error) {
        console.error('获取关联设备失败:', error);
        uni.showToast({
          title: '获取关联设备失败',
          icon: 'none'
        });
      } finally {
        this.loading = false;
      }
    },

    // 获取设备类型标签
    getEquipmentTypeLabel(type) {
      if (!type || !this.dictData.equipment_type) return '未知类型';
      return this.$dict.getDictLabel('equipment_type', type, '未知类型');
    },

    // 判断设备是否已开始巡检
    hasStartedInspection(item) {
      if (!item.inspectionContent) return false;

      try {
        const inspectionItems = JSON.parse(item.inspectionContent);
        if (!Array.isArray(inspectionItems) || inspectionItems.length === 0) {
          return false;
        }

        // 检查是否有任何巡检项目已经开始填写
        return inspectionItems.some(inspectionItem => {
          // 如果有选择结果或填写内容，说明已开始
          if (inspectionItem.result) return true;
          // 如果有备注，说明已开始
          if (inspectionItem.remark && inspectionItem.remark.trim() !== '') return true;
          // 如果有上传图片，说明已开始
          if (inspectionItem.photos && inspectionItem.photos.length > 0) return true;
          return false;
        });
      } catch (error) {
        console.error('解析巡检内容失败:', error);
        return false;
      }
    },

    // 判断设备巡检内容是否完整
    isInspectionContentComplete(item) {
      if (!item.inspectionContent) return false;

      try {
        const inspectionItems = JSON.parse(item.inspectionContent);
        if (!Array.isArray(inspectionItems) || inspectionItems.length === 0) {
          return false;
        }

        // 检查每个巡检项目是否完整
        return inspectionItems.every(inspectionItem => {
          // 检查是否有图片上传
          const hasPhotos = inspectionItem.photos && inspectionItem.photos.length > 0;

          // 对于单选类型
          if (inspectionItem.type === 'radio' || !inspectionItem.type) {
            // 如果没有选择任何选项，返回false
            if (!inspectionItem.result) return false;
            // 如果选择了第一个选项（通常是通过/合格），需要有图片
            if (inspectionItem.result === '0') return hasPhotos;
            // 如果选择了其他选项，需要有备注和图片
            return inspectionItem.remark && inspectionItem.remark.trim() !== '' && hasPhotos;
          }
          // 对于文本类型，检查是否已填写内容和上传图片
          else if (inspectionItem.type === 'text') {
            return inspectionItem.result && inspectionItem.result.trim() !== '' && hasPhotos;
          }
          return false;
        });
      } catch (error) {
        console.error('解析巡检内容失败:', error);
        return false;
      }
    },

    // 获取巡检状态标签
    getInspectionStatusLabel(item) {
      // 首先检查是否已开始巡检
      const hasStarted = this.hasStartedInspection(item);

      if (!hasStarted) {
        return '待检查';
      } else {
        // 如果已开始，进一步判断内容是否完整
        const isComplete = this.isInspectionContentComplete(item);
        return isComplete ? '已完成' : '未完整';
      }
    },

    // 获取巡检状态样式类
    getInspectionStatusClass(item) {
      // 首先检查是否已开始巡检
      const hasStarted = this.hasStartedInspection(item);

      if (!hasStarted) {
        return 'status-pending';
      } else {
        // 如果已开始，进一步判断内容是否完整
        const isComplete = this.isInspectionContentComplete(item);
        return isComplete ? 'status-completed' : 'status-incomplete';
      }
    },

    // 跳转到设备巡检页面
    goToEquipmentInspection(item) {
      uni.navigateTo({
        url: `/pages/inspection/equipment-inspect?inspectionId=${this.inspectionId}&equipmentId=${item.equipmentId}`
      });
    },

    // 判断是否所有设备都已巡检
    isAllEquipmentInspected() {
      if (!this.equipmentList || this.equipmentList.length === 0) {
        return false;
      }
      return this.equipmentList.every(item => {
        // 确保内容完整
        return this.isInspectionContentComplete(item);
      });
    },

    // 提交巡检
    async handleSubmit() {
      if (!this.isAllEquipmentInspected()) {
        // 找出未完成的设备
        const incompleteEquipments = this.equipmentList.filter(item => {
          return !this.isInspectionContentComplete(item);
        });

        let message = '以下设备的巡检尚未完成：\n';
        incompleteEquipments.forEach((item, index) => {
          const equipmentName = item.equipment ? item.equipment.equipmentName : '未知设备';
          const status = this.getInspectionStatusLabel(item);
          message += `${index + 1}. ${equipmentName}（${status}）\n`;
        });

        uni.showModal({
          title: '巡检未完成',
          content: message,
          showCancel: false,
          confirmText: '知道了'
        });
        return;
      }

      // 确认提交
      const confirmResult = await new Promise((resolve) => {
        uni.showModal({
          title: '确认提交',
          content: '确定要提交巡检任务吗？提交后将无法修改。',
          showCancel: true,
          cancelText: '取消',
          confirmText: '确定提交',
          success: (res) => {
            resolve(res.confirm);
          },
          fail: () => {
            resolve(false);
          }
        });
      });

      if (!confirmResult) {
        return;
      }

      try {
        uni.showLoading({
          title: '提交中...'
        });

        // 获取当前用户ID
        const userId = this.$store.state.user.userId;

        // 构建提交数据
        const submitData = {
          inspectionId: this.inspectionId,
          userId: userId,
          status: '4', // 已提交状态
          submitTime: this.formatDateTime(new Date())
        };

        // 调用提交接口
        const response = await submitInspection(submitData);

        if (response.code === 200) {
          uni.hideLoading();
          uni.showToast({
            title: '提交成功',
            icon: 'success',
            duration: 2000,
            success: () => {
              setTimeout(() => {
                // 获取页面栈
                const pages = getCurrentPages();
                // 获取上一个页面
                const prevPage = pages[pages.length - 2];
                // 如果上一个页面存在且有refreshData方法，调用它刷新数据
                if (prevPage && prevPage.$vm && prevPage.$vm.refreshData) {
                  prevPage.$vm.refreshData();
                }
                // 返回上一页
                uni.navigateBack();
              }, 1000);
            }
          });
        } else {
          throw new Error(response.msg || '提交失败');
        }
      } catch (error) {
        uni.hideLoading();
        console.error('提交巡检失败:', error);
        uni.showToast({
          title: error.message || '提交失败',
          icon: 'none'
        });
      }
    },

    // 返回上一页
    goBack() {
      uni.navigateBack();
    },

    // 刷新数据
    refreshData() {
      this.loading = true;
      this.getDicts();
      uni.showToast({
        title: '刷新中',
        icon: 'loading',
        duration: 500
      });
    },

    // 格式化日期时间为 yyyy-MM-dd HH:mm:ss 格式
    formatDateTime(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');

      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #ffffff;
}

/* 顶部导航栏 */
.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: linear-gradient(135deg, #1a56b3 0%, #3a7bd5 100%);
  padding: 10px 15px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.back-icon {
  padding: 5px;
}

.header-title {
  color: #ffffff;
  font-size: 18px;
  font-weight: bold;
}

/* 内容区域 */
.content {
  flex: 1;
}

.inspection-execute {
  padding: 0;
}

/* 状态横幅 */
.status-banner {
  padding: 15px 16px;
  color: #ffffff;
  position: relative;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.status-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-left {
  display: flex;
  align-items: center;
}

.status-icon {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
}

.status-text {
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0.5px;
}

/* 上方操作按钮 */
.action-buttons-top {
  display: flex;
  gap: 10px;
}

.action-button-top {
  background-color: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.4);
  color: #ffffff;
  font-size: 13px;
  padding: 4px 12px;
  border-radius: 15px;
  height: auto;
  line-height: 1.5;
  font-weight: normal;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.action-button-top:active {
  transform: scale(0.95);
  background-color: rgba(255, 255, 255, 0.3);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.action-button-top[disabled] {
  opacity: 0.6;
  background-color: rgba(255, 255, 255, 0.1);
}

/* 分区样式 */
.detail-card {
  background-color: #ffffff;
  margin-bottom: 10px;
  position: relative;
}

.card-header {
  display: flex;
  align-items: center;
  padding: 15px 16px 10px;
  position: relative;
}

.card-title {
  font-size: 16px;
  font-weight: bold;
  margin-left: 8px;
  color: #1a56b3;
}

.equipment-count {
  font-size: 12px;
  color: #666;
  margin-left: auto;
}

.card-content {
  padding: 5px 16px 15px;
}

/* 信息项样式 */
.info-item {
  display: flex;
  margin-bottom: 10px;
  align-items: center;
  position: relative;
  border-bottom: 1px solid #f9f9f9;
  padding-bottom: 10px;
}

.info-item:last-child {
  margin-bottom: 0;
  border-bottom: none;
  padding-bottom: 0;
}

.info-label {
  width: 90px;
  color: #666;
  font-size: 14px;
  flex-shrink: 0;
  position: relative;
  padding-left: 10px;
}

.info-label::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 14px;
  background-color: #1a56b3;
  opacity: 0.6;
  border-radius: 1.5px;
}

.info-value {
  flex: 1;
  color: #333;
  font-size: 14px;
  word-break: break-all;
  font-weight: 500;
}

/* 设备列表样式 */
.equipment-list {
  padding: 0;
}

.equipment-item {
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
  position: relative;
}

.equipment-item:last-child {
  border-bottom: none;
}

.equipment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.equipment-name {
  font-size: 15px;
  font-weight: bold;
  color: #333;
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  position: relative;
  padding-left: 12px;
}

.equipment-name::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 16px;
  background-color: #1a56b3;
  border-radius: 2px;
}

.equipment-type {
  background-color: #409eff;
  color: #ffffff;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 500;
}

.equipment-status {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 4px;
}

.status-tag {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 12px;
  color: #ffffff;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
}

.status-pending {
  background: linear-gradient(135deg, #e6a23c 0%, #f0b95a 100%);
}

.status-completed {
  background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%);
}

.status-incomplete {
  background: linear-gradient(135deg, #f56c6c 0%, #f78989 100%);
}

.status-received {
  background: linear-gradient(135deg, #1a56b3 0%, #3a7bd5 100%);
}

.empty-equipment {
  text-align: center;
  padding: 20px 0;
  color: #999;
  font-size: 14px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 10px;
}
</style>
