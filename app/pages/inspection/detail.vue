<template>
  <view class="container">
    <!-- 顶部导航栏 -->
    <view class="header" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="header-content">
        <view class="back-icon" @click="goBack">
          <uni-icons type="left" size="18" color="#FFFFFF"></uni-icons>
        </view>
        <view class="header-title">巡检详情</view>
        <view class="header-right">
          <view class="refresh-icon" @click="refreshData">
            <uni-icons type="refresh" size="18" color="#FFFFFF"></uni-icons>
          </view>
        </view>
      </view>
    </view>

    <!-- 内容区域 -->
    <scroll-view scroll-y class="content" :style="{ paddingTop: (statusBarHeight + 44) + 'px' }">
      <view v-if="inspectionInfo" class="inspection-detail">
        <!-- 任务状态标识 -->
        <view class="status-banner" :class="getStatusClass(inspectionInfo.status)">
          <view class="status-content">
            <view class="status-left">
              <view class="status-icon">
                <uni-icons :type="getStatusIcon(inspectionInfo.status)" size="18" color="#FFFFFF"></uni-icons>
              </view>
              <text class="status-text">{{ getStatusLabel(inspectionInfo.status) }}</text>
            </view>
            <!-- 操作按钮 -->
            <view class="action-buttons-top" v-if="canPerformAction && (inspectionInfo.status === '0' || inspectionInfo.status === '2')">
              <button class="action-button-top" type="primary" @click="handleReceive" v-if="inspectionInfo.status === '0'">
                接收任务
              </button>
              <button class="action-button-top" type="success" @click="handleExecute" v-if="inspectionInfo.status === '2'">
                执行任务
              </button>
            </view>
            <!-- 非负责人提示 -->
            <view class="action-tip" v-if="!canPerformAction && (inspectionInfo.status === '0' || inspectionInfo.status === '2')">
              <text class="action-tip-text">仅负责人可操作</text>
            </view>
          </view>
        </view>
        <!-- 基本信息卡片 -->
        <view class="detail-card">
          <view class="card-header">
            <uni-icons type="info-filled" size="16" color="#2979ff"></uni-icons>
            <text class="card-title">基本信息</text>
          </view>
          <view class="card-content">
            <view class="info-item">
              <text class="info-label">任务名称</text>
              <text class="info-value">{{ inspectionInfo.inspectionName }}</text>
            </view>
            <view class="info-item">
              <text class="info-label">所属电站</text>
              <text class="info-value">{{ inspectionInfo.station ? inspectionInfo.station.stationName : '未知' }}</text>
            </view>
            <view class="info-item">
              <text class="info-label">负责人员</text>
              <text class="info-value">{{ inspectionInfo.assignChargeUserName || '未指定' }}</text>
            </view>
            <view class="info-item">
              <text class="info-label">分配人员</text>
              <text class="info-value">{{ inspectionInfo.assignUserName }}</text>
            </view>
            <view class="info-item">
              <text class="info-label">计划时间</text>
              <text class="info-value">{{ inspectionInfo.assignStartTime }} - {{ inspectionInfo.assignEndTime }}</text>
            </view>
            <view class="info-item">
              <text class="info-label">下发时间</text>
              <text class="info-value">{{ inspectionInfo.createTime }}</text>
            </view>
            <view class="info-item" v-if="inspectionInfo.inspectorUser">
              <text class="info-label">实际人员</text>
              <text class="info-value">{{ inspectionInfo.inspectorUserName }}</text>
            </view>
            <view class="info-item" v-if="inspectionInfo.receiveTime">
              <text class="info-label">接收时间</text>
              <text class="info-value">{{ formatDateTime(inspectionInfo.receiveTime) }}</text>
            </view>
            <view class="info-item" v-if="inspectionInfo.submitTime">
              <text class="info-label">提交时间</text>
              <text class="info-value">{{ formatDateTime(inspectionInfo.submitTime) }}</text>
            </view>
            <view class="info-item" v-if="inspectionInfo.reviewTime">
              <text class="info-label">审核时间</text>
              <text class="info-value">{{ formatDateTime(inspectionInfo.reviewTime) }}</text>
            </view>
            <view class="info-item" v-if="inspectionInfo.reviewComment">
              <text class="info-label">审核意见</text>
              <text class="info-value">{{ inspectionInfo.reviewComment }}</text>
            </view>
            <view class="info-item" v-if="inspectionInfo.remark">
              <text class="info-label">备注</text>
              <text class="info-value">{{ inspectionInfo.remark }}</text>
            </view>
          </view>
        </view>

        <!-- 设备列表卡片 -->
        <view class="detail-card">
          <view class="card-header">
            <uni-icons type="list" size="16" color="#2979ff"></uni-icons>
            <text class="card-title">巡检内容</text>
          </view>
          <view class="card-content">
            <view v-if="equipmentList.length > 0">
              <view class="equipment-item" v-for="(item, index) in equipmentList" :key="index">
                <view class="equipment-header">
                  <view class="equipment-name">{{ item.equipment ? item.equipment.equipmentName : '未知设备' }}</view>
                  <view class="equipment-type" v-if="item.equipment">
                    {{ getEquipmentTypeLabel(item.equipment.equipmentType) }}
                  </view>
                </view>
                <!-- 检查项列表 -->
                <view class="inspection-items" v-if="getInspectionItems(item).length > 0">
                  <view class="inspection-item" v-for="(checkItem, checkIndex) in getInspectionItems(item)" :key="checkIndex">
                    <!-- 第一行：序号和检查项内容 -->
                    <view class="inspection-item-header">
                      <text class="inspection-item-index">{{ checkIndex + 1 }}</text>
                      <text class="inspection-item-content">{{ checkItem.content }}</text>
                    </view>

                    <!-- 第二行：检查结果显示（仅当任务状态不为0时显示） -->
                    <view class="inspection-result-row" :class="{ 'result-unqualified': isUnqualifiedResult(checkItem) }" v-if="inspectionInfo.status !== '0' && hasInspectionResult(checkItem)">
                      <!-- 检查结果和时间在同一行 -->
                      <view class="result-main">
                        <text class="result-text" :class="{ 'unqualified-text': isUnqualifiedResult(checkItem) }">{{ getInspectionResult(checkItem) }}</text>
                        <text class="result-time-text" v-if="item.inspectionTime">{{ formatDateTime(item.inspectionTime) }}</text>
                      </view>

                      <!-- 图片紧凑显示 -->
                      <view class="result-photos-compact" v-if="checkItem.photos && checkItem.photos.length > 0">
                        <image
                          v-for="(photo, photoIndex) in checkItem.photos"
                          :key="photoIndex"
                          :src="getImageUrl(photo)"
                          class="result-photo-small"
                          mode="aspectFill"
                          @click="previewImage(checkItem.photos, photoIndex)"
                        />
                      </view>
                    </view>
                  </view>
                </view>
                <!-- 无检查项提示 -->
                <view class="no-inspection-items" v-else>
                  <text>暂无检查项</text>
                </view>
              </view>
            </view>
            <view v-else class="empty-equipment">
              <uni-icons type="info" size="36" color="#ddd"></uni-icons>
              <text>暂无关联设备</text>
            </view>
          </view>
        </view>
      </view>

      <loading-container
        v-else
        :loading="loading"
        :error="!loading && !inspectionInfo"
        loadingText="正在加载巡检详情..."
        errorText="获取巡检详情失败"
        @retry="refreshData"
      />
    </scroll-view>
  </view>
</template>

<script>
import { getInspection, getInspectionEquipments } from '@/api/system/inspection'
import { getDictData } from '@/api/system/dictData'
import LoadingContainer from '@/components/LoadingContainer'
import { getImageUrl } from '@/utils/imageUtils'
import { decodeInspectionItems } from '@/utils/htmlDecoder'

export default {
  components: {
    LoadingContainer
  },
  data() {
    return {
      statusBarHeight: 0,
      inspectionId: null,
      inspectionInfo: null,
      equipmentList: [],
      loading: true,
      userId: null,
      dictData: {}
    }
  },
  computed: {
    // 判断当前用户是否可以执行操作
    canPerformAction() {
      if (!this.inspectionInfo || !this.userId) return false;

      // 检查当前用户是否是巡检负责人
      if (this.inspectionInfo.assignChargeUser) {
        return this.inspectionInfo.assignChargeUser === this.userId.toString();
      }

      return false;
    }
  },
  onLoad(options) {
    // 设置状态栏高度
    this.statusBarHeight = uni.getSystemInfoSync().statusBarHeight;

    if (options.id) {
      this.inspectionId = options.id;

      // 获取当前登录用户ID
      this.userId = this.$store.state.user.userId;

      this.getDicts();
    } else {
      this.loading = false;
    }
  },
  methods: {
    // 获取字典数据
    async getDicts() {
      try {
        const response = await getDictData();
        this.dictData = this.$dict.parseDictData(response.data);
        this.getInspectionDetail();
      } catch (error) {
        console.error('获取字典数据失败:', error);
        uni.showToast({
          title: '获取字典数据失败',
          icon: 'none'
        });
        this.loading = false;
      }
    },

    // 获取巡检详情
    async getInspectionDetail() {
      try {
        const response = await getInspection(this.inspectionId);
        this.inspectionInfo = response.data;

        // 获取关联的设备列表
        this.getEquipmentList();
      } catch (error) {
        console.error('获取巡检详情失败:', error);
        uni.showToast({
          title: '获取巡检详情失败',
          icon: 'none'
        });
        this.loading = false;
      }
    },

    // 获取关联的设备列表
    async getEquipmentList() {
      try {
        const response = await getInspectionEquipments(this.inspectionId);
        this.equipmentList = response.data || [];
      } catch (error) {
        console.error('获取关联设备失败:', error);
        uni.showToast({
          title: '获取关联设备失败',
          icon: 'none'
        });
      } finally {
        this.loading = false;
      }
    },

    // 获取状态标签
    getStatusLabel(status) {
      if (!status) return '未知';
      return this.$dict.getDictLabel('inspection_status', status, '未知');
    },

    // 获取状态样式类
    getStatusClass(status) {
      const statusClassMap = {
        '0': 'status-pending',
        '2': 'status-received',
        '4': 'status-submitted',
        '6': 'status-reviewed'
      };
      return statusClassMap[status] || 'status-unknown';
    },

    // 获取状态图标
    getStatusIcon(status) {
      const statusIconMap = {
        '0': 'notification-filled',
        '2': 'staff-filled',
        '4': 'checkbox-filled',
        '6': 'medal-filled'
      };
      return statusIconMap[status] || 'info-filled';
    },

    // 获取设备类型标签
    getEquipmentTypeLabel(type) {
      if (!type || !this.dictData.equipment_type) return '未知类型';

      // 使用字典插件的getDictLabel方法获取标签
      return this.$dict.getDictLabel('equipment_type', type, '未知类型');
    },

    // 格式化日期
    formatDate(dateString) {
      if (!dateString) return '未设置';
      const date = new Date(dateString);
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
    },

    // 格式化日期时间
    formatDateTime(dateString) {
      if (!dateString) return '未设置';
      const date = new Date(dateString);
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}:${String(date.getSeconds()).padStart(2, '0')}`;
    },

    // 接收任务
    handleReceive() {
      uni.navigateTo({
        url: `/pages/inspection/receive?id=${this.inspectionId}`
      });
    },

    // 执行任务
    handleExecute() {
      uni.navigateTo({
        url: `/pages/inspection/execute?id=${this.inspectionId}`
      });
    },

    // 提交巡检
    handleSubmit() {
      uni.navigateTo({
        url: `/pages/inspection/submit?id=${this.inspectionId}`
      });
    },

    // 返回上一页
    goBack() {
      uni.navigateBack();
    },

    // 刷新数据
    refreshData() {
      this.loading = true;
      this.getDicts();
      uni.showToast({
        title: '刷新中',
        icon: 'loading',
        duration: 500
      });
    },



    // 获取设备的检查项
    getInspectionItems(item) {
      if (!item.inspectionContent) return [];

      try {
        // 尝试解析JSON字符串
        const inspectionItems = JSON.parse(item.inspectionContent);
        if (Array.isArray(inspectionItems)) {
          // 解码HTML实体
          return decodeInspectionItems(inspectionItems);
        }
        return [];
      } catch (e) {
        console.error('解析检查项失败:', e);
        return [];
      }
    },

    // 跳转到设备详情
    goToEquipmentDetail(item) {
      if (item.equipment && item.equipment.equipmentId) {
        uni.navigateTo({
          url: `/pages/equipment/detail?id=${item.equipment.equipmentId}`
        });
      }
    },

    // 判断检查项是否有结果
    hasInspectionResult(checkItem) {
      return checkItem.result !== undefined && checkItem.result !== null && checkItem.result !== '';
    },

    // 获取检查结果显示文本
    getInspectionResult(checkItem) {
      if (!this.hasInspectionResult(checkItem)) return '';

      if (checkItem.type === 'radio' || !checkItem.type) {
        // radio类型，根据result下标获取选项文本
        const resultIndex = parseInt(checkItem.result);
        if (checkItem.options && checkItem.options[resultIndex]) {
          let resultText = checkItem.options[resultIndex];
          // 如果有备注，添加备注信息
          if (checkItem.remark) {
            resultText += `（${checkItem.remark}）`;
          }
          return resultText;
        }
        return checkItem.result;
      } else if (checkItem.type === 'text') {
        // text类型，直接返回文本值
        return checkItem.result;
      }

      return checkItem.result;
    },

    // 判断检查项是否为不合格结果
    isUnqualifiedResult(checkItem) {
      if (!this.hasInspectionResult(checkItem)) return false;

      if (checkItem.type === 'radio' || !checkItem.type) {
        // radio类型，result为0表示合格（第一个选项），非0表示不合格
        const resultIndex = parseInt(checkItem.result);
        return resultIndex !== 0;
      } else if (checkItem.type === 'text') {
        // text类型，可以根据具体业务逻辑判断，这里暂时返回false
        return false;
      }

      return false;
    },

    // 获取图片URL
    getImageUrl(path) {
      return getImageUrl(path);
    },

    // 预览图片
    previewImage(photos, current) {
      const urls = photos.map(photo => getImageUrl(photo));
      uni.previewImage({
        urls: urls,
        current: current
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #ffffff;
}

/* 顶部导航栏 */
.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: linear-gradient(135deg, #1a56b3 0%, #3a7bd5 100%);
  padding: 10px 15px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.back-icon {
  padding: 5px;
}

.header-title {
  color: #ffffff;
  font-size: 18px;
  font-weight: bold;
}

/* 内容区域 */
.content {
  flex: 1;
}

.inspection-detail {
  padding: 0;
}

/* 分区样式 */
.detail-card {
  background-color: #ffffff;
  margin-bottom: 10px;
  position: relative;
}

.card-header {
  display: flex;
  align-items: center;
  padding: 15px 16px 10px;
  position: relative;
}

.card-title {
  font-size: 16px;
  font-weight: bold;
  margin-left: 8px;
  color: #1a56b3;
}

.card-content {
  padding: 5px 16px 15px;
}

/* 设备列表卡片的内容区域特殊处理 */
.detail-card:nth-child(2) .card-content {
  padding: 8px 16px;
}

/* 信息项样式 */
.info-item {
  display: flex;
  margin-bottom: 10px;
  align-items: center;
  position: relative;
  border-bottom: 1px solid #f9f9f9;
  padding-bottom: 10px;
}

.info-item:last-child {
  margin-bottom: 0;
  border-bottom: none;
  padding-bottom: 0;
}

.info-label {
  width: 90px;
  color: #666;
  font-size: 14px;
  flex-shrink: 0;
  position: relative;
  padding-left: 10px;
}

.info-label::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 14px;
  background-color: #1a56b3;
  opacity: 0.6;
  border-radius: 1.5px;
}

.info-value {
  flex: 1;
  color: #333;
  font-size: 14px;
  word-break: break-all;
  font-weight: 500;
}

/* 状态标签 */
.status-tag {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 4px;
  color: #ffffff;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
}

.stats-container .status-tag {
  padding: 2px 8px;
  font-size: 14px;
  margin-bottom: 0;
}

.status-pending {
  background: linear-gradient(135deg, #e6a23c 0%, #f0b95a 100%);
}

.status-received {
  background: linear-gradient(135deg, #1a56b3 0%, #3a7bd5 100%);
}

.status-submitted {
  background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%);
}

.status-reviewed {
  background: linear-gradient(135deg, #606266 0%, #909399 100%);
}

.status-unknown {
  background: linear-gradient(135deg, #f56c6c 0%, #f78989 100%);
}

/* 状态横幅 */
.status-banner {
  padding: 15px 16px;
  color: #ffffff;
  position: relative;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.status-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-left {
  display: flex;
  align-items: center;
}

.status-icon {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
}

.status-text {
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0.5px;
}

/* 上方操作按钮 */
.action-buttons-top {
  display: flex;
  gap: 10px;
}

.action-button-top {
  background-color: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.4);
  color: #ffffff;
  font-size: 13px;
  padding: 4px 12px;
  border-radius: 15px;
  height: auto;
  line-height: 1.5;
  font-weight: normal;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.action-button-top:active {
  transform: scale(0.95);
  background-color: rgba(255, 255, 255, 0.3);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* 非负责人提示 */
.action-tip {
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 15px;
  padding: 4px 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.action-tip-text {
  color: #ffffff;
  font-size: 13px;
  opacity: 0.9;
}

/* 设备列表样式 */
.equipment-item {
  border-bottom: 1px solid #f0f0f0;
  position: relative;
  background-color: #ffffff;
  margin-bottom: 10px;
}

.equipment-item:last-child {
  margin-bottom: 0;
  border-bottom: none;
}

.equipment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f5f5f5;
}

.equipment-name {
  font-size: 15px;
  font-weight: bold;
  color: #333;
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  position: relative;
  padding-left: 12px;
}

.equipment-name::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 16px;
  background-color: #1a56b3;
  border-radius: 2px;
}

.equipment-type {
  background-color: #409eff;
  color: #ffffff;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 500;
}

/* 检查项样式 */
.inspection-items {
  padding: 5px 0;
  background-color: #ffffff;
}

.inspection-item {
  padding: 10px 0;
  border-bottom: 1px solid #f5f5f5;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0;
}

.inspection-item:last-child {
  margin-bottom: 0;
  border-bottom: none;
}

.inspection-item-header {
  display: flex;
  align-items: center;
  flex: 1;
}

.inspection-item-index {
  width: 20px;
  height: 20px;
  background-color: #1a56b3;
  color: #ffffff;
  border-radius: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 12px;
  margin-right: 10px;
  flex-shrink: 0;
  font-weight: bold;
}

.inspection-item-content {
  font-size: 14px;
  color: #333;
  flex: 1;
  line-height: 1.4;
}

.no-inspection-items {
  padding: 15px 0;
  text-align: center;
  color: #999;
  font-size: 13px;
}

.empty-equipment {
  text-align: center;
  padding: 20px 0;
  color: #999;
  font-size: 14px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 10px;
}

/* 检查结果第二行样式 */
.inspection-result-row {
  width: 100%;
  margin-top: 6px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-top: 1px solid #f0f0f0;
  background-color: #fafafa;
  border-radius: 4px;
  padding: 8px 12px;
  box-sizing: border-box;
}

/* 不合格项突出显示 */
.inspection-result-row.result-unqualified {
  background-color: #fef0f0;
  border-top: 1px solid #f5c6cb;
  border-left: 3px solid #f56c6c;
}

.result-main {
  display: flex;
  flex-direction: column;
  flex: 1;
  gap: 2px;
  min-width: 0; /* 防止flex子项溢出 */
}

.result-text {
  font-size: 12px;
  color: #333;
  font-weight: 500;
  line-height: 1.3;
  word-wrap: break-word;
}

/* 不合格文本突出显示 */
.result-text.unqualified-text {
  color: #f56c6c;
  font-weight: bold;
}

.result-time-text {
  font-size: 10px;
  color: #999;
  line-height: 1.2;
}

/* 紧凑图片样式 - 靠右显示 */
.result-photos-compact {
  display: flex;
  gap: 4px;
  margin-left: 12px;
  flex-shrink: 0;
  align-items: center;
}

.result-photo-small {
  width: 32px;
  height: 32px;
  border-radius: 4px;
  border: 1px solid #e0e0e0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}




</style>
