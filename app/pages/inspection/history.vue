<template>
  <view class="container">
    <!-- 顶部导航栏 -->
    <view class="header" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="header-content">
        <view class="back-icon" @click="goBack">
          <uni-icons type="left" size="18" color="#FFFFFF"></uni-icons>
        </view>
        <view class="header-title">巡检历史</view>
        <view class="header-right">
          <view class="refresh-icon" @click="refreshData">
            <uni-icons type="refresh" size="18" color="#FFFFFF"></uni-icons>
          </view>
        </view>
      </view>
    </view>

    <!-- 内容区域 -->
    <scroll-view scroll-y class="content" :style="{ paddingTop: (statusBarHeight + 44) + 'px' }">
      <!-- 统计信息 -->
      <view class="stats-container" v-if="statsData">
        <text class="stats-text">
          <text class="stats-item">已完成 <text class="stats-number">{{ statsData.completedCount || 0 }}</text></text>
          <text class="stats-divider">•</text>
          <text class="stats-item">已审核 <text class="stats-number">{{ statsData.reviewedCount || 0 }}</text></text>
          <text class="stats-divider">•</text>
          <text class="stats-item">总计 <text class="stats-number">{{ (statsData.completedCount || 0) + (statsData.reviewedCount || 0) }}</text></text>
        </text>
      </view>

      <!-- 搜索栏 -->
      <view class="search-container">
        <view class="search-box">
          <uni-icons type="search" size="16" color="#999999"></uni-icons>
          <input class="search-input" type="text" v-model="searchKeyword" placeholder="搜索任务名称" @input="handleSearch" />
          <uni-icons v-if="searchKeyword" type="clear" size="16" color="#999999" @click="clearSearch"></uni-icons>
        </view>
        <uni-data-select v-model="selectedStationValue" :localdata="stationOptions" placeholder="选择电站"
          @change="handleStationChange" class="station-select"></uni-data-select>
      </view>

      <!-- 巡检历史列表 -->
      <view class="history-list">
        <scroll-view scroll-y class="inspection-scroll">
          <view v-if="inspectionList.length > 0">
            <view
              class="inspection-card"
              v-for="(item, index) in inspectionList"
              :key="index"
              @click="goToDetail(item)"
            >
              <view class="card-status-indicator" :class="getStatusClass(item.status)"></view>
              <view class="card-content">
                <view class="card-header">
                  <text class="task-name">{{ item.inspectionName }}</text>
                  <view class="status-tag" :class="getStatusClass(item.status)">
                    <uni-icons :type="getStatusIcon(item.status)" size="12" color="#ffffff" class="status-icon"></uni-icons>
                    <text>{{ getStatusLabel(item.status) }}</text>
                  </view>
                </view>
                <view class="card-body">
                  <view class="info-row">
                    <view class="info-icon-container">
                      <uni-icons type="person-filled" size="14" color="#1a56b3" class="info-icon"></uni-icons>
                    </view>
                    <text class="info-label">负责人员:</text>
                    <text class="info-value">{{ item.assignChargeUserName || '未指定' }}</text>
                  </view>
                  <view class="info-row">
                    <view class="info-icon-container">
                      <uni-icons type="staff-filled" size="14" color="#1a56b3" class="info-icon"></uni-icons>
                    </view>
                    <text class="info-label">执行人员:</text>
                    <text class="info-value">{{ item.inspectorUserName || '未分配' }}</text>
                  </view>
                  <view class="info-row">
                    <view class="info-icon-container">
                      <uni-icons type="location-filled" size="14" color="#1a56b3" class="info-icon"></uni-icons>
                    </view>
                    <text class="info-label">巡检电站:</text>
                    <text class="info-value">{{ item.station ? item.station.stationName : '未知电站' }}</text>
                  </view>
                  <view class="info-row">
                    <view class="info-icon-container">
                      <uni-icons type="calendar-filled" size="14" color="#1a56b3" class="info-icon"></uni-icons>
                    </view>
                    <text class="info-label">完成时间:</text>
                    <text class="info-value">{{ formatDateTime(item.submitTime) }}</text>
                  </view>
                  <view class="info-row" v-if="item.reviewTime">
                    <view class="info-icon-container">
                      <uni-icons type="checkmarkempty" size="14" color="#1a56b3" class="info-icon"></uni-icons>
                    </view>
                    <text class="info-label">审核时间:</text>
                    <text class="info-value">{{ formatDateTime(item.reviewTime) }}</text>
                  </view>
                </view>

                <!-- 审核意见区域 -->
                <view class="review-comment-section" v-if="item.reviewComment && item.reviewComment.trim()">
                  <view class="review-comment-header">
                    <uni-icons type="chat-filled" size="12" color="#f39c12" class="comment-icon"></uni-icons>
                    <text class="comment-label">审核意见</text>
                  </view>
                  <view class="review-comment-content">
                    <text class="comment-text">{{ item.reviewComment }}</text>
                  </view>
                </view>
              </view>
            </view>
          </view>

          <!-- 使用LoadingContainer组件 -->
          <loading-container
            v-if="loading || inspectionList.length === 0"
            :loading="loading"
            :error="!loading && inspectionList.length === 0"
            loadingText="正在加载巡检历史..."
            errorText="暂无巡检历史记录"
            :showRetry="false"
            height="200"
          />
        </scroll-view>
      </view>
    </scroll-view>
  </view>
</template>

<script>
import { listInspection, getInspectionStats } from '@/api/system/inspection';
import { getDictData } from '@/api/system/dictData';
import { listStation } from '@/api/system/station';
import LoadingContainer from '@/components/LoadingContainer';

export default {
  name: 'InspectionHistory',
  components: {
    LoadingContainer
  },
  data() {
    return {
      statusBarHeight: 0,
      inspectionList: [],
      allInspectionList: [], // 保存所有数据用于搜索过滤
      statsData: null,
      loading: false,
      pageSize: 9999,
      userId: null,
      dictData: {},
      // 搜索相关
      searchKeyword: '',
      selectedStationValue: '',
      stationOptions: [],
      searchTimer: null
    }
  },
  onLoad() {
    // 设置状态栏高度
    this.statusBarHeight = uni.getSystemInfoSync().statusBarHeight;

    // 获取当前登录用户ID
    this.userId = this.$store.state.user.userId;

    this.getDicts();
  },
  methods: {
    // 获取字典数据
    async getDicts() {
      try {
        const response = await getDictData();
        this.dictData = this.$dict.parseDictData(response.data);

        // 获取站点数据
        await this.getStations();

        // 先获取历史数据，再计算统计信息
        await this.getInspectionHistory();
        this.getStats();
      } catch (error) {
        console.error('获取字典数据失败:', error);
        uni.showToast({
          title: '获取字典数据失败',
          icon: 'none'
        });
        this.loading = false;
      }
    },

    // 获取站点数据
    async getStations() {
      try {
        const response = await listStation();
        this.stationOptions = [
          { text: '全部电站', value: '' },
          ...response.data.map(item => ({
            text: item.stationName,
            value: item.stationId
          }))
        ];
      } catch (error) {
        console.error('获取站点数据失败:', error);
        this.stationOptions = [{ text: '全部电站', value: '' }];
      }
    },

    // 获取巡检历史列表
    getInspectionHistory() {
      this.loading = true;

      return new Promise((resolve, reject) => {
        const params = {
          pageSize: this.pageSize
        };

        // 确保 userId 是字符串类型
        const userId = String(this.userId);

        listInspection(params, userId).then(response => {
          // 过滤出已完成和已审核的任务，保存到allInspectionList
          this.allInspectionList = (response.rows || []).filter(item =>
            item.status === '4' || item.status === '6'
          );
          // 应用搜索过滤
          this.applyFilters();
          this.loading = false;
          resolve(response);
        }).catch(error => {
          console.error('获取巡检历史失败:', error);
          uni.showToast({
            title: '获取巡检历史失败',
            icon: 'none'
          });
          this.loading = false;
          reject(error);
        });
      });
    },

    // 获取统计信息
    async getStats() {
      try {
        const response = await getInspectionStats(this.userId);
        const allStats = response.data;

        // 基于历史列表计算统计信息
        const completedCount = this.inspectionList.filter(item => item.status === '4').length;
        const reviewedCount = this.inspectionList.filter(item => item.status === '6').length;

        this.statsData = {
          completedCount: completedCount,
          reviewedCount: reviewedCount,
          totalCount: allStats.totalCount || 0
        };
      } catch (error) {
        console.error('获取统计信息失败:', error);
        // 如果API失败，基于当前列表计算
        const completedCount = this.inspectionList.filter(item => item.status === '4').length;
        const reviewedCount = this.inspectionList.filter(item => item.status === '6').length;

        this.statsData = {
          completedCount: completedCount,
          reviewedCount: reviewedCount,
          totalCount: completedCount + reviewedCount
        };
      }
    },

    // 获取状态标签
    getStatusLabel(status) {
      if (!status) return '未知';
      return this.$dict.getDictLabel('inspection_status', status, '未知');
    },

    // 获取状态样式类
    getStatusClass(status) {
      const statusClassMap = {
        '4': 'status-submitted',  // 已完成
        '6': 'status-reviewed'    // 已审核
      };
      return statusClassMap[status] || 'status-unknown';
    },

    // 获取状态图标
    getStatusIcon(status) {
      const iconMap = {
        '4': 'checkbox-filled',     // 已完成 - 勾选
        '6': 'eye-filled'           // 已审核 - 查看
      };
      return iconMap[status] || 'help-filled';
    },

    // 格式化日期时间
    formatDateTime(dateString) {
      if (!dateString) return '未设置';
      const date = new Date(dateString);
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
    },

    // 应用搜索过滤
    applyFilters() {
      let filteredList = [...this.allInspectionList];

      // 按任务名称搜索
      if (this.searchKeyword) {
        filteredList = filteredList.filter(item =>
          item.inspectionName && item.inspectionName.toLowerCase().includes(this.searchKeyword.toLowerCase())
        );
      }

      // 按站点过滤
      if (this.selectedStationValue) {
        filteredList = filteredList.filter(item =>
          item.station && String(item.station.stationId) === String(this.selectedStationValue)
        );
      }

      this.inspectionList = filteredList;
    },

    // 处理搜索
    handleSearch() {
      clearTimeout(this.searchTimer);
      this.searchTimer = setTimeout(() => {
        this.applyFilters();
      }, 500);
    },

    // 清除搜索
    clearSearch() {
      this.searchKeyword = '';
      this.applyFilters();
    },

    // 处理站点变化
    handleStationChange(value) {
      this.selectedStationValue = value;
      this.applyFilters();
    },

    // 刷新数据
    async refreshData() {
      uni.showToast({
        title: '刷新中',
        icon: 'loading',
        duration: 500
      });

      try {
        await this.getInspectionHistory();
        this.getStats();
      } catch (error) {
        console.error('刷新数据失败:', error);
      }
    },

    // 跳转到详情页
    goToDetail(item) {
      uni.navigateTo({
        url: `/pages/inspection/detail?id=${item.inspectionId}`
      });
    },

    // 返回上一页
    goBack() {
      uni.navigateBack();
    }
  }
}
</script>
<style lang="scss" scoped>
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f7fa;
}

/* 顶部导航栏 */
.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: linear-gradient(135deg, #1a56b3 0%, #2d7dd2 100%);
  box-shadow: 0 2px 8px rgba(26, 86, 179, 0.15);
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 44px;
  padding: 0 15px;
}

.back-icon, .refresh-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 6px;
}

.header-title {
  font-size: 16px;
  font-weight: 600;
  color: #FFFFFF;
  letter-spacing: 0.5px;
}

.header-right {
  width: 32px;
}

/* 内容区域 */
.content {
  flex: 1;
  background-color: #f5f7fa;
}

/* 统计信息 */
.stats-container {
  padding: 8px 12px;
  margin: 0 12px 4px;
}

.stats-text {
  font-size: 12px;
  color: #495057;
  line-height: 1.2;
  font-weight: 500;
  letter-spacing: 0.3px;
}

.stats-item {
  color: #495057;
}

.stats-number {
  color: #1a56b3;
  font-weight: 600;
  margin-left: 2px;
}

.stats-divider {
  color: #adb5bd;
  margin: 0 8px;
  font-weight: 400;
}

/* 搜索栏 */
.search-container {
  display: flex;
  align-items: center;
  padding: 6px 15px;
  margin-bottom: 6px;
  gap: 10px;
}

.search-box {
  display: flex;
  align-items: center;
  background-color: #f8f9fa;
  border-radius: 16px;
  padding: 6px 10px;
  flex: 1;
  min-width: 0;
}

.search-input {
  flex: 1;
  height: 20px;
  margin: 0 6px;
  font-size: 13px;
  color: #333333;
  min-width: 0;
}

.station-select {
  width: 100px;
  flex-shrink: 0;
}

/* 历史列表 */
.history-list {
  flex: 1;
  padding: 0 15px 15px;
}

.inspection-scroll {
  flex: 1;
  height: calc(100vh - (var(--status-bar-height) + 44px + 80px));
}

/* 优化的卡片样式 */
.inspection-card {
  display: flex;
  margin-bottom: 12px;
  background-color: #ffffff;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
  overflow: hidden;
  position: relative;
  border: 1px solid #f0f4fa;
  transition: transform 0.2s, box-shadow 0.2s;
}

.card-status-indicator {
  width: 4px;
  flex-shrink: 0;
}

.card-content {
  flex-grow: 1;
  padding: 15px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f4fa;
}

.task-name {
  width: 60px;
  font-size: 15px;
  font-weight: 600;
  color: #1a1a1a;
  line-height: 1.3;
  flex: 1;
  margin-right: 10px;
  letter-spacing: 0.2px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.status-tag {
  display: flex;
  align-items: center;
  padding: 3px 8px;
  border-radius: 12px;
  font-size: 11px;
  color: #ffffff;
  font-weight: 500;
  white-space: nowrap;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12);
}

.status-icon {
  margin-right: 3px;
}

.card-body {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.info-row {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #555555;
}

.info-icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 22px;
  height: 22px;
  border-radius: 6px;
  background-color: #f0f4fa;
  margin-right: 8px;
}

.info-label {
  color: #666666;
  margin-right: 6px;
  white-space: nowrap;
  font-weight: 500;
  min-width: 60px;
}

.info-value {
  color: #333333;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
  font-weight: 400;
}

/* 审核意见区域样式 */
.review-comment-section {
  margin-top: 12px;
  padding: 10px;
  background-color: #fff8e1;
  border-radius: 8px;
  border-left: 3px solid #f39c12;
}

.review-comment-header {
  display: flex;
  align-items: center;
  margin-bottom: 6px;
}

.comment-icon {
  margin-right: 6px;
}

.comment-label {
  font-size: 12px;
  font-weight: 600;
  color: #f39c12;
  letter-spacing: 0.3px;
}

.review-comment-content {
  padding-left: 18px;
}

.comment-text {
  font-size: 12px;
  color: #333333;
  line-height: 1.4;
  word-wrap: break-word;
  word-break: break-all;
}

/* 状态颜色定义 */
/* 已完成/已提交 - 绿色 */
.status-submitted {
  background-color: #2ecc71;
}
.inspection-card .card-status-indicator.status-submitted {
  background-color: #2ecc71;
}

/* 已审核 - 浅灰色 */
.status-reviewed {
  background-color: #95a5a6;
}
.inspection-card .card-status-indicator.status-reviewed {
  background-color: #95a5a6;
}

/* 未知状态 - 灰色 */
.status-unknown {
  background-color: #7f8c8d;
}
.inspection-card .card-status-indicator.status-unknown {
  background-color: #7f8c8d;
}
</style>
