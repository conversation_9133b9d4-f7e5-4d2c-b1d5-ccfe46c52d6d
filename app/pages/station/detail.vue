<template>
  <view class="container">
    <!-- 顶部导航栏 -->
    <view class="header">
      <view class="header-content">
        <view class="back-icon" @click="goBack">
          <uni-icons type="back" size="22" color="#ffffff"></uni-icons>
        </view>
        <text class="header-title">电站详情</text>
        <view class="header-right" @click="goToEquipments">
          <uni-icons type="gear" size="18" color="#ffffff"></uni-icons>
          <text class="header-right-text">设备</text>
        </view>
      </view>
    </view>

    <!-- 内容区域 -->
    <scroll-view scroll-y class="content-scroll">
      <view v-if="stationInfo">
        <!-- 电站信息 -->
        <view class="station-header">
          <image-preview :src="stationInfo.imagesStr" mode="aspectFill" class="station-image"
            @error="handleImageError"></image-preview>
          <view class="station-info-overlay">
            <text class="station-name">{{ stationInfo.name }}</text>
            <text class="station-address">{{ formatDistrict() }}</text>
            <view class="station-type-tag" v-if="stationInfo.type">{{ stationInfo.type }}</view>
            <view class="station-type-tag" v-if="stationInfo.stationType">{{ stationInfo.stationType }}</view>
            <view class="station-type-tag" v-if="stationInfo.gridType">{{ stationInfo.gridType }}</view>
          </view>
        </view>

        <!-- 基本信息 -->
        <uni-collapse class="detail-card" v-model="collapseValue" @change="onCollapseChange">
          <uni-collapse-item name="basicInfo" :show-arrow="false">
            <template #title>
              <view class="card-header-custom">
                <view class="card-header-left">
                  <uni-icons type="info-filled" size="16" color="#2979ff"></uni-icons>
                  <text class="card-title">基本信息</text>
                </view>
                <view class="card-header-right">
                  <text class="collapse-text" :class="{ 'expanded': isBasicInfoExpanded }">
                    {{ isBasicInfoExpanded ? '收起' : '展开' }}
                  </text>
                  <view class="collapse-arrow" :class="{ 'expanded': isBasicInfoExpanded }">
                    <uni-icons type="bottom" size="16" color="#2979ff"></uni-icons>
                  </view>
                </view>
              </view>
            </template>
            <view class="card-content">
              <view class="info-item">
                <text class="info-label">直流侧容量</text>
                <text class="info-value">{{ stationInfo.directCurrent }} MW</text>
              </view>
              <view class="info-item">
                <text class="info-label">交流侧容量</text>
                <text class="info-value">{{ stationInfo.capacity }} MW</text>
              </view>
              <view class="info-item">
                <text class="info-label">并网时间</text>
                <text class="info-value">{{ stationInfo.networkTime }}</text>
              </view>
              <view class="info-item" v-if="stationInfo.priceType">
                <text class="info-label">电价方式</text>
                <text class="info-value">{{ stationInfo.priceType }}</text>
              </view>
              <view class="info-item" v-if="stationInfo.contact && stationInfo.contact.length > 0">
                <text class="info-label">联系信息</text>
                <view class="contact-info">
                  <view class="contact-item" v-for="(contact, index) in stationInfo.contact" :key="index">
                    <text class="contact-name">{{ contact.name }}</text>
                    <text class="contact-phone">{{ contact.phone }}</text>
                  </view>
                </view>
              </view>
            </view>
          </uni-collapse-item>
        </uni-collapse>

        <!-- 发电统计 -->
        <power-generation-stats :stationId="stationId" />

        <!-- 巡检记录 -->
        <view class="detail-card">
          <view class="card-header">
            <uni-icons type="calendar" size="16" color="#2979ff"></uni-icons>
            <text class="card-title">巡检记录</text>
          </view>
          <view class="card-content">
            <view v-if="inspectionHistoryLoading" class="loading-container">
              <text class="loading-text">加载中...</text>
            </view>
            <view v-else-if="inspectionHistory.length > 0" class="inspection-history-list">
              <view
                class="inspection-history-item"
                v-for="(item, index) in inspectionHistory"
                :key="index"
                @click="goToInspectionDetail(item)"
              >
                <view class="history-item-header">
                  <text class="inspection-name">{{ item.inspectionName }}</text>
                  <view class="status-tag" :class="getStatusClass(item.status)">
                    <text class="status-text">{{ getStatusLabel(item.status) }}</text>
                  </view>
                </view>
                <view class="history-item-info">
                  <text class="inspection-time">{{ formatTime(item.submitTime || item.createTime) }}</text>
                </view>
              </view>
            </view>
            <view v-else class="empty-state">
              <uni-icons type="info" size="24" color="#cccccc"></uni-icons>
              <text class="empty-text">暂无巡检记录</text>
            </view>
          </view>
        </view>
      </view>

      <view v-else-if="loading" class="loading-container">
        <text>加载中...</text>
      </view>
    </scroll-view>
  </view>
</template>

<script>

import { getDictData } from '@/api/system/dictData'
import { getStation } from '@/api/system/station'
import { listInspection } from '@/api/system/inspection'
import { getImageUrl, getImageList } from '@/utils/imageUtils'
import ImagePreview from '@/components/ImagePreview/index.vue'
import PowerGenerationStats from '@/components/PowerGenerationStats.vue'
import { listInspectionByStationId } from "../../api/system/inspection";

export default {
  components: {
    ImagePreview,
    PowerGenerationStats
  },
  data() {
    return {
      // 字典数据
      dictData: {},
      stationId: null,
      stationInfo: null,
      loading: true,
      defaultImage: '/static/images/solar-power-station.png',
      // 巡检记录相关
      inspectionHistory: [],
      inspectionHistoryLoading: false,
      userId: null,
      // 折叠组件相关
      collapseValue: [], // 默认折叠状态
      isBasicInfoExpanded: false
    }
  },
  onLoad(options) {
    // 设置状态栏高度
    this.statusBarHeight = uni.getSystemInfoSync().statusBarHeight;

    if (options.id) {
      this.stationId = options.id;
      this.getStationDicts();
    }
  },
  methods: {
    // 获取电站字典数据
    async getStationDicts() {
      try {
        const response = await getDictData();
        // 使用字典插件解析字典数据
        this.dictData = this.$dict.parseDictData(response.data);
        // 获取电站详情数据
        this.getStationDetail();
      } catch (error) {
        console.error('获取电站字典数据失败:', error);
        uni.showToast({
          title: '获取字典数据失败',
          icon: 'none'
        });
      }
    },

    async getStationDetail() {
      try {
        const response = await getStation(this.stationId);
        this.stationInfo = {
          id: response.data.stationId,
          name: response.data.stationName,
          address: response.data.address,
          directCurrent: response.data.dcCapacity,
          capacity: response.data.acCapacity,
          type: response.data.ownerType,
          stationType: response.data.stationType,
          imagesStr: response.data.images || '',
          images: response.data.images ? getImageList(response.data.images) : [],
          networkTime: response.data.gridTime,
          gridType: response.data.gridType,
          priceType: response.data.priceType,
          contact: response.data.contact ? JSON.parse(response.data.contact) : [],
          district: response.data.district ? JSON.parse(response.data.district) : []
        };
        // 对字典数据进行转换
        this.stationInfo = {
          ...this.stationInfo,
          // 转换投资类型的值为标签
          type: this.$dict.getDictLabel('station_owner_type', this.stationInfo.type, this.stationInfo.type),
          // 转换电站类型的值为标签
          stationType: this.$dict.getDictLabel('station_type', this.stationInfo.stationType, this.stationInfo.stationType),
          // 转换并网类型的值为标签
          gridType: this.$dict.getDictLabel('station_grid_type', this.stationInfo.gridType, this.stationInfo.gridType),
        };

        // 获取巡检记录
        this.getInspectionHistory();

      } catch (error) {
        console.error('获取电站详情失败:', error);
        uni.showToast({
          title: '获取电站详情失败',
          icon: 'none'
        });
      } finally {
        this.loading = false;
      }
    },

    // 获取巡检记录
    async getInspectionHistory() {
      this.inspectionHistoryLoading = true;
      try {
        const params = {
          stationId: this.stationId,
          pageSize: 9999 // 获取所有记录
        };

        const response = await listInspectionByStationId(params);

        // 过滤出已完成和已审核的任务，并按时间倒序排列
        this.inspectionHistory = (response.rows || [])
          .sort((a, b) => {
            const timeA = new Date(b.submitTime || b.createTime);
            const timeB = new Date(a.submitTime || a.createTime);
            return timeA - timeB;
          });

      } catch (error) {
        console.error('获取巡检记录失败:', error);
        this.inspectionHistory = [];
      } finally {
        this.inspectionHistoryLoading = false;
      }
    },

    // 格式化地区显示
    formatDistrict() {
      return this.stationInfo.address || '暂无地址';
    },

    goBack() {
      uni.navigateBack();
    },
    goToMonitor() {
      uni.navigateTo({
        url: `/pages/monitor/dashboard?stationId=${this.stationId}`
      })
    },
    goToInspectionDetail(item) {
      uni.navigateTo({
        url: `/pages/inspection/detail?id=${item.inspectionId}`
      });
    },
    goToEquipments() {
      uni.navigateTo({
        url: `/pages/equipment/list?stationId=${this.stationId}`
      })
    },
    goToAnalysis() {
      uni.navigateTo({
        url: `/pages/analysis/index?stationId=${this.stationId}`
      })
    },
    handleImageError() {
      if (this.stationInfo) {
        this.stationInfo.images = [this.defaultImage];
      }
    },

    // 获取状态样式类
    getStatusClass(status) {
      switch (status) {
        case '0': return 'status-pending';
        case '2': return 'status-received';
        case '4': return 'status-completed';
        case '6': return 'status-reviewed';
        default: return 'status-pending';
      }
    },

    // 获取状态标签
    getStatusLabel(status) {
      switch (status) {
        case '0': return '待接收';
        case '2': return '进行中';
        case '4': return '已完成';
        case '6': return '已审核';
        default: return '未知';
      }
    },

    // 格式化时间
    formatTime(timeStr) {
      if (!timeStr) return '';
      const date = new Date(timeStr);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },

    // 监听折叠状态变化
    onCollapseChange(activeNames) {
      this.isBasicInfoExpanded = activeNames.includes('basicInfo');
    }
  },

  // 添加监听器
  watch: {
    collapseValue(newVal) {
      this.isBasicInfoExpanded = newVal.includes('basicInfo');
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}

/* 顶部导航栏 */
.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background-color: #2979ff;
  padding: 10px 15px;
  padding-top: var(--status-bar-height);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.back-icon {
  padding: 5px;
}

.header-title {
  color: #ffffff;
  font-size: 18px;
  font-weight: bold;
  padding-left: 20px;
}

.header-right {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding: 5px;
  cursor: pointer;
}

.header-right-text {
  color: #ffffff;
  font-size: 14px;
  margin-left: 2px;
  font-weight: 500;
}

/* 内容区域 */
.content-scroll {
  flex: 1;
  margin-top: calc(var(--status-bar-height) + 42px);
  height: calc(100vh - (var(--status-bar-height) + 42px));
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
}

/* 电站基本信息 */
.station-header {
  position: relative;
  width: 100%;
  height: 200px;
}

.station-image {
  width: 100%;
  height: 100%;
}

.station-info-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 15px;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent);
}

.station-name {
  font-size: 18px;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 5px;
  display: block;
}

.station-address {
  font-size: 14px;
  color: #f0f0f0;
  display: block;
  margin-bottom: 5px;
}

.station-type-tag {
  display: inline-block;
  padding: 4px 8px;
  margin-right: 6px;
  background-color: rgba(41, 121, 255, 0.8);
  color: #ffffff;
  font-size: 12px;
  border-radius: 4px;
}

/* 详细信息卡片 */
.detail-card {
  background-color: #ffffff;
  margin-bottom: 10px;
  position: relative;
}

.card-header {
  display: flex;
  align-items: center;
  padding: 10px 16px;
  position: relative;
}

.card-title {
  font-size: 15px;
  font-weight: bold;
  margin-left: 6px;
  color: #2979ff;
}

.card-content {
  padding: 2px 16px 10px;
}

/* 信息项样式 */
.info-item {
  display: flex;
  margin-bottom: 8px;
  align-items: center;
  position: relative;
  border-bottom: 1px solid #f9f9f9;
  padding-bottom: 8px;
}

.info-item:last-child {
  margin-bottom: 0;
  border-bottom: none;
  padding-bottom: 0;
}

.info-label {
  width: 80px;
  color: #666;
  font-size: 13px;
  flex-shrink: 0;
  position: relative;
  padding-left: 8px;
}

.info-label::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 2px;
  height: 12px;
  background-color: #2979ff;
  opacity: 0.6;
  border-radius: 1px;
}

.info-value {
  flex: 1;
  color: #333;
  font-size: 13px;
  word-break: break-all;
  font-weight: 500;
}

/* 联系信息样式 */
.contact-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.contact-item {
  display: flex;
  flex-direction: row;
  align-items: flex-end;
  margin-bottom: 4px;

  &:last-child {
    margin-bottom: 0;
  }

  .contact-name {
    font-size: 14px;
    color: #333;
    margin-right: 4px;
    font-weight: 500;
  }

  .contact-phone {
    font-size: 14px;
    color: #2979ff;
    font-weight: 500;
  }
}

/* 巡检记录样式 */
.inspection-history-list {
  margin-top: 5px;
}

.inspection-history-item {
  padding: 12px 0;
  border-bottom: 1px solid #f5f5f5;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.inspection-history-item:last-child {
  border-bottom: none;
}

.inspection-history-item:active {
  background-color: #f8f9fa;
}

.history-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}

.inspection-name {
  font-size: 14px;
  color: #333;
  font-weight: 500;
  flex: 1;
  margin-right: 10px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.status-tag {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  flex-shrink: 0;
  color: #ffffff;
}

.status-tag.status-pending {
  background: linear-gradient(135deg, #e6a23c 0%, #f0b95a 100%);
}

.status-tag.status-received {
  background: linear-gradient(135deg, #1a56b3 0%, #3a7bd5 100%);
}

.status-tag.status-completed {
  background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%);
}

.status-tag.status-reviewed {
  background: linear-gradient(135deg, #606266 0%, #909399 100%);
}

.status-text {
  font-size: 12px;
}

.history-item-info {
  display: flex;
  align-items: center;
}

.inspection-time {
  font-size: 12px;
  color: #999;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
}

.empty-text {
  font-size: 14px;
  color: #999;
  margin-top: 8px;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
}

.loading-text {
  font-size: 14px;
  color: #999;
}

/* 折叠组件自定义样式 */
.detail-card.uni-collapse {
  background-color: #ffffff;
  margin-bottom: 10px;
  position: relative;
}

/* 重写uni-collapse-item的标题样式 */
.detail-card ::v-deep .uni-collapse-item__title {
  background-color: #ffffff;
  border-bottom: none;
}

.detail-card ::v-deep .uni-collapse-item__title-box {
  padding: 0;
  height: auto;
  background-color: transparent;
}

.detail-card ::v-deep .uni-collapse-item__wrap-content {
  background-color: #ffffff;
}

/* 自定义卡片头部样式 */
.detail-card .card-header-custom {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  position: relative;
  width: 100%;
  background-color: #ffffff;
  border-radius: 8px 8px 0 0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.detail-card .card-header-left {
  display: flex;
  align-items: center;
}

.detail-card .card-title {
  font-size: 15px;
  font-weight: bold;
  margin-left: 6px;
  color: #2979ff;
}

.detail-card .card-header-right {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.detail-card .collapse-text {
  font-size: 12px;
  color: #2979ff;
  font-weight: 500;
  transition: all 0.3s ease;
  border-radius: 12px;
}

.detail-card .collapse-arrow {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  transition: all 0.3s ease;
  transform: rotate(0deg);
}

.detail-card .collapse-arrow.expanded {
  transform: rotate(-180deg);
}
</style>
