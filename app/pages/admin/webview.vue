<template>
  <view class="container">
    <!-- 顶部导航栏 -->
    <view class="header" :class="{ 'fullscreen-hidden': isFullscreen }" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="header-content">
        <view class="back-icon" @click="goBack">
          <uni-icons type="left" size="18" color="#FFFFFF"></uni-icons>
        </view>
        <view class="header-title">管理后台</view>
        <view class="header-right">
          <view class="fullscreen-icon" @click="toggleFullscreen" v-if="isLandscape">
            <uni-icons :type="isFullscreen ? 'minus' : 'plus'" size="18" color="#FFFFFF"></uni-icons>
          </view>
          <view class="refresh-icon" @click="refreshWebview">
            <uni-icons type="refresh" size="18" color="#FFFFFF"></uni-icons>
          </view>
        </view>
      </view>
    </view>

    <!-- 网页内容区域 -->
    <view class="webview-container" :style="{ paddingTop: isFullscreen ? '0px' : (statusBarHeight + (isLandscape ? 36 : 44)) + 'px' }" :class="{ 'fullscreen': isFullscreen }">
      <web-view 
        v-if="webviewUrl" 
        :src="webviewUrl" 
        @message="handleMessage"
        @error="handleError"
        @load="handleLoad"
        class="webview"
      ></web-view>
      
      <!-- 加载状态 -->
      <view v-if="loading" class="loading-container">
        <uni-icons type="spinner-cycle" size="24" color="#2979ff" class="loading-icon"></uni-icons>
        <text class="loading-text">正在加载管理后台...</text>
      </view>
      
      <!-- 错误状态 -->
      <view v-if="error" class="error-container">
        <uni-icons type="closeempty" size="48" color="#ff6b6b"></uni-icons>
        <text class="error-title">加载失败</text>
        <text class="error-message">{{ errorMessage }}</text>
        <button class="retry-button" @click="retryLoad">重新加载</button>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      statusBarHeight: 0,
      webviewUrl: '',
      loading: true,
      error: false,
      errorMessage: '',
      isLandscape: false,
      windowWidth: 0,
      windowHeight: 0,
      isFullscreen: false
    }
  },
  onLoad(options) {
    // 设置状态栏高度
    this.statusBarHeight = uni.getSystemInfoSync().statusBarHeight;

    // 获取屏幕信息
    this.updateScreenInfo();

    // 获取传入的URL参数
    if (options.url) {
      this.webviewUrl = decodeURIComponent(options.url);
    } else {
      this.error = true;
      this.errorMessage = '未提供有效的网页地址';
      this.loading = false;
    }
  },
  onShow() {
    // 页面显示时更新屏幕信息
    this.updateScreenInfo();
  },
  onResize() {
    // 屏幕旋转时更新屏幕信息
    this.updateScreenInfo();
  },
  methods: {
    // 更新屏幕信息
    updateScreenInfo() {
      const systemInfo = uni.getSystemInfoSync();
      this.windowWidth = systemInfo.windowWidth;
      this.windowHeight = systemInfo.windowHeight;
      this.isLandscape = systemInfo.windowWidth > systemInfo.windowHeight;

      // 横屏时自动退出全屏模式，竖屏时也退出全屏
      if (!this.isLandscape && this.isFullscreen) {
        this.isFullscreen = false;
      }
    },

    // 切换全屏模式
    toggleFullscreen() {
      this.isFullscreen = !this.isFullscreen;

      // 可以在这里添加状态栏的显示/隐藏逻辑
      if (this.isFullscreen) {
        // 进入全屏模式
        uni.hideNavigationBarLoading();
      } else {
        // 退出全屏模式
      }
    },

    // 返回上一页
    goBack() {
      uni.navigateBack();
    },

    // 刷新网页
    refreshWebview() {
      this.loading = true;
      this.error = false;
      this.errorMessage = '';

      // 重新设置webview的src来触发刷新
      const currentUrl = this.webviewUrl;
      this.webviewUrl = '';
      this.$nextTick(() => {
        this.webviewUrl = currentUrl;
      });
    },

    // 重新加载
    retryLoad() {
      this.refreshWebview();
    },

    // 网页加载完成
    handleLoad() {
      this.loading = false;
      this.error = false;
    },

    // 网页加载错误
    handleError(e) {
      console.error('网页加载错误:', e);
      this.loading = false;
      this.error = true;
      this.errorMessage = '网页加载失败，请检查网络连接或稍后重试';
    },

    // 处理网页消息
    handleMessage(e) {
      console.log('收到网页消息:', e.detail.data);
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}

/* 顶部导航栏 */
.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background-color: #2979ff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.header.fullscreen-hidden {
  transform: translateY(-100%);
  opacity: 0;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 44px;
  padding: 0 15px;
  transition: all 0.3s ease;
}

.back-icon, .refresh-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.back-icon:active, .refresh-icon:active {
  background-color: rgba(255, 255, 255, 0.1);
}

.header-title {
  color: #ffffff;
  font-size: 18px;
  font-weight: bold;
  text-align: center;
  flex: 1;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.fullscreen-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.fullscreen-icon:active {
  background-color: rgba(255, 255, 255, 0.1);
}

/* 网页容器 */
.webview-container {
  flex: 1;
  position: relative;
  background-color: #ffffff;
  transition: padding-top 0.3s ease;
}

.webview-container.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
  padding-top: 0 !important;
}

.webview {
  width: 100%;
  height: 100%;
}

/* 加载状态 */
.loading-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.loading-icon {
  animation: spin 1s linear infinite;
  margin-bottom: 12px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 14px;
  color: #666;
  text-align: center;
}

/* 错误状态 */
.error-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  text-align: center;
}

.error-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin: 16px 0 8px;
}

.error-message {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
  margin-bottom: 20px;
  max-width: 280px;
}

.retry-button {
  background-color: #2979ff;
  color: #ffffff;
  border: none;
  border-radius: 6px;
  padding: 10px 20px;
  font-size: 14px;
  font-weight: 500;
  transition: background-color 0.2s;
}

.retry-button:active {
  background-color: #1976d2;
}

/* 横屏适配 */
@media screen and (orientation: landscape) {
  .header {
    z-index: 1001; /* 确保在全屏模式下也能显示 */
  }

  .header.fullscreen-hidden {
    transform: translateY(-100%);
    transition: transform 0.3s ease;
  }

  .header-content {
    height: 36px; /* 横屏时稍微降低导航栏高度 */
    padding: 0 12px;
  }

  .header-title {
    font-size: 16px;
  }

  .back-icon, .refresh-icon, .fullscreen-icon {
    width: 28px;
    height: 28px;
  }

  /* 横屏时优化按钮间距 */
  .header-right {
    gap: 6px;
  }
}

/* 适配小屏幕 */
@media (max-width: 375px) {
  .header-content {
    padding: 0 12px;
  }

  .header-title {
    font-size: 16px;
  }

  .error-message {
    font-size: 13px;
    max-width: 260px;
  }
}

/* 大屏幕横屏优化 */
@media screen and (min-width: 768px) and (orientation: landscape) {
  .header-content {
    height: 48px;
    padding: 0 20px;
  }

  .header-title {
    font-size: 20px;
  }

  .back-icon, .refresh-icon {
    width: 36px;
    height: 36px;
  }
}
</style>
