<template>
  <view class="business-login-container">
    <!-- 品牌区域 -->
    <view class="brand-section">
      <view class="brand-logo">
        <image src="/static/dfe-logo.svg" class="logo-icon" mode="aspectFit"></image>
      </view>
      <view class="brand-title">E1030新能源智慧运维系统</view>
      <view class="brand-subtitle">专业 · 智能 · 高效</view>
    </view>

    <!-- 登录表单区域 -->
    <view class="login-form-section">
      <view class="form-title">账户登录</view>

      <view class="form-container">
        <view class="input-group">
          <view class="input-wrapper">
            <view class="input-icon">
              <view class="iconfont icon-user"></view>
            </view>
            <input
              v-model="loginForm.username"
              class="form-input"
              type="text"
              placeholder="请输入账号"
              maxlength="30"
              placeholder-class="input-placeholder"
            />
          </view>
        </view>

        <view class="input-group">
          <view class="input-wrapper">
            <view class="input-icon">
              <view class="iconfont icon-password"></view>
            </view>
            <input
              v-model="loginForm.password"
              type="password"
              class="form-input"
              placeholder="请输入密码"
              maxlength="20"
              placeholder-class="input-placeholder"
            />
          </view>
        </view>

        <view class="input-group captcha-group" v-if="captchaEnabled">
          <view class="input-wrapper captcha-wrapper">
            <view class="input-icon">
              <view class="iconfont icon-code"></view>
            </view>
            <input
              v-model="loginForm.code"
              type="number"
              class="form-input captcha-input"
              placeholder="验证码"
              maxlength="4"
              placeholder-class="input-placeholder"
            />
            <view class="captcha-image" @click="getCode">
              <image :src="codeUrl" class="captcha-img" mode="aspectFit"></image>
            </view>
          </view>
        </view>

        <view class="login-actions">
          <button @click="handleLogin" class="login-button">
            <text class="button-text">立即登录</text>
          </button>
        </view>

        <view class="register-link" v-if="register">
          <text class="link-text">还没有账号？</text>
          <text @click="handleUserRegister" class="link-action">立即注册</text>
        </view>
      </view>

      <!-- 系统说明区域 -->
      <view class="system-info-section">
        <view class="system-footer">
          <text class="footer-text">© 2025 E1030新能源智慧运维系统</text>
          <text class="footer-version">Version 1.0.0</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
  import { getCodeImg } from '@/api/login'

  export default {
    data() {
      return {
        codeUrl: "",
        captchaEnabled: false,
        // 用户注册开关
        register: false,
        globalConfig: getApp().globalData.config,
        loginForm: {
          username: "",
          password: "",
          code: "",
          uuid: ""
        }
      }
    },
    created() {
      this.getCode()
      this.loadLastLoginInfo()
    },
    methods: {
      // 用户注册
      handleUserRegister() {
        this.$tab.redirectTo(`/pages/register`)
      },
      // 加载上次登录信息
      loadLastLoginInfo() {
        const lastUsername = this.$store.getters.lastUsername
        const lastPassword = this.$store.getters.lastPassword
        if (lastUsername) {
          this.loginForm.username = lastUsername
        }
        if (lastPassword) {
          this.loginForm.password = lastPassword
        }
      },
      // 获取图形验证码
      getCode() {
        getCodeImg().then(res => {
          this.captchaEnabled = res.captchaEnabled === undefined ? true : res.captchaEnabled
          if (this.captchaEnabled) {
            this.codeUrl = 'data:image/gif;base64,' + res.img
            this.loginForm.uuid = res.uuid
          }
        })
      },
      // 登录方法
      async handleLogin() {
        if (this.loginForm.username === "") {
          this.$modal.msgError("请输入账号")
        } else if (this.loginForm.password === "") {
          this.$modal.msgError("请输入密码")
        } else if (this.loginForm.code === "" && this.captchaEnabled) {
          this.$modal.msgError("请输入验证码")
        } else {
          this.$modal.loading("登录中，请耐心等待...")
          this.pwdLogin()
        }
      },
      // 密码登录
      async pwdLogin() {
        this.$store.dispatch('Login', this.loginForm).then(() => {
          this.$modal.closeLoading()
          this.loginSuccess()
        }).catch(() => {
          if (this.captchaEnabled) {
            this.getCode()
          }
        })
      },
      // 登录成功后，处理函数
      loginSuccess(result) {
        // 设置用户信息
        this.$store.dispatch('GetInfo').then(res => {
          this.$tab.reLaunch('/pages/index')
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
  @import '@/uni_modules/uni-scss/index.scss';

  page {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    height: 100vh;
  }

  .business-login-container {
    width: 100%;
    height: 100vh;
    background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 50%, #1e40af 100%);
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: hidden;

    // 添加装饰性背景元素
    &::before {
      content: '';
      position: absolute;
      top: -50%;
      right: -20%;
      width: 200%;
      height: 200%;
      background: radial-gradient(circle, rgba(255, 255, 255, 0.08) 0%, transparent 70%);
      transform: rotate(-15deg);
      z-index: 1;
      animation: float 6s ease-in-out infinite;
    }

    &::after {
      content: '';
      position: absolute;
      bottom: -30%;
      left: -10%;
      width: 150%;
      height: 150%;
      background: radial-gradient(circle, rgba(59, 130, 246, 0.2) 0%, transparent 60%);
      z-index: 1;
      animation: float 8s ease-in-out infinite reverse;
    }
  }

  @keyframes float {
    0%, 100% {
      transform: rotate(-15deg) translateY(0px);
    }
    50% {
      transform: rotate(-15deg) translateY(-20px);
    }
  }

  .brand-section {
    flex: 1.08;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60rpx 40rpx 40rpx;
    z-index: 2;
    position: relative;

    .brand-logo {
      margin-bottom: 40rpx;
      animation: fadeInDown 1s ease-out;

      .logo-icon {
        width: 100rpx;
        height: 100rpx;
        filter: brightness(0) invert(1);
      }
    }

    .brand-title {
      font-size: 48rpx;
      font-weight: 600;
      color: #ffffff;
      text-align: center;
      margin-bottom: 20rpx;
      letter-spacing: 2rpx;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    .brand-subtitle {
      font-size: 28rpx;
      color: rgba(255, 255, 255, 0.8);
      text-align: center;
      letter-spacing: 4rpx;
      font-weight: 300;
    }
  }

  .login-form-section {
    background: #ffffff;
    border-radius: 40rpx 40rpx 0 0;
    padding: 50rpx 40rpx 30rpx;
    z-index: 2;
    position: relative;
    box-shadow: 0 -10px 30px rgba(0, 0, 0, 0.1);
    flex: 0.92;

    .form-title {
      font-size: 44rpx;
      font-weight: 600;
      color: #1f2937;
      text-align: center;
      margin-bottom: 60rpx;
      letter-spacing: 1rpx;
    }
  }

  .form-container {
    .input-group {
      margin-bottom: 40rpx;

      &.captcha-group {
        margin-bottom: 50rpx;
      }
    }

    .input-wrapper {
      position: relative;
      display: flex;
      align-items: center;
      background: #f8fafc;
      border: 2rpx solid #e2e8f0;
      border-radius: 16rpx;
      transition: all 0.3s ease;

      &:focus-within {
        border-color: #3b82f6;
        background: #ffffff;
        box-shadow: 0 0 0 6rpx rgba(59, 130, 246, 0.1);
      }

      &.captcha-wrapper {
        .form-input {
          flex: 1;
        }
      }
    }

    .input-icon {
      padding: 0 24rpx;
      display: flex;
      align-items: center;
      justify-content: center;

      .iconfont {
        font-size: 36rpx;
        color: #64748b;
        transition: color 0.3s ease;
      }
    }

    .input-wrapper:focus-within .input-icon .iconfont {
      color: #3b82f6;
    }

    .form-input {
      flex: 1;
      height: 96rpx;
      font-size: 32rpx;
      color: #1f2937;
      background: transparent;
      border: none;
      outline: none;
      padding: 0 24rpx 0 0;

      &.captcha-input {
        padding-right: 200rpx;
      }
    }

    .input-placeholder {
      color: #9ca3af;
      font-size: 30rpx;
    }

    .captcha-image {
      position: absolute;
      right: 16rpx;
      top: 50%;
      transform: translateY(-50%);
      width: 160rpx;
      height: 64rpx;
      border-radius: 8rpx;
      overflow: hidden;
      border: 1rpx solid #e2e8f0;
      cursor: pointer;
      transition: all 0.3s ease;

      &:active {
        transform: translateY(-50%) scale(0.95);
      }

      .captcha-img {
        width: 100%;
        height: 100%;
      }
    }
  }

  .login-actions {
    margin-top: 60rpx;
    margin-bottom: 40rpx;

    .login-button {
      width: 100%;
      height: 96rpx;
      background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
      border: none;
      border-radius: 16rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;
      box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);

      &:active {
        transform: translateY(2rpx);
        box-shadow: 0 2px 8px rgba(59, 130, 246, 0.4);
      }

      .button-text {
        font-size: 32rpx;
        font-weight: 600;
        color: #ffffff;
        letter-spacing: 2rpx;
      }
    }
  }

  .register-link {
    text-align: center;
    margin-top: 40rpx;

    .link-text {
      font-size: 28rpx;
      color: #6b7280;
    }

    .link-action {
      font-size: 28rpx;
      color: #3b82f6;
      font-weight: 500;
      margin-left: 8rpx;
      text-decoration: underline;
      cursor: pointer;
    }
  }

  .system-info-section {
    margin-top: 40rpx;
    padding-top: 20rpx;

    .system-footer {
      text-align: center;
      margin-top: 20rpx;
      padding-top: 10rpx;

      .footer-text {
        display: block;
        font-size: 24rpx;
        color: #9ca3af;
        margin-bottom: 8rpx;
        letter-spacing: 0.5rpx;
      }

      .footer-version {
        font-size: 22rpx;
        color: #d1d5db;
        font-weight: 300;
      }
    }
  }
</style>
