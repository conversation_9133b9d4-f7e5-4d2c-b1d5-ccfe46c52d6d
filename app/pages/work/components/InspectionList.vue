<template>
  <scroll-view scroll-y class="inspection-scroll">
    <view v-if="inspectionList.length > 0">
      <view
        class="inspection-card"
        v-for="(item, index) in inspectionList"
        :key="index"
        @click="goToDetail(item)"
      >
        <view class="card-status-indicator" :class="getStatusClass(item.status)"></view>
        <view class="card-content">
          <view class="card-header">
            <text class="task-name">{{ item.inspectionName }}</text>
            <view class="status-tag" :class="getStatusClass(item.status)">
              <uni-icons :type="getStatusIcon(item.status)" size="12" color="#ffffff" class="status-icon"></uni-icons>
              <text>{{ getStatusLabel(item.status) }}</text>
            </view>
          </view>
          <view class="card-body">
            <view class="info-row">
              <view class="info-icon-container">
                <uni-icons type="person-filled" size="14" color="#1a56b3" class="info-icon"></uni-icons>
              </view>
              <text class="info-label">负责人员:</text>
              <text class="info-value">{{ item.assignChargeUserName || '未指定' }}</text>
            </view>
            <view class="info-row">
              <view class="info-icon-container">
                <uni-icons type="staff-filled" size="14" color="#1a56b3" class="info-icon"></uni-icons>
              </view>
              <text class="info-label">分配人员:</text>
              <text class="info-value">{{ item.assignUserName || '未分配' }}</text>
            </view>
            <view class="info-row">
              <view class="info-icon-container">
                <uni-icons type="location-filled" size="14" color="#1a56b3" class="info-icon"></uni-icons>
              </view>
              <text class="info-label">巡检电站:</text>
              <text class="info-value">{{ item.station ? item.station.stationName : '未知电站' }}</text>
            </view>
            <view class="info-row">
              <view class="info-icon-container">
                <uni-icons type="calendar-filled" size="14" color="#1a56b3" class="info-icon"></uni-icons>
              </view>
              <text class="info-label">计划时间:</text>
              <text class="info-value">{{ formatDate(item.assignStartTime) }} - {{ formatDate(item.assignEndTime) }}</text>
            </view>
            <view class="info-row">
              <view class="info-icon-container">
                <uni-icons type="calendar-filled" size="14" color="#1a56b3" class="info-icon"></uni-icons>
              </view>
              <text class="info-label">下发时间:</text>
              <text class="info-value">{{ item.createTime }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 使用LoadingContainer组件 -->
    <loading-container
      v-if="loading || inspectionList.length === 0"
      :loading="loading"
      :error="!loading && inspectionList.length === 0"
      loadingText="正在加载巡检任务..."
      errorText="暂无巡检任务"
      :showRetry="false"
      height="200"
    />
  </scroll-view>
</template>

<script>
import { listInspection } from '@/api/system/inspection';
import LoadingContainer from '@/components/LoadingContainer';

export default {
  name: 'InspectionList',
  components: {
    LoadingContainer
  },
  props: {
    userId: {
      type: [String, Number],
      required: true
    },
    stationId: {
      type: [String, Number],
      default: null
    },
    dictData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      inspectionList: [],
      loading: false,
      pageSize: 9999
    }
  },
  created() {
    this.getInspectionList();
  },
  methods: {
    // 获取巡检任务列表
    getInspectionList() {
      this.loading = true;

      return new Promise((resolve, reject) => {
        const params = {
          pageSize: this.pageSize,
          stationId: this.stationId
        };

        // 确保 userId 是字符串类型
        const userId = String(this.userId);

        listInspection(params, userId).then(response => {
          // 过滤出待处理和进行中的任务
          this.inspectionList = response.rows.filter(item =>
            item.status === '0' || item.status === '2'
          ) || [];
          this.loading = false;
          resolve(response);
        }).catch(error => {
          console.error('获取巡检任务列表失败:', error);
          uni.showToast({
            title: '获取巡检任务列表失败',
            icon: 'none'
          });
          this.loading = false;
          reject(error);
        });
      });
    },

    // 获取状态标签
    getStatusLabel(status) {
      if (!status) return '未知';
      return this.$dict.getDictLabel('inspection_status', status, '未知');
    },

    // 获取状态样式类
    getStatusClass(status) {
      const statusClassMap = {
        '0': 'status-pending',    // 待处理
        '2': 'status-received',   // 进行中
        '4': 'status-submitted',  // 已完成
        '6': 'status-reviewed'    // 已审核
      };
      return statusClassMap[status] || 'status-unknown'; // 默认为未知状态
    },

    // 获取状态图标
    getStatusIcon(status) {
      const iconMap = {
        '0': 'notification-filled', // 待处理 - 提醒
        '2': 'paperplane-filled',   // 进行中 - 进行
        '4': 'checkbox-filled',     // 已完成 - 勾选
        '6': 'eye-filled'           // 已审核 - 查看
      };
      return iconMap[status] || 'help-filled'; // 默认为问号图标
    },

    // 格式化日期
    formatDate(dateString) {
      if (!dateString) return '未设置';
      const date = new Date(dateString);
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
    },

    // 刷新列表
    refresh() {
      return this.getInspectionList();
    },

    // 跳转到详情页
    goToDetail(item) {
      uni.navigateTo({
        url: `/pages/inspection/detail?id=${item.inspectionId}`
      });
    }
  }
}
</script>

<style lang="scss" scoped>
/* 巡检任务列表 */
.inspection-scroll {
  flex: 1;
  height: calc(100vh - (var(--status-bar-height) + 56px + 170px));
  padding: 0 10px;
}

/* 优化的卡片样式 */
.inspection-card {
  display: flex;
  margin-bottom: 10px;
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  overflow: hidden;
  position: relative;
  border: 1px solid #f0f4fa;
}

.card-status-indicator {
  width: 3px;
  flex-shrink: 0;
}

.card-content {
  flex-grow: 1;
  padding: 12px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  padding-bottom: 6px;
  border-bottom: 1px solid #f0f4fa;
}

.task-name {
  width: 60px;
  font-size: 14px;
  font-weight: 600;
  color: #1a1a1a;
  line-height: 1.3;
  flex: 1;
  margin-right: 8px;
  letter-spacing: 0.2px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.status-tag {
  display: flex;
  align-items: center;
  padding: 2px 6px;
  border-radius: 12px;
  font-size: 10px;
  color: #ffffff;
  font-weight: 500;
  white-space: nowrap;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.status-icon {
  margin-right: 2px;
}

.card-body {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.info-row {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #555555;
}

.info-icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border-radius: 4px;
  background-color: #f0f4fa;
  margin-right: 6px;
}

.info-label {
  color: #666666;
  margin-right: 4px;
  white-space: nowrap;
  font-weight: 500;
}

.info-value {
  color: #333333;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
  font-weight: 400;
}

/* 状态颜色定义 */
/* 待处理 - 橙色 */
.status-pending {
  background-color: #f7b84b;
}
.inspection-card .card-status-indicator.status-pending {
  background-color: #f7b84b;
}

/* 已驳回 - 红色 (假设) */
.status-rejected {
  background-color: #e74c3c;
}
.inspection-card .card-status-indicator.status-rejected {
  background-color: #e74c3c;
}

/* 进行中/已接收 - 蓝色 */
.status-received {
  background-color: #3498db;
}
.inspection-card .card-status-indicator.status-received {
  background-color: #3498db;
}

/* 已暂停 - 紫色 (假设) */
.status-paused {
  background-color: #9b59b6;
}
.inspection-card .card-status-indicator.status-paused {
  background-color: #9b59b6;
}

/* 已完成/已提交 - 绿色 */
.status-submitted {
  background-color: #2ecc71;
}
.inspection-card .card-status-indicator.status-submitted {
  background-color: #2ecc71;
}

/* 已关闭 - 深灰色 (假设) */
.status-closed {
  background-color: #34495e;
}
.inspection-card .card-status-indicator.status-closed {
  background-color: #34495e;
}

/* 已审核 - 浅灰色 */
.status-reviewed {
  background-color: #95a5a6;
}
.inspection-card .card-status-indicator.status-reviewed {
  background-color: #95a5a6;
}

/* 未知状态 - 灰色 */
.status-unknown {
  background-color: #7f8c8d;
}
.inspection-card .card-status-indicator.status-unknown {
  background-color: #7f8c8d;
}


</style>
