<template>
  <view class="task-stats-container">
    <view class="task-stats-header">
      <view class="header-left">
        <text class="task-stats-title">我的任务</text>
        <view class="status-legend">
          <view class="legend-item">
            <view class="legend-square status-pending"></view>
            <text class="legend-text">待处理</text>
          </view>
          <view class="legend-item">
            <view class="legend-square status-received"></view>
            <text class="legend-text">进行中</text>
          </view>
          <view class="legend-item">
            <view class="legend-square status-submitted"></view>
            <text class="legend-text">已完成</text>
          </view>
        </view>
      </view>
      <view class="task-stats-refresh" @click="refreshStats">
        <uni-icons type="refresh" size="12" color="#1a56b3"></uni-icons>
        <text>刷新</text>
      </view>
    </view>

    <view class="task-stats-grid">
      <view class="task-stats-item" :class="{active: activeTaskType === 'inspection'}" @click="switchTaskType('inspection')">
        <view class="task-stats-icon-container" :class="{active: activeTaskType === 'inspection'}">
          <uni-icons type="checkbox-filled" size="18" :color="activeTaskType === 'inspection' ? '#ffffff' : '#1a56b3'" class="task-stats-icon"></uni-icons>
        </view>
        <view class="task-stats-content">
          <text class="task-stats-label" :class="{active: activeTaskType === 'inspection'}">巡检任务</text>
          <text class="task-stats-count">{{ inspectionStats.pendingCount + inspectionStats.inProgressCount || 0 }}</text>
        </view>
        <view class="task-stats-detail">
          <view class="task-stats-detail-item">
            <text class="task-stats-detail-value status-pending">{{ inspectionStats.pendingCount || 0 }}</text>
          </view>
          <view class="task-stats-detail-item">
            <text class="task-stats-detail-value status-received">{{ inspectionStats.inProgressCount || 0 }}</text>
          </view>
          <view class="task-stats-detail-item" @click="goToWorkHistory('inspection', $event)">
            <text class="task-stats-detail-value status-submitted clickable">{{ inspectionStats.completedCount || 0 }}</text>
          </view>
        </view>
      </view>

      <view class="task-stats-item" :class="{active: activeTaskType === 'cleaning'}" @click="switchTaskType('cleaning')">
        <view class="task-stats-icon-container" :class="{active: activeTaskType === 'cleaning'}">
          <uni-icons type="plus-filled" size="18" :color="activeTaskType === 'cleaning' ? '#ffffff' : '#1a56b3'" class="task-stats-icon"></uni-icons>
        </view>
        <view class="task-stats-content">
          <text class="task-stats-label" :class="{active: activeTaskType === 'cleaning'}">组件清洗</text>
          <text class="task-stats-count">{{ cleaningStats.pendingCount + cleaningStats.inProgressCount || 0 }}</text>
        </view>
        <view class="task-stats-detail">
          <view class="task-stats-detail-item">
            <text class="task-stats-detail-value status-pending">{{ cleaningStats.pendingCount || 0 }}</text>
          </view>
          <view class="task-stats-detail-item">
            <text class="task-stats-detail-value status-received">{{ cleaningStats.inProgressCount || 0 }}</text>
          </view>
          <view class="task-stats-detail-item" @click="goToWorkHistory('cleaning', $event)">
            <text class="task-stats-detail-value status-submitted clickable">{{ cleaningStats.completedCount || 0 }}</text>
          </view>
        </view>
      </view>

      <view class="task-stats-item" :class="{active: activeTaskType === 'repair'}" @click="switchTaskType('repair')">
        <view class="task-stats-icon-container" :class="{active: activeTaskType === 'repair'}">
          <uni-icons type="gear-filled" size="18" :color="activeTaskType === 'repair' ? '#ffffff' : '#1a56b3'" class="task-stats-icon"></uni-icons>
        </view>
        <view class="task-stats-content">
          <text class="task-stats-label" :class="{active: activeTaskType === 'repair'}">故障检修</text>
          <text class="task-stats-count">{{ faultRepairStats.pendingCount + faultRepairStats.inProgressCount || 0 }}</text>
        </view>
        <view class="task-stats-detail">
          <view class="task-stats-detail-item">
            <text class="task-stats-detail-value status-pending">{{ faultRepairStats.pendingCount || 0 }}</text>
          </view>
          <view class="task-stats-detail-item">
            <text class="task-stats-detail-value status-received">{{ faultRepairStats.inProgressCount || 0 }}</text>
          </view>
          <view class="task-stats-detail-item" @click="goToWorkHistory('repair', $event)">
            <text class="task-stats-detail-value status-submitted clickable">{{ faultRepairStats.completedCount || 0 }}</text>
          </view>
        </view>
      </view>

      <view class="task-stats-item" :class="{active: activeTaskType === 'power'}" @click="switchTaskType('power')">
        <view class="task-stats-icon-container" :class="{active: activeTaskType === 'power'}">
          <uni-icons type="eye-filled" size="18" :color="activeTaskType === 'power' ? '#ffffff' : '#1a56b3'" class="task-stats-icon"></uni-icons>
        </view>
        <view class="task-stats-content">
          <text class="task-stats-label" :class="{active: activeTaskType === 'power'}">电量统计</text>
          <text class="task-stats-count">{{ meterReadingStats.pendingCount + meterReadingStats.inProgressCount || 0 }}</text>
        </view>
        <view class="task-stats-detail">
          <view class="task-stats-detail-item">
            <text class="task-stats-detail-value status-pending">{{ meterReadingStats.pendingCount || 0 }}</text>
          </view>
          <view class="task-stats-detail-item">
            <text class="task-stats-detail-value status-received">{{ meterReadingStats.inProgressCount || 0 }}</text>
          </view>
          <view class="task-stats-detail-item" @click="goToWorkHistory('power', $event)">
            <text class="task-stats-detail-value status-submitted clickable">{{ meterReadingStats.completedCount || 0 }}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { getInspectionStats } from '@/api/system/inspection';
import { getCleaningStats } from '@/api/system/cleaning';
import { getFaultRepairStats } from '@/api/system/faultrepair';
import { getMeterReadingStats } from '@/api/system/meterReading';

export default {
  name: 'TaskStatsPanel',
  props: {
    userId: {
      type: [String, Number],
      required: true
    },
    value: {
      type: String,
      default: 'inspection'
    }
  },
  data() {
    return {
      activeTaskType: 'inspection',
      inspectionStats: {
        totalCount: 0,
        pendingCount: 0,
        inProgressCount: 0,
        completedCount: 0
      },
      cleaningStats: {
        totalCount: 0,
        pendingCount: 0,
        inProgressCount: 0,
        completedCount: 0
      },
      faultRepairStats: {
        totalCount: 0,
        pendingCount: 0,
        inProgressCount: 0,
        completedCount: 0
      },
      meterReadingStats: {
        totalCount: 0,
        pendingCount: 0,
        inProgressCount: 0,
        completedCount: 0
      }
    }
  },
  watch: {
    value: {
      handler(newVal) {
        this.activeTaskType = newVal;
      },
      immediate: true
    }
  },
  created() {
    this.getInspectionStats();
    this.getCleaningStats();
    this.getFaultRepairStats();
    this.getMeterReadingStats();
  },
  methods: {
    // 获取巡检任务统计信息
    async getInspectionStats() {
      try {
        // 确保 userId 是字符串类型
        const userId = String(this.userId);
        const { data } = await getInspectionStats(userId);
        this.inspectionStats = data || {
          totalCount: 0,
          pendingCount: 0,
          inProgressCount: 0,
          completedCount: 0
        };
      } catch (error) {
        console.error('获取巡检任务统计信息失败:', error);
      }
    },

    // 获取清洗任务统计信息
    async getCleaningStats() {
      try {
        // 确保 userId 是字符串类型
        const userId = String(this.userId);
        const { data } = await getCleaningStats(userId);
        this.cleaningStats = data || {
          totalCount: 0,
          pendingCount: 0,
          inProgressCount: 0,
          completedCount: 0
        };
      } catch (error) {
        console.error('获取清洗任务统计信息失败:', error);
        // 如果接口不存在，设置默认值
        this.cleaningStats = {
          totalCount: 0,
          pendingCount: 0,
          inProgressCount: 0,
          completedCount: 0
        };
      }
    },

    // 获取故障检修任务统计信息
    async getFaultRepairStats() {
      try {
        // 确保 userId 是字符串类型
        const userId = String(this.userId);
        const { data } = await getFaultRepairStats(userId);
        this.faultRepairStats = data || {
          totalCount: 0,
          pendingCount: 0,
          inProgressCount: 0,
          completedCount: 0
        };
      } catch (error) {
        console.error('获取故障检修任务统计信息失败:', error);
        // 如果接口不存在，设置默认值
        this.faultRepairStats = {
          totalCount: 0,
          pendingCount: 0,
          inProgressCount: 0,
          completedCount: 0
        };
      }
    },

    // 获取电量统计任务统计信息
    async getMeterReadingStats() {
      try {
        // 确保 userId 是字符串类型
        const userId = String(this.userId);
        const { data } = await getMeterReadingStats(userId);
        this.meterReadingStats = data || {
          totalCount: 0,
          pendingCount: 0,
          inProgressCount: 0,
          completedCount: 0
        };
      } catch (error) {
        console.error('获取电量统计任务统计信息失败:', error);
        // 如果接口不存在，设置默认值
        this.meterReadingStats = {
          totalCount: 0,
          pendingCount: 0,
          inProgressCount: 0,
          completedCount: 0
        };
      }
    },

    // 切换任务类型
    switchTaskType(type) {
      this.activeTaskType = type;
      this.$emit('input', type);
      this.$emit('task-type-change', type);
    },

    // 刷新统计信息
    refreshStats() {
      this.getInspectionStats();
      this.getCleaningStats();
      this.getFaultRepairStats();
      this.getMeterReadingStats();
      this.$emit('refresh');
    },

    // 跳转到工作记录页面
    goToWorkHistory(taskType, event) {
      // 阻止事件冒泡，避免触发父级的点击事件
      if (event) {
        event.stopPropagation();
      }

      switch (taskType) {
        case 'inspection':
          // 跳转到巡检历史页面
          uni.navigateTo({
            url: '/pages/inspection/history'
          });
          break;
        case 'cleaning':
          // 跳转到清洗历史页面
          uni.navigateTo({
            url: '/pages/cleaning/history'
          });
          break;
        case 'repair':
          // 跳转到故障检修历史页面
          uni.navigateTo({
            url: '/pages/fault/repair-history'
          });
          break;
        case 'power':
          // 跳转到电量统计历史页面
          uni.navigateTo({
            url: '/pages/meterReading/history'
          });
          break;
        default:
          console.warn('未知的任务类型:', taskType);
      }
    }
  }
}
</script>

<style lang="scss" scoped>
/* 任务统计信息区域 */
.task-stats-container {
  background-color: #ffffff;
  border-radius: 4px;
  margin: 0 12px 12px;
  overflow: hidden;
}

.task-stats-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 12px;
  border-bottom: 1px solid #f0f4fa;
}

.header-left {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.task-stats-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  letter-spacing: 0.3px;
}

.status-legend {
  display: flex;
  align-items: center;
  gap: 10px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 3px;
}

.legend-square {
  width: 6px;
  height: 6px;
  border-radius: 1px;
  flex-shrink: 0;
}

.legend-text {
  font-size: 9px;
  color: #999;
  font-weight: 400;
  letter-spacing: 0.2px;
}

.task-stats-refresh {
  display: flex;
  align-items: center;
  font-size: 11px;
  color: #1a56b3;
  padding: 3px 6px;
  background-color: #f0f4fa;
  border-radius: 10px;
}

.task-stats-refresh text {
  margin-left: 3px;
}

.task-stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
  padding: 8px;
}

.task-stats-item {
  position: relative;
  background-color: #f8f9fc;
  border-radius: 8px;
  padding: 10px;
  transition: all 0.3s ease;
  border: 1px solid #f0f4fa;
}

.task-stats-item.active {
  background-color: #f0f4fa;
  border: 1px solid #e0e7f5;
}

.task-stats-icon-container {
  position: absolute;
  top: 10px;
  right: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 30px;
  height: 30px;
  border-radius: 6px;
  background-color: #f0f4fa;
}

.task-stats-icon-container.active {
  background-color: #1a56b3;
}

.task-stats-content {
  display: flex;
  flex-direction: column;
  margin-bottom: 6px;
}

.task-stats-label {
  font-size: 13px;
  color: #666;
  font-weight: 500;
  margin-bottom: 4px;
  transition: all 0.3s ease;
}

.task-stats-label.active {
  color: #1a56b3;
  font-weight: 600;
}

.task-stats-count {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin-bottom: 6px;
}

.task-stats-detail {
  display: flex;
  justify-content: space-between;
  margin-top: 6px;
}

.task-stats-detail-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.task-stats-detail-value {
  font-size: 11px;
  font-weight: 600;
  padding: 2px 8px;
  border-radius: 8px;
  color: #ffffff;
  min-width: 18px;
  text-align: center;
}

/* 状态颜色定义 */
/* 待处理 - 橙色 */
.status-pending {
  background-color: #f7b84b;
}

/* 进行中/已接收 - 蓝色 */
.status-received {
  background-color: #3498db;
}

/* 已完成/已提交 - 绿色 */
.status-submitted {
  background-color: #2ecc71;
}

/* 可点击的胶囊样式 */
.clickable {
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.clickable:hover {
  transform: scale(1.05);
  box-shadow: 0 2px 4px rgba(46, 204, 113, 0.3);
}

.clickable:active {
  transform: scale(0.95);
}

/* 为可点击的详情项添加样式 */
.task-stats-detail-item.clickable {
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: 4px;
  padding: 2px;
}

.task-stats-detail-item.clickable:hover {
  background-color: rgba(46, 204, 113, 0.1);
}
</style>
