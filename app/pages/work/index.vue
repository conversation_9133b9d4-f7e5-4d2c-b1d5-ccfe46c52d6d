<template>
  <view class="container">
    <!-- 顶部导航栏 -->
    <view class="header">
      <view class="header-content">
        <view class="header-left">
          <text class="header-title">工作台</text>
          <text class="header-subtitle">Professional Workspace</text>
        </view>
        <view class="header-right">
          <view class="datetime-container">
            <text class="date-text">{{ currentDate }}</text>
            <text class="time-text">{{ currentTime }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 占位元素，用于确保内容不被固定元素遮挡 -->
    <view class="fixed-placeholder"></view>

    <!-- 内容区域 -->
    <view class="content">
      <!-- 任务统计信息区域 -->
      <task-stats-panel
        :userId="userId"
        v-model="activeTaskType"
        @task-type-change="switchTaskType"
        @refresh="refreshPage"
      />

      <!-- 巡检任务列表 -->
      <inspection-list
        v-if="activeTaskType === 'inspection'"
        :userId="userId"
        :stationId="stationId"
        :dictData="dictData"
        ref="inspectionList"
      />

      <!-- 清洗任务列表 -->
      <cleaning-list
        v-if="activeTaskType === 'cleaning'"
        :userId="userId"
        :stationId="stationId"
        :dictData="dictData"
        ref="cleaningList"
      />

      <!-- 故障检修任务列表 -->
      <fault-repair-list
        v-if="activeTaskType === 'repair'"
        :userId="userId"
        :stationId="stationId"
        :dictData="dictData"
        ref="faultRepairList"
      />

      <!-- 电量统计任务列表 -->
      <meter-reading-list
        v-if="activeTaskType === 'power'"
        :userId="userId"
        :stationId="stationId"
        :dictData="dictData"
        ref="meterReadingList"
      />

      <!-- 其他功能模块 -->
      <view v-if="activeTaskType !== 'inspection' && activeTaskType !== 'cleaning' && activeTaskType !== 'repair' && activeTaskType !== 'power'" class="coming-soon">
        <image src="/static/images/empty/no_search_results.png" mode="aspectFit" class="coming-soon-image"></image>
        <text class="coming-soon-text">功能即将上线</text>
      </view>
    </view>
  </view>
</template>

<script>
import { getDictData} from "@/api/system/dictData";
import TaskStatsPanel from './components/TaskStatsPanel.vue';
import InspectionList from './components/InspectionList.vue';
import CleaningList from './components/CleaningList.vue';
import FaultRepairList from './components/FaultRepairList.vue';
import MeterReadingList from './components/MeterReadingList.vue';

export default {
  components: {
    TaskStatsPanel,
    InspectionList,
    CleaningList,
    FaultRepairList,
    MeterReadingList
  },
  data() {
    return {
      statusBarHeight: 0,
      stationId: null,
      userId: null,
      dictData: {},
      activeTaskType: 'inspection', // 默认显示巡检任务
      // 日期和时间
      currentDate: '',
      currentTime: '',
      timerInterval: null
    }
  },
  onLoad(options) {
    // 设置状态栏高度
    this.statusBarHeight = uni.getSystemInfoSync().statusBarHeight;

    if (options.stationId) {
      this.stationId = options.stationId;
    }

    // 获取当前登录用户ID
    this.userId = this.$store.state.user.userId;

    // 获取字典数据
    this.getDicts();

    // 初始化日期和时间
    this.updateDateTime();
    // 启动定时器，每秒更新时间
    this.timerInterval = setInterval(() => {
      this.updateDateTime();
    }, 1000);
  },

  onUnload() {
    // 页面卸载时清除定时器
    if (this.timerInterval) {
      clearInterval(this.timerInterval);
    }
  },

  methods: {
    // 获取字典数据
    async getDicts() {
      try {
        const response = await getDictData();
        this.dictData = this.$dict.parseDictData(response.data);
      } catch (error) {
        console.error('获取字典数据失败:', error);
        uni.showToast({
          title: '获取字典数据失败',
          icon: 'none'
        });
      }
    },

    // 刷新页面
    refreshPage() {
      if (this.activeTaskType === 'inspection' && this.$refs.inspectionList) {
        this.$refs.inspectionList.refresh().then(() => {
          uni.showToast({
            title: '刷新成功',
            icon: 'none'
          });
        });
      } else if (this.activeTaskType === 'cleaning' && this.$refs.cleaningList) {
        this.$refs.cleaningList.refresh().then(() => {
          uni.showToast({
            title: '刷新成功',
            icon: 'none'
          });
        });
      } else if (this.activeTaskType === 'repair' && this.$refs.faultRepairList) {
        this.$refs.faultRepairList.refresh().then(() => {
          uni.showToast({
            title: '刷新成功',
            icon: 'none'
          });
        });
      } else if (this.activeTaskType === 'power' && this.$refs.meterReadingList) {
        this.$refs.meterReadingList.refresh().then(() => {
          uni.showToast({
            title: '刷新成功',
            icon: 'none'
          });
        });
      } else {
        uni.showToast({
          title: '刷新成功',
          icon: 'none'
        });
      }
    },

    // 更新日期和时间
    updateDateTime() {
      const now = new Date();

      // 格式化日期：YYYY-MM-DD 星期几
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, '0');
      const day = String(now.getDate()).padStart(2, '0');
      const weekDays = ['日', '一', '二', '三', '四', '五', '六'];
      const weekDay = weekDays[now.getDay()];
      this.currentDate = `${year}-${month}-${day} 星期${weekDay}`;

      // 格式化时间：HH:MM:SS
      const hours = String(now.getHours()).padStart(2, '0');
      const minutes = String(now.getMinutes()).padStart(2, '0');
      const seconds = String(now.getSeconds()).padStart(2, '0');
      this.currentTime = `${hours}:${minutes}:${seconds}`;
    },

    // 切换任务类型
    switchTaskType(type) {
      this.activeTaskType = type;
      if (type === 'inspection' && this.$refs.inspectionList) {
        // 刷新巡检任务数据
        this.$refs.inspectionList.refresh();
      } else if (type === 'cleaning' && this.$refs.cleaningList) {
        // 刷新清洗任务数据
        this.$refs.cleaningList.refresh();
      } else if (type === 'repair' && this.$refs.faultRepairList) {
        // 刷新故障检修任务数据
        this.$refs.faultRepairList.refresh();
      } else if (type === 'power' && this.$refs.meterReadingList) {
        // 刷新电量统计任务数据
        this.$refs.meterReadingList.refresh();
      }
    },
  }
}
</script>

<style lang="scss" scoped>
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8f9fc;
}

/* 占位元素 */
.fixed-placeholder {
  height: calc(var(--status-bar-height) + 56px);
  width: 100%;
}

/* 顶部导航栏 */
.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: linear-gradient(135deg, #1a56b3 0%, #3a7bd5 100%);
  padding: 8px 16px;
  padding-top: var(--status-bar-height);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  flex-direction: column;
}

.header-title {
  color: #ffffff;
  font-size: 18px;
  font-weight: bold;
  letter-spacing: 0.5px;
  margin-bottom: 2px;
}

.header-subtitle {
  color: rgba(255, 255, 255, 0.8);
  font-size: 10px;
  font-weight: 400;
  letter-spacing: 0.5px;
}

.header-right {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.datetime-container {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.date-text {
  color: rgba(255, 255, 255, 0.9);
  font-size: 12px;
  font-weight: 400;
  margin-bottom: 2px;
}

.time-text {
  color: #ffffff;
  font-size: 16px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

/* 内容区域 */
.content {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: calc(100vh - (var(--status-bar-height) + 60px));
  padding-top: 4px;
}

/* 即将上线提示 */
.coming-soon {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: calc(100vh - (var(--status-bar-height) + 56px + 100px));
}

.coming-soon-image {
  width: 120px;
  height: 120px;
  margin-bottom: 16px;
  opacity: 0.8;
}

.coming-soon-text {
  color: #666;
  font-size: 15px;
  font-weight: 500;
  text-align: center;
}
</style>