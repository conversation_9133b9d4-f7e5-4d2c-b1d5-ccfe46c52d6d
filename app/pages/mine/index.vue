<template>
  <view class="mine-container" :style="{height: `${windowHeight}px`}">
    <!--顶部个人信息栏-->
    <view class="header-section">
      <view class="flex padding justify-between">
        <view class="flex align-center">
          <view v-if="!avatar" class="cu-avatar xl round bg-white">
            <view class="iconfont icon-people text-gray icon"></view>
          </view>
          <image v-if="avatar" @click="handleToInfo" :src="avatar" class="cu-avatar xl round" mode="widthFix">
          </image>
          <view v-if="!name" @click="handleToLogin" class="login-tip">
            <view class="iconfont icon-login" style="margin-right: 5px;"></view>
            点击登录
          </view>
          <view v-if="name" @click="handleToInfo" class="user-info">
            <view class="u_title">
              {{ nickName }}
            </view>
            <view class="s_title">
              登录账号：{{ name }}
            </view>
          </view>
        </view>
      </view>
    </view>

    <view class="content-section">
      <view class="mine-actions">
        <view class="section-title">工作记录</view>
        <view class="actions-grid grid col-4 text-center">
          <view class="action-item" @click="handleToInspection">
            <uni-icons type="checkmarkempty" size="28" color="#4a6cf7" class="icon"></uni-icons>
            <text class="text">巡检任务</text>
          </view>
          <view class="action-item" @click="handleToCleaning">
            <uni-icons type="refreshempty" size="28" color="#00d4aa" class="icon"></uni-icons>
            <text class="text">组件清洗</text>
          </view>
          <view class="action-item" @click="handleToRepair">
            <uni-icons type="gear" size="28" color="#ff6b6b" class="icon"></uni-icons>
            <text class="text">故障检修</text>
          </view>
          <view class="action-item" @click="handleToPowerStats">
            <uni-icons type="bars" size="28" color="#ffa726" class="icon"></uni-icons>
            <text class="text">电量统计</text>
          </view>
        </view>
      </view>

      <!-- 常用工具栏 -->
      <view class="mine-tools">
        <view class="section-title">常用工具</view>
        <view class="tools-grid grid col-4 text-center">
          <view class="tool-item" @click="handleToFaultReport">
            <image src="/static/images/icon/fault_reporting.svg" class="tool-icon"></image>
            <text class="text">故障上报</text>
          </view>
          <view class="tool-item" @click="handleToFaultHistory">
            <image src="/static/images/icon/list_of_faults.svg" class="tool-icon"></image>
            <text class="text">故障历史</text>
          </view>
        </view>
      </view>

      <view class="menu-list">
        <view class="list-cell list-cell-arrow" @click="handleToEditInfo">
          <view class="menu-item-box">
            <view class="iconfont icon-user menu-icon"></view>
            <view>编辑资料</view>
          </view>
        </view>
        <view class="list-cell list-cell-arrow" @click="handleToPwd">
          <view class="menu-item-box">
            <view class="iconfont icon-password menu-icon"></view>
            <view>修改密码</view>
          </view>
        </view>
        <!-- 管理后台菜单项，仅管理员可见 -->
        <view v-if="hasAdminPermission" class="list-cell list-cell-arrow" @click="handleToAdmin">
          <view class="menu-item-box">
            <view class="iconfont icon-setting menu-icon"></view>
            <view>管理后台</view>
          </view>
        </view>
        <view class="list-cell list-cell-arrow" @click="handleLogout">
          <view class="menu-item-box">
            <view class="iconfont icon-logout menu-icon"></view>
            <view>退出登录</view>
          </view>
        </view>
      </view>

    </view>
  </view>
</template>

<script>
  export default {
    data() {
      return {
        name: this.$store.state.user.name,
        nickName: this.$store.state.user.nickName
      }
    },
    computed: {
      avatar() {
        return this.$store.state.user.avatar
      },
      windowHeight() {
        return uni.getSystemInfoSync().windowHeight - 50
      },
      // 判断用户是否具有管理员权限
      hasAdminPermission() {
        const roles = this.$store.state.user.roles || []

        // 检查角色中是否包含admin和manage
        return roles.includes('manage') || roles.includes('admin')
      }
    },
    methods: {
      handleToInfo() {
        this.$tab.navigateTo('/pages/mine/info/index')
      },
      handleToEditInfo() {
        this.$tab.navigateTo('/pages/mine/info/edit')
      },
      handleToPwd() {
        this.$tab.navigateTo('/pages/mine/pwd/index')
      },
      handleToLogin() {
        this.$tab.reLaunch('/pages/login')
      },
      handleLogout() {
        this.$modal.confirm('确定注销并退出系统吗？').then(() => {
          this.$store.dispatch('LogOut').then(() => {}).finally(()=>{
            this.$tab.reLaunch('/pages/index')
          })
        })
      },
      handleToInspection() {
        this.$tab.navigateTo('/pages/inspection/history')
      },
      handleToCleaning() {
        this.$tab.navigateTo('/pages/cleaning/history')
      },
      handleToRepair() {
        this.$tab.navigateTo('/pages/fault/repair-history')
      },
      handleToPowerStats() {
        this.$tab.navigateTo('/pages/meterReading/history')
      },
      // 跳转到管理后台
      handleToAdmin() {
        uni.navigateTo({
          url: '/pages/admin/webview?url=' + encodeURIComponent('http://************:7789/login')
        })
      },
      // 跳转到故障上报
      handleToFaultReport() {
        this.$tab.navigateTo('/pages/fault/fault-report')
      },
      // 跳转到故障历史
      handleToFaultHistory() {
        this.$tab.navigateTo('/pages/fault/fault-history')
      }
    }
  }
</script>

<style lang="scss" scoped>
  page {
    background-color: #f8f9fa;
  }

  .mine-container {
    width: 100%;
    height: 100%;

    .header-section {
      padding: 60px 20px 60px 20px;
      background-image: linear-gradient(rgba(0, 0, 0, 0.1), rgba(0, 0, 0, 0.1)), url('/static/images/mine.jpg');
      background-size: cover;
      background-position: center;
      color: white;
      position: relative;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);

      .login-tip {
        font-size: 18px;
        margin-left: 15px;
        font-weight: 500;
        letter-spacing: 0.5px;
      }

      .cu-avatar {
        width: 70px;
        height: 70px;
        border: 3px solid rgba(255, 255, 255, 0.8);
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);

        .icon {
          font-size: 50px;
        }
      }

      .user-info {
        margin-left: 20px;

        .u_title {
          font-size: 18px;
          line-height: 30px;
          font-weight: 500;
          text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }

        .s_title {
          margin-top: 4px;
          font-size: 14px;
          line-height: 20px;
          color: #dfdfdf;
        }
      }

      .info-btn {
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 14px;
        transition: background-color 0.2s;
      }
    }

    .content-section {
      position: relative;
      top: -50px;

      .mine-actions {
        margin: 0 10px 14px 10px;
        padding: 12px 12px 10px 12px;
        border-radius: 12px;
        background-color: white;
        box-shadow: 0 2px 15px rgba(0, 0, 0, 0.05);

        .section-title {
          font-size: 12px;
          font-weight: bold;
          letter-spacing: 0.3px;
          text-align: left;
          padding-left: 10px;
          position: relative;
        }

        .actions-grid {
          display: flex;
          justify-content: space-between;
          align-items: stretch;
        }

        .actions-grid .action-item {
          transition: all 0.3s ease;
          padding: 8px 4px;
          border-radius: 8px;
          margin: 0 2px;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          min-height: 80px;
          flex: 1;

          .icon {
            margin-bottom: 8px;
            transition: transform 0.2s ease;
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
            display: block;
            flex-shrink: 0;
          }

          .text {
            display: block;
            font-size: 12px;
            margin: 0;
            color: #2c3e50;
            font-weight: 500;
            letter-spacing: 0.3px;
            text-align: center;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 100%;
            line-height: 1.2;
          }
        }
      }

      .mine-tools {
        margin: 0 10px 14px 10px;
        padding: 12px 12px 10px 12px;
        border-radius: 12px;
        background-color: white;
        box-shadow: 0 2px 15px rgba(0, 0, 0, 0.05);

        .section-title {
          font-size: 12px;
          font-weight: bold;
          letter-spacing: 0.3px;
          text-align: left;
          padding-left: 10px;
          position: relative;
        }

        .tools-grid {
          display: flex;
          justify-content: flex-start;
          align-items: stretch;
        }

        .tools-grid .tool-item {
          transition: all 0.3s ease;
          padding: 8px 4px;
          border-radius: 8px;
          margin: 0 2px;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          min-height: 80px;
          flex: 1;
          max-width: 25%;

          .tool-icon {
            width: 28px;
            height: 28px;
            margin-bottom: 8px;
            transition: transform 0.2s ease;
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
            display: block;
            flex-shrink: 0;
          }

          .text {
            display: block;
            font-size: 12px;
            margin: 0;
            color: #2c3e50;
            font-weight: 500;
            letter-spacing: 0.3px;
            text-align: center;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 100%;
            line-height: 1.2;
          }
        }
      }

      .menu-list {
        margin: 0 15px;
        border-radius: 12px;
        background-color: white;
        box-shadow: 0 2px 15px rgba(0, 0, 0, 0.05);
        overflow: hidden;

        .list-cell {
          padding: 16px 20px;
          border-bottom: 1px solid #f0f0f0;

          &:last-child {
            border-bottom: none;
          }

          .menu-item-box {
            display: flex;
            align-items: center;

            .menu-icon {
              margin-right: 15px;
              font-size: 20px;
              color: #555;
            }
          }
        }
      }
    }
  }
</style>
