<template>
  <view class="container">
    <!-- 个人信息卡片 -->
    <view class="info-card">
      <view class="card-header">
        <uni-icons type="person-filled" size="16" color="#2979ff"></uni-icons>
        <text class="card-title">个人信息</text>
      </view>
      <view class="card-content">
        <view class="info-item">
          <view class="info-left">
            <uni-icons type="person" size="14" color="#666"></uni-icons>
            <text class="info-label">昵称</text>
          </view>
          <text class="info-value">{{ user.nickName || '未设置' }}</text>
        </view>
        <view class="info-item">
          <view class="info-left">
            <uni-icons type="phone" size="14" color="#666"></uni-icons>
            <text class="info-label">手机号码</text>
          </view>
          <text class="info-value">{{ user.phonenumber || '未设置' }}</text>
        </view>
        <view class="info-item">
          <view class="info-left">
            <uni-icons type="email" size="14" color="#666"></uni-icons>
            <text class="info-label">邮箱</text>
          </view>
          <text class="info-value">{{ user.email || '未设置' }}</text>
        </view>
        <view class="info-item">
          <view class="info-left">
            <uni-icons type="gear" size="14" color="#666"></uni-icons>
            <text class="info-label">岗位</text>
          </view>
          <text class="info-value">{{ postGroup || '未分配' }}</text>
        </view>
        <view class="info-item">
          <view class="info-left">
            <uni-icons type="staff" size="14" color="#666"></uni-icons>
            <text class="info-label">角色</text>
          </view>
          <text class="info-value">{{ roleGroup || '未分配' }}</text>
        </view>
        <view class="info-item">
          <view class="info-left">
            <uni-icons type="calendar" size="14" color="#666"></uni-icons>
            <text class="info-label">创建日期</text>
          </view>
          <text class="info-value">{{ formatDate(user.createTime) }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
  import { getUserProfile } from "@/api/system/user"

  export default {
    data() {
      return {
        user: {},
        roleGroup: "",
        postGroup: ""
      }
    },
    onLoad() {
      this.getUser()
    },
    methods: {
      getUser() {
        getUserProfile().then(response => {
          this.user = response.data
          this.roleGroup = response.roleGroup
          this.postGroup = response.postGroup
        })
      },
      // 格式化日期
      formatDate(dateStr) {
        if (!dateStr) return '未知'
        try {
          const date = new Date(dateStr)
          return date.toLocaleDateString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit'
          })
        } catch (error) {
          return dateStr
        }
      }
    }
  }
</script>

<style lang="scss" scoped>
page {
  background-color: #f5f5f5;
}

.container {
  padding: 15px;
  min-height: 100vh;
}

/* 信息卡片样式 */
.info-card {
  background-color: #ffffff;
  border-radius: 12px;
  box-shadow: 0 2px 15px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.card-header {
  display: flex;
  align-items: center;
  padding: 16px 20px 12px;
  border-bottom: 1px solid #f0f0f0;
  background-color: #fafafa;
}

.card-title {
  font-size: 16px;
  font-weight: bold;
  margin-left: 8px;
  color: #2979ff;
  letter-spacing: 0.5px;
}

.card-content {
  padding: 8px 20px 16px;
}

/* 信息项样式 */
.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f9f9f9;
  position: relative;
}

.info-item:last-child {
  border-bottom: none;
  padding-bottom: 8px;
}

.info-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.info-label {
  font-size: 14px;
  color: #333;
  margin-left: 8px;
  font-weight: 500;
}

.info-value {
  font-size: 14px;
  color: #666;
  text-align: right;
  max-width: 60%;
  word-break: break-all;
  line-height: 1.4;
}

/* 空值样式 */
.info-value:empty::after {
  content: '未设置';
  color: #ccc;
  font-style: italic;
}

/* 响应式设计 */
@media (max-width: 375px) {
  .container {
    padding: 10px;
  }

  .card-header {
    padding: 14px 16px 10px;
  }

  .card-content {
    padding: 6px 16px 14px;
  }

  .info-item {
    padding: 10px 0;
  }

  .info-label {
    font-size: 13px;
  }

  .info-value {
    font-size: 13px;
    max-width: 55%;
  }
}
</style>
