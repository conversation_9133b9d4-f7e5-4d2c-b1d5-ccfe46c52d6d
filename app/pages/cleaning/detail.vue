<template>
  <view class="container">
    <!-- 顶部导航栏 -->
    <view class="header" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="header-content">
        <view class="back-icon" @click="goBack">
          <uni-icons type="left" size="18" color="#FFFFFF"></uni-icons>
        </view>
        <view class="header-title">清洗详情</view>
        <view class="header-right">
          <view class="refresh-icon" @click="refreshData">
            <uni-icons type="refresh" size="18" color="#FFFFFF"></uni-icons>
          </view>
        </view>
      </view>
    </view>

    <!-- 内容区域 -->
    <scroll-view scroll-y class="content" :style="{ paddingTop: (statusBarHeight + 44) + 'px' }">
      <view v-if="cleaningInfo" class="cleaning-detail">
        <!-- 任务状态标识 -->
        <view class="status-banner" :class="getStatusClass(cleaningInfo.status)">
          <view class="status-content">
            <view class="status-left">
              <view class="status-icon">
                <uni-icons :type="getStatusIcon(cleaningInfo.status)" size="18" color="#FFFFFF"></uni-icons>
              </view>
              <text class="status-text">{{ getStatusLabel(cleaningInfo.status) }}</text>
            </view>
            <!-- 操作按钮 -->
            <view class="action-buttons-top" v-if="canPerformAction && (cleaningInfo.status === '0' || cleaningInfo.status === '2')">
              <button class="action-button-top" type="primary" @click="handleReceive" v-if="cleaningInfo.status === '0'">
                接收任务
              </button>
              <button class="action-button-top" type="success" @click="handleExecute" v-if="cleaningInfo.status === '2'">
                执行任务
              </button>
            </view>
            <!-- 非负责人提示 -->
            <view class="action-tip" v-if="!canPerformAction && (cleaningInfo.status === '0' || cleaningInfo.status === '2')">
              <text class="action-tip-text">仅负责人可操作</text>
            </view>
          </view>
        </view>

        <!-- 基本信息卡片 -->
        <view class="detail-card">
          <view class="card-header">
            <uni-icons type="info-filled" size="16" color="#2979ff"></uni-icons>
            <text class="card-title">基本信息</text>
          </view>
          <view class="card-content">
            <view class="info-item">
              <text class="info-label">任务名称</text>
              <text class="info-value">{{ cleaningInfo.cleaningName }}</text>
            </view>
            <view class="info-item">
              <text class="info-label">所属电站</text>
              <text class="info-value">{{ cleaningInfo.station ? cleaningInfo.station.stationName : '未指定' }}</text>
            </view>
            <view class="info-item">
              <text class="info-label">负责人员</text>
              <text class="info-value">{{ cleaningInfo.assignChargeUserName || '未指定' }}</text>
            </view>
            <view class="info-item">
              <text class="info-label">清洗人员</text>
              <text class="info-value">{{ cleaningInfo.assignUserName || '未指定' }}</text>
            </view>
            <view class="info-item">
              <text class="info-label">计划时间</text>
              <text class="info-value">{{ formatDate(cleaningInfo.planStartTime) }} - {{ formatDate(cleaningInfo.planEndTime) }}</text>
            </view>
            <view class="info-item">
              <text class="info-label">下发时间</text>
              <text class="info-value">{{ cleaningInfo.createTime }}</text>
            </view>
            <view class="info-item" v-if="cleaningInfo.cleaningUserName">
              <text class="info-label">实际人员</text>
              <text class="info-value">{{ cleaningInfo.cleaningUserName }}</text>
            </view>
            <view class="info-item" v-if="cleaningInfo.receiveTime">
              <text class="info-label">接收时间</text>
              <text class="info-value">{{ cleaningInfo.receiveTime }}</text>
            </view>
            <view class="info-item" v-if="cleaningInfo.submitTime">
              <text class="info-label">提交时间</text>
              <text class="info-value">{{ cleaningInfo.submitTime }}</text>
            </view>
            <view class="info-item" v-if="cleaningInfo.reviewTime">
              <text class="info-label">审核时间</text>
              <text class="info-value">{{ cleaningInfo.reviewTime }}</text>
            </view>
            <view class="info-item" v-if="cleaningInfo.reviewRemark">
              <text class="info-label">审核备注</text>
              <text class="info-value">{{ cleaningInfo.reviewRemark }}</text>
            </view>
          </view>
        </view>

        <!-- 设备清洗列表 -->
        <view class="detail-card" v-if="equipmentList.length > 0">
          <view class="card-header">
            <uni-icons type="list" size="16" color="#2979ff"></uni-icons>
            <text class="card-title">清洗内容</text>
          </view>
          <view class="card-content">
            <view class="equipment-item" v-for="(equipment, index) in equipmentList" :key="index">
              <view class="equipment-header">
                <view class="equipment-name">{{ equipment.equipment.equipmentName }}</view>
                <view class="equipment-status" :class="getEquipmentStatusClass(equipment)">
                  <uni-icons :type="getEquipmentStatusIcon(equipment)" size="12" color="#FFFFFF"></uni-icons>
                  <text>{{ getEquipmentStatusText(equipment) }}</text>
                </view>
              </view>
              <!-- 清洗详情 -->
              <view class="cleaning-details" v-if="equipment.cleaningTime">
                <!-- 清洗信息紧凑显示 -->
                <view class="cleaning-result-row">
                  <view class="result-main">
                    <view class="cleaning-info-compact">
                      <text class="cleaning-time-text">{{ equipment.cleaningTime }}</text>
                      <view class="cleaning-method-dust" v-if="equipment.cleaningMethod || equipment.cleaningDustThickness">
                        <text class="cleaning-method-text" v-if="equipment.cleaningMethod">{{ getCleaningMethodLabel(equipment.cleaningMethod) }}</text>
                        <text class="cleaning-dust-text" v-if="equipment.cleaningDustThickness">积尘: {{ getDustThicknessLabel(equipment.cleaningDustThickness) }}</text>
                      </view>
                      <text class="cleaning-remark-text" v-if="equipment.cleaningRemark">{{ equipment.cleaningRemark }}</text>
                    </view>
                  </view>
                </view>

                <!-- 清洗前照片 -->
                <view class="cleaning-photo-row" v-if="equipment.beforeImage">
                  <view class="photo-row-content">
                    <text class="photo-row-title">清洗前</text>
                    <view class="photo-row-images">
                      <view
                        v-for="(photo, photoIndex) in getBeforeImageList(equipment.beforeImage).slice(0, 3)"
                        :key="photoIndex"
                        class="photo-container"
                        @click="previewBeforeImages(equipment.beforeImage, photoIndex)"
                      >
                        <image
                          :src="photo"
                          mode="aspectFit"
                          class="cleaning-photo-compact"
                        />
                        <view
                          v-if="photoIndex === 2 && getBeforeImageList(equipment.beforeImage).length > 3"
                          class="photo-more-indicator"
                        >
                          +{{ getBeforeImageList(equipment.beforeImage).length - 3 }}
                        </view>
                      </view>
                    </view>
                  </view>
                </view>

                <!-- 清洗后照片 -->
                <view class="cleaning-photo-row" v-if="equipment.afterImage">
                  <view class="photo-row-content">
                    <text class="photo-row-title">清洗后</text>
                    <view class="photo-row-images">
                      <view
                        v-for="(photo, photoIndex) in getAfterImageList(equipment.afterImage).slice(0, 3)"
                        :key="photoIndex"
                        class="photo-container"
                        @click="previewAfterImages(equipment.afterImage, photoIndex)"
                      >
                        <image
                          :src="photo"
                          mode="aspectFit"
                          class="cleaning-photo-compact"
                        />
                        <view
                          v-if="photoIndex === 2 && getAfterImageList(equipment.afterImage).length > 3"
                          class="photo-more-indicator"
                        >
                          +{{ getAfterImageList(equipment.afterImage).length - 3 }}
                        </view>
                      </view>
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 空设备状态 -->
        <view class="detail-card" v-if="!loading && cleaningInfo && equipmentList.length === 0">
          <view class="card-header">
            <uni-icons type="list" size="16" color="#2979ff"></uni-icons>
            <text class="card-title">设备清洗情况</text>
          </view>
          <view class="card-content">
            <view class="empty-equipment">
              <uni-icons type="info-filled" size="48" color="#cccccc"></uni-icons>
              <text>暂无关联设备</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 使用LoadingContainer组件 -->
      <loading-container
        v-if="loading || !cleaningInfo"
        :loading="loading"
        :error="!loading && !cleaningInfo"
        loadingText="正在加载清洗详情..."
        errorText="加载清洗详情失败"
        :showRetry="true"
        @retry="getCleaningDetail"
        height="300"
      />
    </scroll-view>
  </view>
</template>

<script>
import { getCleaning, getCleaningEquipments } from '@/api/system/cleaning';
import { getDictData } from '@/api/system/dictData';
import LoadingContainer from '@/components/LoadingContainer'
import { getImageUrl, getImageList } from '@/utils/imageUtils';

export default {
  name: 'CleaningDetail',
  components: {
    LoadingContainer
  },
  data() {
    return {
      statusBarHeight: 0,
      cleaningId: null,
      cleaningInfo: null,
      equipmentList: [],
      loading: false,
      userId: null,
      dictData: {}
    }
  },
  computed: {
    // 判断当前用户是否可以执行操作
    canPerformAction() {
      if (!this.cleaningInfo || !this.userId) return false;
      
      // 检查是否为负责人
      const assignChargeUsers = this.cleaningInfo.assignChargeUser ? this.cleaningInfo.assignChargeUser.split(',') : [];
      return assignChargeUsers.includes(String(this.userId));
    }
  },
  onLoad(options) {
    // 设置状态栏高度
    this.statusBarHeight = uni.getSystemInfoSync().statusBarHeight;

    if (options.id) {
      this.cleaningId = options.id;
    }

    // 获取当前登录用户ID
    this.userId = this.$store.state.user.userId;

    // 获取字典数据
    this.getDicts();
  },
  methods: {
    // 获取字典数据
    async getDicts() {
      try {
        const response = await getDictData();
        // 使用字典插件解析字典数据
        this.dictData = this.$dict.parseDictData(response.data);
        // 获取清洗详情
        this.getCleaningDetail();
      } catch (error) {
        console.error('获取字典数据失败:', error);
        uni.showToast({
          title: '获取字典数据失败',
          icon: 'none'
        });
        this.loading = false;
      }
    },

    // 获取清洗详情
    getCleaningDetail() {
      if (!this.cleaningId) {
        uni.showToast({
          title: '缺少清洗任务ID',
          icon: 'none'
        });
        return;
      }
      
      this.loading = true;
      
      Promise.all([
        getCleaning(this.cleaningId),
        getCleaningEquipments(this.cleaningId)
      ]).then(([cleaningResponse, equipmentResponse]) => {
        this.cleaningInfo = cleaningResponse.data;
        this.equipmentList = equipmentResponse.data || [];
        this.loading = false;
      }).catch(error => {
        console.error('获取清洗详情失败:', error);
        uni.showToast({
          title: '获取清洗详情失败',
          icon: 'none'
        });
        this.loading = false;
      });
    },

    // 刷新数据
    refreshData() {
      this.getCleaningDetail();
    },

    // 返回上一页
    goBack() {
      uni.navigateBack();
    },

    // 接收任务
    handleReceive() {
      uni.navigateTo({
        url: `/pages/cleaning/receive?id=${this.cleaningId}`
      });
    },

    // 执行任务
    handleExecute() {
      uni.navigateTo({
        url: `/pages/cleaning/execute?id=${this.cleaningId}`
      });
    },

    // 获取状态标签
    getStatusLabel(status) {
      if (!status) return '未知';
      return this.$dict.getDictLabel('cleaning_status', status, '未知');
    },

    // 获取状态样式类
    getStatusClass(status) {
      const statusClassMap = {
        '0': 'status-pending',    // 待处理
        '2': 'status-received',   // 进行中
        '4': 'status-submitted',  // 已完成
        '6': 'status-reviewed'    // 已审核
      };
      return statusClassMap[status] || 'status-unknown';
    },

    // 获取状态图标
    getStatusIcon(status) {
      const statusIconMap = {
        '0': 'notification-filled',
        '2': 'staff-filled',
        '4': 'checkbox-filled',
        '6': 'medal-filled'
      };
      return statusIconMap[status] || 'info-filled';
    },

    // 获取设备类型标签
    getEquipmentTypeLabel(type) {
      if (!type || !this.dictData.equipment_type) return '未知类型';
      return this.$dict.getDictLabel('equipment_type', type, '未知类型');
    },

    // 获取清洗方式标签
    getCleaningMethodLabel(method) {
      if (!method) return '未知';
      return this.$dict.getDictLabel('cleaning_method', method, '未知');
    },

    // 获取积尘厚度标签
    getDustThicknessLabel(thickness) {
      if (!thickness) return '未知';
      return this.$dict.getDictLabel('cleaning_dust_thickness', thickness, '未知');
    },

    // 获取设备清洗状态
    getEquipmentStatusClass(equipment) {
      if (!equipment.cleaningTime) {
        return 'equipment-status-pending';
      } else if (equipment.cleaningTime && equipment.afterImage) {
        return 'equipment-status-completed';
      } else {
        return 'equipment-status-incomplete';
      }
    },

    // 获取设备状态图标
    getEquipmentStatusIcon(equipment) {
      if (!equipment.cleaningTime) {
        return 'notification-filled';
      } else if (equipment.cleaningTime && equipment.afterImage) {
        return 'checkbox-filled';
      } else {
        return 'info-filled';
      }
    },

    // 获取设备状态文本
    getEquipmentStatusText(equipment) {
      if (!equipment.cleaningTime) {
        return '未清洗';
      } else if (equipment.cleaningTime && equipment.afterImage) {
        return '已完成';
      } else {
        return '清洗中';
      }
    },

    // 格式化日期
    formatDate(dateString) {
      if (!dateString) return '未设置';
      const date = new Date(dateString);
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
    },

    // 获取图片URL
    getImageUrl(path) {
      return getImageUrl(path);
    },

    // 获取清洗前图片列表
    getBeforeImageList(beforeImage) {
      if (!beforeImage) return [];
      return getImageList(beforeImage);
    },

    // 获取清洗后图片列表
    getAfterImageList(afterImage) {
      if (!afterImage) return [];
      return getImageList(afterImage);
    },

    // 预览清洗前图片
    previewBeforeImages(beforeImage, currentIndex) {
      if (!beforeImage) return;
      const urls = getImageList(beforeImage);
      uni.previewImage({
        urls: urls,
        current: currentIndex
      });
    },

    // 预览清洗后图片
    previewAfterImages(afterImage, currentIndex) {
      if (!afterImage) return;
      const urls = getImageList(afterImage);
      uni.previewImage({
        urls: urls,
        current: currentIndex
      });
    },

    // 预览图片（保留兼容性）
    previewImage(url) {
      uni.previewImage({
        urls: [url],
        current: url
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #ffffff;
}

/* 顶部导航栏 */
.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: linear-gradient(135deg, #1a56b3 0%, #3a7bd5 100%);
  padding: 10px 15px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.back-icon {
  padding: 5px;
}

.header-title {
  color: #ffffff;
  font-size: 18px;
  font-weight: bold;
}

/* 内容区域 */
.content {
  flex: 1;
}

.cleaning-detail {
  padding: 0;
}

/* 状态横幅 */
.status-banner {
  padding: 15px 16px;
  color: #ffffff;
  position: relative;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.status-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-left {
  display: flex;
  align-items: center;
}

.status-icon {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
}

.status-text {
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0.5px;
}

/* 上方操作按钮 */
.action-buttons-top {
  display: flex;
  gap: 10px;
}

.action-button-top {
  background-color: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.4);
  color: #ffffff;
  font-size: 13px;
  padding: 4px 12px;
  border-radius: 15px;
  height: auto;
  line-height: 1.5;
  font-weight: normal;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.action-button-top:active {
  transform: scale(0.95);
  background-color: rgba(255, 255, 255, 0.3);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* 非负责人提示 */
.action-tip {
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 15px;
  padding: 4px 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.action-tip-text {
  color: #ffffff;
  font-size: 13px;
  opacity: 0.9;
}

/* 分区样式 */
.detail-card {
  background-color: #ffffff;
  margin-bottom: 10px;
  position: relative;
}

.card-header {
  display: flex;
  align-items: center;
  padding: 15px 16px 10px;
  position: relative;
}

.card-title {
  font-size: 16px;
  font-weight: bold;
  margin-left: 8px;
  color: #1a56b3;
}

.card-content {
  padding: 5px 16px 15px;
}

/* 设备列表卡片的内容区域特殊处理 */
.detail-card:nth-child(3) .card-content {
  padding: 8px 16px;
}

/* 信息项样式 */
.info-item {
  display: flex;
  margin-bottom: 10px;
  align-items: center;
  position: relative;
  border-bottom: 1px solid #f9f9f9;
  padding-bottom: 10px;
}

.info-item:last-child {
  margin-bottom: 0;
  border-bottom: none;
  padding-bottom: 0;
}

.info-label {
  width: 90px;
  color: #666;
  font-size: 14px;
  flex-shrink: 0;
  position: relative;
  padding-left: 10px;
}

.info-label::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 14px;
  background-color: #1a56b3;
  opacity: 0.6;
  border-radius: 1.5px;
}

.info-value {
  flex: 1;
  color: #333;
  font-size: 14px;
  word-break: break-all;
  font-weight: 500;
}

/* 设备列表样式 */
.equipment-item {
  border-bottom: 1px solid #f0f0f0;
  position: relative;
  background-color: #ffffff;
  margin-bottom: 10px;
}

.equipment-item:last-child {
  margin-bottom: 0;
  border-bottom: none;
}

.equipment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f5f5f5;
}

.equipment-name {
  font-size: 15px;
  font-weight: bold;
  color: #333;
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  position: relative;
  padding-left: 12px;
}

.equipment-name::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 16px;
  background-color: #1a56b3;
  border-radius: 2px;
}

.equipment-status {
  display: flex;
  align-items: center;
  padding: 2px 6px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
  color: #FFFFFF;
}

.equipment-status text {
  margin-left: 4px;
}

.equipment-info {
  margin-bottom: 8px;
}

.equipment-code {
  color: #666666;
  font-size: 12px;
}

/* 清洗详情 */
.cleaning-details {
  padding: 8px 0;
  margin-top: 8px;
}

/* 清洗结果行样式 */
.cleaning-result-row {
  width: 100%;
  margin-bottom: 8px;
  display: flex;
  align-items: flex-start;
  padding: 0;
}

.result-main {
  display: flex;
  flex-direction: column;
  flex: 1;
  gap: 2px;
  min-width: 0;
}

.cleaning-info-compact {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.cleaning-time-text {
  font-size: 14px;
  color: #1a56b3;
  font-weight: 600;
  line-height: 1.4;
  margin-bottom: 6px;
  position: relative;
  padding-left: 12px;
}
.cleaning-method-dust {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  margin-top: 4px;
  margin-left: 12px;
}

.cleaning-method-text {
  font-size: 12px;
  color: #1a56b3;
  background-color: #f0f7ff;
  padding: 2px 6px;
  border-radius: 4px;
  line-height: 1.3;
  font-weight: 500;
}

.cleaning-dust-text {
  font-size: 12px;
  color: #d97706;
  background-color: #fff8f0;
  padding: 2px 6px;
  border-radius: 4px;
  line-height: 1.3;
  font-weight: 500;
}

.cleaning-remark-text {
  font-size: 12px;
  color: #666;
  line-height: 1.4;
  word-wrap: break-word;
  margin-top: 6px;
  margin-left: 12px;
  padding-left: 8px;
  border-left: 2px solid #e0e0e0;
  font-style: italic;
}

/* 清洗照片行样式 */
.cleaning-photo-row {
  width: 100%;
  margin-top: 6px;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  padding: 0;
}

.cleaning-photo-row:last-child {
  margin-bottom: 0;
}

.photo-row-content {
  display: flex;
  align-items: center;
  width: 100%;
  gap: 12px;
}

.photo-row-title {
  font-size: 12px;
  font-weight: 500;
  color: #000000;
  min-width: 50px;
  flex-shrink: 0;
  position: relative;
  padding-left: 12px;
}

.photo-row-images {
  display: flex;
  gap: 8px;
  flex: 1;
  flex-wrap: wrap;
}

.photo-container {
  position: relative;
  cursor: pointer;
}

.cleaning-photo-compact {
  width: 60px;
  height: 45px;
  border-radius: 4px;
  border: 1px solid #e0e0e0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
  transition: all 0.2s ease;
  cursor: pointer;
  position: relative;
}

.photo-more-indicator {
  position: absolute;
  top: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  font-size: 10px;
  padding: 2px 4px;
  border-radius: 0 4px 0 4px;
  line-height: 1;
}

/* 设备状态颜色 */
.equipment-status-pending {
  background-color: #f7b84b;
}

.equipment-status-incomplete {
  background-color: #3498db;
}

.equipment-status-completed {
  background-color: #27ae60;
}

/* 状态标签 */
.status-tag {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 4px;
  color: #ffffff;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
}

.status-pending {
  background: linear-gradient(135deg, #e6a23c 0%, #f0b95a 100%);
}

.status-received {
  background: linear-gradient(135deg, #1a56b3 0%, #3a7bd5 100%);
}

.status-submitted {
  background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%);
}

.status-reviewed {
  background: linear-gradient(135deg, #606266 0%, #909399 100%);
}

.status-unknown {
  background: linear-gradient(135deg, #f56c6c 0%, #f78989 100%);
}

.empty-equipment {
  text-align: center;
  padding: 20px 0;
  color: #999;
  font-size: 14px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 10px;
}
</style>
