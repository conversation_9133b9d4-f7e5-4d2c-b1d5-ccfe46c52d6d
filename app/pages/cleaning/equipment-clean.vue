<template>
  <view class="container">
    <!-- 顶部导航栏 -->
    <view class="header" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="header-content">
        <view class="back-icon" @click="goBack">
          <uni-icons type="left" size="18" color="#FFFFFF"></uni-icons>
        </view>
        <view class="header-title">设备清洗</view>
        <view style="width: 20px;"></view>
      </view>
    </view>

    <!-- 内容区域 -->
    <scroll-view scroll-y class="content" :style="{ paddingTop: (statusBarHeight + 44) + 'px' }">
      <view v-if="equipmentInfo" class="cleaning-detail">
        <!-- 任务状态标识 -->
        <view class="status-banner status-cleaning">
          <view class="status-content">
            <view class="status-left">
              <view class="status-icon">
                <uni-icons type="gear-filled" size="18" color="#FFFFFF"></uni-icons>
              </view>
              <text class="status-text">清洗中</text>
            </view>
            <!-- 操作按钮 -->
            <view class="action-buttons-top">
              <button class="action-button-top cancel" @click="goBack">
                取消
              </button>
              <button class="action-button-top save" @click="handleSave">
                保存
              </button>
            </view>
          </view>
        </view>

        <!-- 设备信息区域 -->
        <view class="detail-card">
          <view class="card-header">
            <uni-icons type="gear" size="16" color="#1a56b3"></uni-icons>
            <text class="card-title">设备信息</text>
          </view>
          <view class="card-content">
            <view class="info-item">
              <text class="info-label">设备名称</text>
              <text class="info-value">{{ equipmentInfo.equipmentName }}</text>
            </view>
            <view class="info-item" v-if="equipmentInfo.equipmentCode">
              <text class="info-label">设备编号</text>
              <text class="info-value">{{ equipmentInfo.equipmentCode }}</text>
            </view>
            <view class="info-item">
              <text class="info-label">设备类型</text>
              <text class="info-value">{{ getEquipmentTypeLabel(equipmentInfo.equipmentType) }}</text>
            </view>
            <view class="info-item" v-if="equipmentInfo.model">
              <text class="info-label">设备型号</text>
              <text class="info-value">{{ equipmentInfo.model }}</text>
            </view>
          </view>
        </view>

        <!-- 清洗信息表单 -->
        <view class="detail-card">
          <view class="card-header">
            <uni-icons type="checkmarkempty" size="16" color="#1a56b3"></uni-icons>
            <text class="card-title">清洗信息</text>
          </view>
          <view class="card-content">
            <!-- 清洗前照片 -->
            <view class="form-item">
              <view class="form-label">
                <uni-icons type="camera-filled" size="14" color="#1a56b3"></uni-icons>
                <text class="label-text">清洗前照片</text>
                <text class="required">*</text>
              </view>
              <uni-file-picker
                v-model="beforeImageList"
                file-mediatype="image"
                mode="grid"
                :limit="6"
                :image-styles="imageStyles"
                :auto-upload="false"
                @select="(e) => selectBeforeImages(e)"
                @delete="(e) => deleteBeforeImage(e)"
                :source-type="['camera']"
              />
            </view>

            <!-- 清洗方式 -->
            <view class="form-item">
              <view class="form-label">
                <uni-icons type="settings-filled" size="14" color="#1a56b3"></uni-icons>
                <text class="label-text">清洗方式</text>
                <text class="required">*</text>
              </view>
              <radio-group @change="handleCleaningMethodChange">
                <label class="radio-label" v-for="(option, index) in cleaningMethodOptions" :key="index">
                  <radio :value="option.value" color="#67c23a" :checked="cleaningData.cleaningMethod === option.value" />
                  <text>{{ option.label }}</text>
                </label>
              </radio-group>
            </view>

            <!-- 积尘厚度 -->
            <view class="form-item">
              <view class="form-label">
                <uni-icons type="bars" size="14" color="#1a56b3"></uni-icons>
                <text class="label-text">积尘厚度</text>
                <text class="required">*</text>
              </view>
              <radio-group @change="handleDustThicknessChange">
                <label class="radio-label" v-for="(option, index) in dustThicknessOptions" :key="index">
                  <radio :value="option.value" color="#67c23a" :checked="cleaningData.cleaningDustThickness === option.value" />
                  <text>{{ option.label }}</text>
                </label>
              </radio-group>
            </view>

            <!-- 清洗后照片 -->
            <view class="form-item">
              <view class="form-label">
                <uni-icons type="camera-filled" size="14" color="#1a56b3"></uni-icons>
                <text class="label-text">清洗后照片</text>
                <text class="required">*</text>
              </view>
              <uni-file-picker
                v-model="afterImageList"
                file-mediatype="image"
                mode="grid"
                :limit="6"
                :image-styles="imageStyles"
                :auto-upload="false"
                @select="(e) => selectAfterImages(e)"
                @delete="(e) => deleteAfterImage(e)"
                :source-type="['camera']"
              />
            </view>

            <!-- 清洗备注 -->
            <view class="form-item">
              <view class="form-label">
                <uni-icons type="chatbubble-filled" size="14" color="#1a56b3"></uni-icons>
                <text class="label-text">清洗备注</text>
              </view>
              <textarea
                class="remark-textarea"
                placeholder="请输入清洗备注信息（选填）"
                v-model="cleaningData.cleaningRemark"
                maxlength="200"
              ></textarea>
            </view>
          </view>
        </view>
      </view>

      <loading-container
        v-else
        :loading="loading"
        :error="!loading && !equipmentInfo"
        loadingText="正在加载设备信息..."
        errorText="获取设备信息失败"
        @retry="refreshData"
      />
    </scroll-view>

    <!-- 隐藏的canvas用于水印处理 -->
    <canvas
      v-if="waterMarkParams.display"
      canvas-id="watermarkCanvas"
      :style="canvasStyle"
    ></canvas>
  </view>
</template>

<script>
import { getCleaning, getCleaningEquipments, updateEquipmentCleaning } from '@/api/system/cleaning'
import { getEquipment } from '@/api/system/equipment'
import { getDictData } from '@/api/system/dictData'
import { uploadFile } from '@/api/system/common'
import LoadingContainer from '@/components/LoadingContainer'
import { getRelativePath, getFullUrl } from '@/utils/imageUrlUtils'
import { addInspectionWatermark } from '@/utils/watermark'

export default {
  components: {
    LoadingContainer
  },
  data() {
    return {
      statusBarHeight: 0,
      cleaningId: null,
      equipmentId: null,
      cleaningInfo: null,
      equipmentInfo: null,
      cleaningEquipment: null,
      cleaningData: {
        beforeImage: '',
        afterImage: '',
        cleaningMethod: '',
        cleaningDustThickness: '',
        cleaningRemark: ''
      },
      beforeImageList: [],
      afterImageList: [],
      beforeImageUrls: [],
      afterImageUrls: [],
      imageStyles: {
        width: 80,
        height: 80,
        border: {
          radius: '4px'
        }
      },
      loading: true,
      dictData: {},
      userId: null,
      waterMarkParams: {
        display: false,
        canvasWidth: 300,
        canvasHeight: 225,
      }
    }
  },
  computed: {
    canvasStyle() {
      return {
        position: 'fixed',
        left: '9999px',
        width: this.waterMarkParams.canvasWidth + 'px',
        height: this.waterMarkParams.canvasHeight + 'px',
      }
    },
    cleaningMethodOptions() {
      return this.dictData.cleaning_method || [];
    },
    dustThicknessOptions() {
      return this.dictData.cleaning_dust_thickness || [];
    }
  },
  onLoad(options) {
    // 设置状态栏高度
    this.statusBarHeight = uni.getSystemInfoSync().statusBarHeight;

    // 获取当前登录用户ID
    this.userId = this.$store.state.user.userId;

    if (options.cleaningId && options.equipmentId) {
      this.cleaningId = options.cleaningId;
      this.equipmentId = options.equipmentId;
      this.getDicts();
    } else {
      this.loading = false;
      uni.showToast({
        title: '缺少必要参数',
        icon: 'none'
      });
    }
  },
  methods: {
    // 获取字典数据
    async getDicts() {
      try {
        const response = await getDictData();
        this.dictData = this.$dict.parseDictData(response.data);
        this.loadData();
      } catch (error) {
        console.error('获取字典数据失败:', error);
        uni.showToast({
          title: '获取字典数据失败',
          icon: 'none'
        });
        this.loading = false;
      }
    },

    // 加载数据
    async loadData() {
      try {
        // 并行加载数据
        const [cleaningResponse, equipmentResponse, cleaningEquipmentsResponse] = await Promise.all([
          getCleaning(this.cleaningId),
          getEquipment(this.equipmentId),
          getCleaningEquipments(this.cleaningId)
        ]);

        this.cleaningInfo = cleaningResponse.data;
        this.equipmentInfo = equipmentResponse.data;

        // 查找当前设备的清洗记录
        const equipmentList = cleaningEquipmentsResponse.data || [];
        this.cleaningEquipment = equipmentList.find(item =>
          item.equipmentId.toString() === this.equipmentId.toString()
        );

        // 加载已有数据
        if (this.cleaningEquipment) {
          this.loadExistingCleaningData();
        }
      } catch (error) {
        console.error('加载数据失败:', error);
        uni.showToast({
          title: '加载数据失败',
          icon: 'none'
        });
      } finally {
        this.loading = false;
      }
    },

    // 加载已有的清洗数据
    loadExistingCleaningData() {
      if (this.cleaningEquipment) {
        this.cleaningData = {
          beforeImage: this.cleaningEquipment.beforeImage || '',
          afterImage: this.cleaningEquipment.afterImage || '',
          cleaningMethod: this.cleaningEquipment.cleaningMethod || '',
          cleaningDustThickness: this.cleaningEquipment.cleaningDustThickness || '',
          cleaningRemark: this.cleaningEquipment.cleaningRemark || ''
        };

        // 处理清洗前图片
        if (this.cleaningData.beforeImage) {
          const beforeUrls = this.cleaningData.beforeImage.split(',').filter(url => url.trim());
          this.beforeImageList = beforeUrls.map((url, index) => ({
            url: getFullUrl(url),
            name: `before_image_${index}.jpg`,
            extname: 'jpg',
            size: 0
          }));
          this.beforeImageUrls = beforeUrls;
        }

        // 处理清洗后图片
        if (this.cleaningData.afterImage) {
          const afterUrls = this.cleaningData.afterImage.split(',').filter(url => url.trim());
          this.afterImageList = afterUrls.map((url, index) => ({
            url: getFullUrl(url),
            name: `after_image_${index}.jpg`,
            extname: 'jpg',
            size: 0
          }));
          this.afterImageUrls = afterUrls;
        }
      }
    },

    // 获取设备类型标签
    getEquipmentTypeLabel(type) {
      if (!type || !this.dictData.equipment_type) return '未知类型';
      return this.$dict.getDictLabel('equipment_type', type, '未知类型');
    },

    // 处理清洗方式变化
    handleCleaningMethodChange(e) {
      this.cleaningData.cleaningMethod = e.detail.value;
    },

    // 处理积尘厚度变化
    handleDustThicknessChange(e) {
      this.cleaningData.cleaningDustThickness = e.detail.value;
    },

    // 选择清洗前图片
    selectBeforeImages(e) {
      this.uploadImages(e.tempFiles, 'before');
    },

    // 选择清洗后图片
    selectAfterImages(e) {
      this.uploadImages(e.tempFiles, 'after');
    },

    // 删除清洗前图片
    deleteBeforeImage(e) {
      const index = e.index;
      this.beforeImageUrls.splice(index, 1);
      this.cleaningData.beforeImage = this.beforeImageUrls.join(',');
    },

    // 删除清洗后图片
    deleteAfterImage(e) {
      const index = e.index;
      this.afterImageUrls.splice(index, 1);
      this.cleaningData.afterImage = this.afterImageUrls.join(',');
    },

    // 上传图片
    async uploadImages(tempFiles, type) {
      if (tempFiles.length === 0) return;

      uni.showLoading({
        title: '正在上传...',
        mask: true
      });

      try {
        const uploadPromises = tempFiles.map(async (file) => {
          // 生成自定义文件名：QX_{设备名称}_{时间戳}
          const timestamp = this.generateTimestamp();
          const equipmentName = this.equipmentInfo.equipmentName || 'Unknown';
          const cleanEquipmentName = equipmentName.replace(/[^\u4e00-\u9fa5a-zA-Z0-9_]/g, '_');
          const prefix = type === 'before' ? 'QX_BEFORE' : 'QX_AFTER';
          const customFileName = `${prefix}_${cleanEquipmentName}_${timestamp}`;

          // 准备水印信息
          const watermarkInfo = {
            taskName: this.cleaningInfo?.cleaningName || '设备清洗',
            equipmentName: this.equipmentInfo?.equipmentName || '未知设备',
            time: this.formatDateTime(new Date())
          };

          // 为图片添加水印
          const watermarkedImagePath = await addInspectionWatermark(file.path, watermarkInfo, this);

          // 构建formData
          const formData = {
            customFileName: customFileName
          };

          // 上传图片
          const res = await uploadFile(watermarkedImagePath, formData);

          if (res.code === 200) {
            return res.url;
          } else {
            throw new Error(res.msg || '上传失败');
          }
        });

        const uploadedUrls = await Promise.all(uploadPromises);

        // 更新对应的图片数组
        if (type === 'before') {
          this.beforeImageUrls = [...this.beforeImageUrls, ...uploadedUrls.map(url => getRelativePath(url))];
          this.cleaningData.beforeImage = this.beforeImageUrls.join(',');
        } else {
          this.afterImageUrls = [...this.afterImageUrls, ...uploadedUrls.map(url => getRelativePath(url))];
          this.cleaningData.afterImage = this.afterImageUrls.join(',');
        }

        uni.hideLoading();
        uni.showToast({
          title: '图片上传成功',
          icon: 'success'
        });
      } catch (error) {
        uni.hideLoading();
        console.error('图片上传失败:', error);
        uni.showToast({
          title: '图片上传失败',
          icon: 'none'
        });
      }
    },

    // 生成时间戳
    generateTimestamp() {
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, '0');
      const day = String(now.getDate()).padStart(2, '0');
      const hours = String(now.getHours()).padStart(2, '0');
      const minutes = String(now.getMinutes()).padStart(2, '0');
      const seconds = String(now.getSeconds()).padStart(2, '0');
      return `${year}${month}${day}${hours}${minutes}${seconds}`;
    },

    // 格式化日期时间
    formatDateTime(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },

    // 验证表单完整性
    validateForm() {
      const errors = [];

      if (!this.cleaningData.beforeImage) {
        errors.push('请上传清洗前照片');
      }

      if (!this.cleaningData.afterImage) {
        errors.push('请上传清洗后照片');
      }

      if (!this.cleaningData.cleaningMethod) {
        errors.push('请选择清洗方式');
      }

      if (!this.cleaningData.cleaningDustThickness) {
        errors.push('请选择积尘厚度');
      }

      return errors;
    },

    // 保存清洗数据
    async handleSave() {
      try {
        // 检查必填项是否完整
        const errors = this.validateForm();

        if (errors.length > 0) {
          // 显示详细的错误信息
          let message = '以下信息需要完善：\n\n';
          errors.forEach((error, index) => {
            message += `${index + 1}. ${error}\n`;
          });

          message += '\n是否仍要保存当前进度？';

          // 显示确认对话框，允许用户选择是否继续保存
          const result = await new Promise((resolve) => {
            uni.showModal({
              title: '清洗信息不完整',
              content: message,
              showCancel: true,
              cancelText: '继续编辑',
              confirmText: '仍要保存',
              success: (res) => {
                resolve(res.confirm);
              },
              fail: () => {
                resolve(false);
              }
            });
          });

          // 如果用户选择不保存，直接返回
          if (!result) {
            return;
          }
        }

        uni.showLoading({
          title: '保存中...'
        });

        const cleaningData = {
          cleaningId: this.cleaningId,
          equipmentId: this.equipmentId,
          userId: this.userId,
          beforeImage: this.cleaningData.beforeImage,
          afterImage: this.cleaningData.afterImage,
          cleaningMethod: this.cleaningData.cleaningMethod,
          cleaningDustThickness: this.cleaningData.cleaningDustThickness,
          cleaningRemark: this.cleaningData.cleaningRemark,
          cleaningTime: this.formatDateTime(new Date())
        };

        const response = await updateEquipmentCleaning(cleaningData);

        if (response.code === 200) {
          uni.hideLoading();

          // 根据完成状态显示不同的提示信息
          const isCompleted = errors.length === 0;
          const toastTitle = isCompleted ? '清洗保存成功' : '进度已保存';
          const toastIcon = isCompleted ? 'success' : 'none';

          uni.showToast({
            title: toastTitle,
            icon: toastIcon,
            duration: 2000,
            success: () => {
              setTimeout(() => {
                // 获取页面栈
                const pages = getCurrentPages();
                // 获取上一个页面
                const prevPage = pages[pages.length - 2];
                // 如果上一个页面存在且有refreshData方法，调用它刷新数据
                if (prevPage && prevPage.$vm && prevPage.$vm.refreshData) {
                  prevPage.$vm.refreshData();
                }
                // 返回上一页
                uni.navigateBack();
              }, 1000);
            }
          });
        } else {
          throw new Error(response.msg || '保存失败');
        }
      } catch (error) {
        uni.hideLoading();
        console.error('保存清洗数据失败:', error);
        uni.showToast({
          title: error.message || '保存失败',
          icon: 'none'
        });
      }
    },

    // 返回上一页
    goBack() {
      // 获取页面栈
      const pages = getCurrentPages();
      // 获取上一个页面
      const prevPage = pages[pages.length - 2];
      // 如果上一个页面存在且有refreshData方法，调用它刷新数据
      if (prevPage && prevPage.$vm && prevPage.$vm.refreshData) {
        prevPage.$vm.refreshData();
      }
      uni.navigateBack();
    },

    // 刷新数据
    refreshData() {
      this.loading = true;
      this.getDicts();
      uni.showToast({
        title: '刷新中',
        icon: 'loading',
        duration: 500
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #ffffff;
}

/* 顶部导航栏 */
.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: linear-gradient(135deg, #1a56b3 0%, #3a7bd5 100%);
  padding: 10px 15px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.back-icon {
  padding: 5px;
}

.header-title {
  color: #ffffff;
  font-size: 18px;
  font-weight: bold;
}

/* 内容区域 */
.content {
  flex: 1;
}

.cleaning-detail {
  padding: 0;
}

/* 状态横幅 */
.status-banner {
  padding: 15px 16px;
  color: #ffffff;
  position: relative;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.status-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-left {
  display: flex;
  align-items: center;
}

.status-icon {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
}

.status-text {
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0.5px;
}

/* 上方操作按钮 */
.action-buttons-top {
  display: flex;
  gap: 10px;
}

.action-button-top {
  background-color: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.4);
  color: #ffffff;
  font-size: 13px;
  padding: 4px 12px;
  border-radius: 15px;
  height: auto;
  line-height: 1.5;
  font-weight: normal;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.action-button-top:active {
  transform: scale(0.95);
  background-color: rgba(255, 255, 255, 0.3);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.action-button-top.cancel {
  background-color: rgba(255, 255, 255, 0.1);
}

.action-button-top.save {
  background-color: rgba(255, 255, 255, 0.2);
}

/* 分区样式 */
.detail-card {
  background-color: #ffffff;
  margin-bottom: 10px;
  position: relative;
}

.card-header {
  display: flex;
  align-items: center;
  padding: 15px 16px 10px;
  position: relative;
}

.card-title {
  font-size: 16px;
  font-weight: bold;
  margin-left: 8px;
  color: #1a56b3;
}

.card-content {
  padding: 5px 16px 15px;
}

/* 基本信息样式 */
.info-item {
  display: flex;
  margin-bottom: 10px;
  align-items: center;
  position: relative;
  border-bottom: 1px solid #f9f9f9;
  padding-bottom: 10px;
}

.info-item:last-child {
  margin-bottom: 0;
  border-bottom: none;
  padding-bottom: 0;
}

.info-label {
  width: 80px;
  color: #666;
  font-size: 14px;
  flex-shrink: 0;
  position: relative;
  padding-left: 10px;
}

.info-label::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 14px;
  background-color: #1a56b3;
  opacity: 0.6;
  border-radius: 1.5px;
}

.info-value {
  flex: 1;
  color: #333;
  font-size: 14px;
  word-break: break-all;
  font-weight: 500;
}

/* 表单样式 */
.form-item {
  margin-bottom: 24px;
  position: relative;
}

.form-label {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  padding: 8px 0;
}

.label-text {
  font-size: 15px;
  color: #333;
  font-weight: 600;
  margin-left: 8px;
  letter-spacing: 0.3px;
}

.required {
  color: #ff4757;
  margin-left: 6px;
  font-weight: bold;
  font-size: 16px;
}

/* 单选按钮样式 */
.radio-label {
  display: flex;
  align-items: center;
  margin-right: 20px;
  margin-bottom: 12px;
  padding: 8px 12px;
  border-radius: 6px;
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
}

.radio-label:hover {
  background-color: #f0f7ff;
  border-color: #409eff;
}

.radio-label text {
  margin-left: 8px;
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

/* 备注输入框样式 */
.remark-textarea {
  width: 100%;
  min-height: 100px;
  padding: 12px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  font-size: 14px;
  color: #333;
  background-color: #fafbfc;
  resize: none;
  transition: all 0.3s ease;
  line-height: 1.5;
}

.remark-textarea:focus {
  border-color: #409eff;
  background-color: #fff;
  outline: none;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
}

/* 文件上传组件样式优化 */
:deep(.uni-file-picker__container) {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

:deep(.file-picker__box) {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

:deep(.file-picker__box:hover) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

:deep(.is-add) {
  border: 2px dashed #409eff;
  background-color: #f0f7ff;
  border-radius: 8px;
  transition: all 0.3s ease;
}

:deep(.is-add:hover) {
  border-color: #1a56b3;
  background-color: #e6f2ff;
}

/* 状态样式 */
.status-cleaning {
  background: linear-gradient(135deg, #409eff 0%, #64b0ff 100%);
}
</style>
