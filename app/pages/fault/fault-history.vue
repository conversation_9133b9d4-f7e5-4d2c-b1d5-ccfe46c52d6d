<template>
  <view class="container">
    <!-- 顶部导航栏 -->
    <view class="header" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="header-content">
        <view class="back-icon" @click="goBack">
          <uni-icons type="left" size="18" color="#FFFFFF"></uni-icons>
        </view>
        <view class="header-title">故障历史</view>
        <view class="header-right">
          <view class="refresh-icon" @click="refreshData">
            <uni-icons type="refresh" size="18" color="#FFFFFF"></uni-icons>
          </view>
        </view>
      </view>
    </view>

    <!-- 内容区域 -->
    <scroll-view scroll-y class="content" :style="{ paddingTop: (statusBarHeight + 44) + 'px' }">
      <!-- 搜索栏 -->
      <view class="search-container">
        <view class="search-box">
          <uni-icons type="search" size="16" color="#999999"></uni-icons>
          <input class="search-input" type="text" v-model="searchKeyword" placeholder="搜索故障名称" @input="handleSearch" />
          <uni-icons v-if="searchKeyword" type="clear" size="16" color="#999999" @click="clearSearch"></uni-icons>
        </view>
        <uni-data-select v-model="selectedStationValue" :localdata="stationOptions" placeholder="选择电站"
          @change="handleStationChange" class="station-select"></uni-data-select>
      </view>

      <!-- 筛选选项 -->
      <view class="filter-container">
        <view class="filter-option">
          <view class="checkbox-label" @click="toggleMyReports">
            <view class="custom-checkbox" :class="{ 'checked': showMyReportsOnly }">
              <uni-icons v-if="showMyReportsOnly" type="checkmarkempty" size="12" color="#ffffff"></uni-icons>
            </view>
            <text class="checkbox-text">仅看本人上报</text>
          </view>
        </view>
      </view>

      <!-- 故障历史列表 -->
      <view class="history-list">
        <scroll-view scroll-y class="fault-scroll">
          <view v-if="faultList.length > 0">
            <view
              class="fault-card"
              v-for="(item, index) in faultList"
              :key="index"
              @click="goToDetail(item)"
            >
              <view class="card-status-indicator" :class="getStatusClass(item.status)"></view>
              <view class="card-content">
                <view class="card-header">
                  <text class="fault-name">{{ item.faultName }}</text>
                  <view class="status-tag" :class="getStatusClass(item.status)">
                    <uni-icons :type="getStatusIcon(item.status)" size="12" color="#ffffff" class="status-icon"></uni-icons>
                    <text>{{ getStatusLabel(item.status) }}</text>
                  </view>
                </view>
                <view class="card-body">
                  <view class="info-row">
                    <view class="info-icon-container">
                      <uni-icons type="location-filled" size="14" color="#1a56b3" class="info-icon"></uni-icons>
                    </view>
                    <text class="info-label">所属电站:</text>
                    <text class="info-value">{{ item.station ? item.station.stationName : '未知电站' }}</text>
                  </view>
                  <view class="info-row">
                    <view class="info-icon-container">
                      <uni-icons type="gear-filled" size="14" color="#1a56b3" class="info-icon"></uni-icons>
                    </view>
                    <text class="info-label">故障设备:</text>
                    <text class="info-value">{{ getEquipmentNames(item.equipmentListId) }}</text>
                  </view>
                  <view class="info-row">
                    <view class="info-icon-container">
                      <uni-icons type="calendar-filled" size="14" color="#1a56b3" class="info-icon"></uni-icons>
                    </view>
                    <text class="info-label">发现时间:</text>
                    <text class="info-value">{{ formatDate(item.discoverTime) }}</text>
                  </view>
                  <view class="info-row">
                    <view class="info-icon-container">
                      <uni-icons type="flag-filled" size="14" color="#1a56b3" class="info-icon"></uni-icons>
                    </view>
                    <text class="info-label">故障等级:</text>
                    <text class="info-value">{{ getFaultLevelLabel(item.faultLevel) }}</text>
                  </view>
                  <view class="info-row" v-if="item.faultDescription">
                    <view class="info-icon-container">
                      <uni-icons type="compose" size="14" color="#1a56b3" class="info-icon"></uni-icons>
                    </view>
                    <text class="info-label">故障描述:</text>
                    <text class="info-value description">{{ item.faultDescription }}</text>
                  </view>
                </view>
              </view>
            </view>
          </view>

          <!-- 空状态 -->
          <view v-else class="empty-state">
            <uni-icons type="notification" size="48" color="#cccccc"></uni-icons>
            <text class="empty-text">暂无故障记录</text>
            <text class="empty-tip">您还没有上报过任何故障</text>
          </view>

          <!-- 加载更多 -->
          <view v-if="hasMore && faultList.length > 0" class="load-more" @click="loadMore">
            <text class="load-more-text">加载更多</text>
          </view>
          <view v-else-if="!hasMore && faultList.length > 0" class="no-more">
            <text class="no-more-text">没有更多数据了</text>
          </view>
        </scroll-view>
      </view>

      <!-- 使用LoadingContainer组件 -->
      <loading-container
          v-if="loading"
          :loading="loading"
          loadingText="正在加载故障历史..."
          height="200"
      />
    </scroll-view>
  </view>
</template>

<script>
import { listFault } from '@/api/system/fault'
import { listStation } from '@/api/system/station'
import { listEquipmentByStationId } from '@/api/system/equipment'
import { getDictData } from '@/api/system/dictData'
import LoadingContainer from '@/components/LoadingContainer'

export default {
  components: {
    LoadingContainer
  },
  data() {
    return {
      statusBarHeight: 0,
      loading: false,
      // 搜索和筛选
      searchKeyword: '',
      selectedStationValue: null,
      showMyReportsOnly: false, // 仅看本人上报
      // 数据列表
      faultList: [],
      // 分页
      pageNum: 1,
      pageSize: 10,
      hasMore: true,
      // 选项数据
      stationOptions: [],
      equipmentMap: {}, // 设备ID到名称的映射
      // 字典数据
      dictData: {},
      faultLevelOptions: []
    }
  },
  onLoad() {
    // 设置状态栏高度
    this.statusBarHeight = uni.getSystemInfoSync().statusBarHeight
    
    // 初始化数据
    this.initData()
  },
  methods: {
    // 初始化数据
    async initData() {
      this.loading = true
      try {
        // 并行获取基础数据
        await Promise.all([
          this.getDicts(),
          this.getStationList()
        ])
        
        // 获取故障列表
        await this.getFaultList()
      } catch (error) {
        console.error('初始化数据失败:', error)
        uni.showToast({
          title: '数据加载失败',
          icon: 'none'
        })
      } finally {
        this.loading = false
      }
    },

    // 获取字典数据
    async getDicts() {
      try {
        const response = await getDictData()
        if (response.code === 200 && response.data) {
          this.dictData = response.data
          this.parseDictData()
        }
      } catch (error) {
        console.error('获取字典数据失败:', error)
      }
    },

    // 解析字典数据
    parseDictData() {
      try {
        // 故障等级
        if (this.dictData.fault_level && Array.isArray(this.dictData.fault_level)) {
          this.faultLevelOptions = this.dictData.fault_level.map(item => ({
            value: item.dictValue,
            text: item.dictLabel
          }))
        }
      } catch (error) {
        console.error('解析字典数据失败:', error)
        this.faultLevelOptions = []
      }
    },

    // 获取电站列表
    async getStationList() {
      try {
        const response = await listStation()
        if (response.code === 200 && response.data) {
          this.stationOptions = [
            { value: null, text: '全部电站' },
            ...response.data.map(station => ({
              value: station.stationId,
              text: station.stationName
            }))
          ]
          
          // 获取所有电站的设备信息
          await this.getAllEquipments(response.data)
        }
      } catch (error) {
        console.error('获取电站列表失败:', error)
      }
    },

    // 获取所有设备信息
    async getAllEquipments(stations) {
      try {
        const equipmentPromises = stations.map(station => 
          listEquipmentByStationId(station.stationId)
        )
        
        const responses = await Promise.all(equipmentPromises)
        
        // 构建设备映射
        responses.forEach(response => {
          if (response.code === 200 && response.data) {
            response.data.forEach(equipment => {
              this.equipmentMap[equipment.equipmentId] = equipment.equipmentName
            })
          }
        })
      } catch (error) {
        console.error('获取设备信息失败:', error)
      }
    },

    // 获取故障列表
    async getFaultList(isLoadMore = false) {
      try {
        if (!isLoadMore) {
          this.loading = true
          this.pageNum = 1
          this.faultList = []
          this.hasMore = true
        }

        const userId = this.$store.state.user.userId
        const params = {
          pageNum: this.pageNum,
          pageSize: this.pageSize
        }

        // 添加搜索条件
        if (this.searchKeyword) {
          params.faultName = this.searchKeyword
        }

        // 添加电站筛选
        if (this.selectedStationValue) {
          params.stationId = this.selectedStationValue
        }

        // 添加本人上报筛选
        if (this.showMyReportsOnly) {
          params.discoverUserId = userId
        }

        const response = await listFault(params)
        
        if (response.code === 200) {
          const newData = response.rows || []
          
          if (isLoadMore) {
            this.faultList = [...this.faultList, ...newData]
          } else {
            this.faultList = newData
          }
          
          // 判断是否还有更多数据
          this.hasMore = newData.length === this.pageSize
          
          if (this.hasMore) {
            this.pageNum++
          }
        }
      } catch (error) {
        console.error('获取故障列表失败:', error)
        uni.showToast({
          title: '获取数据失败',
          icon: 'none'
        })
      } finally {
        this.loading = false
      }
    },

    // 搜索处理
    handleSearch() {
      // 防抖处理
      clearTimeout(this.searchTimer)
      this.searchTimer = setTimeout(() => {
        this.getFaultList()
      }, 500)
    },

    // 清除搜索
    clearSearch() {
      this.searchKeyword = ''
      this.getFaultList()
    },

    // 电站选择变化
    handleStationChange() {
      this.getFaultList()
    },

    // 切换本人上报筛选
    toggleMyReports() {
      this.showMyReportsOnly = !this.showMyReportsOnly
      this.getFaultList()
    },

    // 刷新数据
    refreshData() {
      this.initData()
    },

    // 加载更多
    loadMore() {
      if (this.hasMore && !this.loading) {
        this.getFaultList(true)
      }
    },

    // 跳转到详情页
    goToDetail(item) {
      if (item.faultId) {
        uni.navigateTo({
          url: `/pages/fault/fault-detail?id=${item.faultId}`
        });
      } else {
        uni.showToast({
          title: '故障ID不存在',
          icon: 'none'
        });
      }
    },

    // 返回上一页
    goBack() {
      uni.navigateBack()
    },

    // 获取状态样式类
    getStatusClass(status) {
      const statusMap = {
        0: 'status-pending',     // 待确认
        2: 'status-rejected',    // 已驳回
        4: 'status-confirmed',   // 已确认
        6: 'status-completed'    // 已完成
      }
      return statusMap[status] || 'status-pending'
    },

    // 获取状态图标
    getStatusIcon(status) {
      const iconMap = {
        0: 'notification-filled',// 待确认
        2: 'close',              // 已驳回
        4: 'checkmarkempty',     // 已确认
        6: 'checkbox-filled'     // 已完成
      }
      return iconMap[status] || 'clock'
    },

    // 获取状态标签
    getStatusLabel(status) {
      const labelMap = {
        0: '待确认',
        2: '已驳回',
        4: '已确认',
        6: '已完成'
      }
      return labelMap[status] || '未知状态'
    },

    // 获取设备名称
    getEquipmentNames(equipmentListId) {
      if (!equipmentListId) return '未指定设备'

      const equipmentIds = equipmentListId.split(',').filter(id => id.trim())
      const names = equipmentIds.map(id => this.equipmentMap[id] || `设备${id}`).join('、')

      return names || '未知设备'
    },

    // 获取故障等级标签
    getFaultLevelLabel(faultLevel) {
      const level = this.faultLevelOptions.find(item => item.value === faultLevel)
      return level ? level.text : '未知等级'
    },

    // 格式化日期
    formatDate(dateStr) {
      if (!dateStr) return '未知时间'

      try {
        const date = new Date(dateStr)
        const year = date.getFullYear()
        const month = String(date.getMonth() + 1).padStart(2, '0')
        const day = String(date.getDate()).padStart(2, '0')

        return `${year}-${month}-${day}`
      } catch (error) {
        return '时间格式错误'
      }
    }
  }
}
</script>

<style lang="scss" scoped>
page {
  background-color: #f8f9fa;
}

.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8f9fa;

  /* 顶部导航栏 */
  .header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: linear-gradient(135deg, #1a56b3 0%, #3a7bd5 100%);
    padding: 10px 15px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  }

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .back-icon, .refresh-icon {
    padding: 5px;
    border-radius: 50%;
    transition: background-color 0.2s;

    &:active {
      background-color: rgba(255, 255, 255, 0.1);
    }
  }

  .header-title {
    color: #ffffff;
    font-size: 18px;
    font-weight: bold;
    letter-spacing: 0.5px;
  }

  .header-right {
    width: 28px;
    display: flex;
    justify-content: flex-end;
  }

  /* 内容区域 */
  .content {
    flex: 1;
    padding: 0 12px;
  }



  /* 搜索栏 */
  .search-container {
    display: flex;
    gap: 12px;
    margin: 12px 0;
    align-items: center;

    .search-box {
      flex: 1;
      display: flex;
      align-items: center;
      background-color: #ffffff;
      border-radius: 10px;
      padding: 0 14px;
      height: 34px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

      .search-input {
        flex: 1;
        margin: 0 12px;
        font-size: 13px;
        color: #333333;
        border: none;
        outline: none;
        background: transparent;

        &::placeholder {
          color: #999999;
        }
      }
    }

    .station-select {
      width: 120px;
      background-color: #ffffff;
      border-radius: 22px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    }
  }

  /* 筛选容器 */
  .filter-container {
    display: flex;
    justify-content: flex-end;
    margin: 0 0 6px 0;

    .filter-option {
      .checkbox-label {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 0 12px 6px;

        .custom-checkbox {
          width: 16px;
          height: 16px;
          border: 2px solid #d1d5db;
          border-radius: 3px;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all 0.2s ease;

          &.checked {
            background-color: #1a56b3;
            border-color: #1a56b3;
          }
        }

        .checkbox-text {
          font-size: 13px;
          color: #666666;
          font-weight: 500;
          user-select: none;
        }
      }
    }
  }

  /* 历史列表 */
  .history-list {
    flex: 1;
    margin-bottom: 12px;

    .fault-scroll {
      height: 100%;
    }
  }

  /* 故障卡片 */
  .fault-card {
    background-color: #ffffff;
    margin-bottom: 6px;
    border-radius: 8px;
    position: relative;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.06), 0 1px 2px rgba(0, 0, 0, 0.04);
    border: 1px solid rgba(26, 86, 179, 0.08);
    overflow: hidden;

    &:last-child {
      margin-bottom: 0;
    }

    .card-status-indicator {
      position: absolute;
      left: 0;
      top: 0;
      bottom: 0;
      width: 3px;
      border-top-left-radius: 8px;
      border-bottom-left-radius: 8px;

      &.status-pending {
        background: linear-gradient(180deg, #e6a23c 0%, #f4b942 100%);
      }

      &.status-rejected {
        background: linear-gradient(180deg, #f56c6c 0%, #ff7875 100%);
      }

      &.status-confirmed {
        background: linear-gradient(180deg, #409eff 0%, #5dade2 100%);
      }

      &.status-completed {
        background: linear-gradient(180deg, #67c23a 0%, #7ed321 100%);
      }
    }

    .card-content {
      padding: 14px 16px 14px 18px;
      position: relative;
    }

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;
      min-height: 24px;

      .fault-name {
        flex: 1;
        font-size: 15px;
        font-weight: 600;
        color: #1a56b3;
        line-height: 1.3;
        margin-right: 10px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        letter-spacing: 0.2px;
      }

      .status-tag {
        display: flex;
        align-items: center;
        padding: 3px 8px;
        border-radius: 10px;
        font-size: 11px;
        font-weight: 600;
        color: #ffffff;
        white-space: nowrap;
        flex-shrink: 0;
        text-transform: uppercase;
        letter-spacing: 0.3px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12);

        .status-icon {
          margin-right: 3px;
        }

        &.status-pending {
          background: linear-gradient(135deg, #e6a23c 0%, #f4b942 100%);
        }

        &.status-rejected {
          background: linear-gradient(135deg, #f56c6c 0%, #ff7875 100%);
        }

        &.status-confirmed {
          background: linear-gradient(135deg, #409eff 0%, #5dade2 100%);
        }

        &.status-completed {
          background: linear-gradient(135deg, #67c23a 0%, #7ed321 100%);
        }
      }
    }

    .card-body {
      .info-row {
        display: flex;
        align-items: center;
        margin-bottom: 6px;
        font-size: 13px;
        min-height: 20px;

        &:last-child {
          margin-bottom: 0;
        }

        .info-icon-container {
          width: 16px;
          height: 16px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 6px;
          flex-shrink: 0;
          opacity: 0.8;
        }

        .info-label {
          color: #5a6c7d;
          margin-right: 6px;
          flex-shrink: 0;
          font-weight: 500;
          min-width: 60px;
          font-size: 12px;
          letter-spacing: 0.1px;
        }

        .info-value {
          color: #2c3e50;
          flex: 1;
          line-height: 1.3;
          word-break: break-all;
          font-weight: 400;

          &.description {
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 1;
            -webkit-box-orient: vertical;
            color: #5a6c7d;
          }
        }
      }
    }
  }

  /* 空状态 */
  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    margin-top: 20px;

    .empty-text {
      font-size: 16px;
      color: #666666;
      margin: 16px 0 8px;
      font-weight: 500;
    }

    .empty-tip {
      font-size: 14px;
      color: #999999;
      text-align: center;
      line-height: 1.4;
    }
  }

  /* 加载更多 */
  .load-more {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
    margin-top: 12px;
    transition: opacity 0.2s;

    &:active {
      opacity: 0.7;
    }

    .load-more-text {
      color: #1a56b3;
      font-size: 14px;
      font-weight: 500;
    }
  }

  .no-more {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
    margin-top: 12px;

    .no-more-text {
      color: #999999;
      font-size: 14px;
    }
  }

  /* 加载组件样式 */
  .loading-container {
    margin: 20px 0;
    border-radius: 12px;
    overflow: hidden;
  }
}

/* 全局样式优化 */
::v-deep .uni-data-select {
  .uni-stat__select {
    border: none !important;
    border-radius: 22px !important;
    background-color: #ffffff !important;
    height: 44px !important;
    line-height: 44px !important;
    padding: 0 16px !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04) !important;
    display: flex !important;
    align-items: center !important;

    &:focus {
      border-color: #1a56b3 !important;
      box-shadow: 0 0 0 3px rgba(26, 86, 179, 0.1) !important;
    }
  }

  .uni-stat__select-text {
    color: #333333 !important;
    font-size: 14px !important;
    line-height: 1 !important;
  }

  .uni-stat__select-placeholder {
    color: #999999 !important;
    font-size: 14px !important;
    line-height: 1 !important;
  }
}
</style>
