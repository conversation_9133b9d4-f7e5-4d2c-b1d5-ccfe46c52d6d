<template>
  <view class="container">
    <!-- 顶部导航栏 -->
    <view class="header" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="header-content">
        <view class="back-icon" @click="goBack">
          <uni-icons type="left" size="18" color="#FFFFFF"></uni-icons>
        </view>
        <view class="header-title">故障检修详情</view>
        <view class="header-right">
          <view class="refresh-icon" @click="refreshData">
            <uni-icons type="refresh" size="18" color="#FFFFFF"></uni-icons>
          </view>
        </view>
      </view>
    </view>

    <!-- 内容区域 -->
    <scroll-view scroll-y class="content" :style="{ paddingTop: (statusBarHeight + 44) + 'px' }">
      <view v-if="repairInfo" class="inspection-detail">
        <!-- 维修状态标识 -->
        <view class="status-banner" :class="getStatusClass(repairInfo.status)">
          <view class="status-content">
            <view class="status-left">
              <view class="status-icon">
                <uni-icons :type="getStatusIcon(repairInfo.status)" size="18" color="#FFFFFF"></uni-icons>
              </view>
              <text class="status-text">{{ getStatusLabel(repairInfo.status) }}</text>
            </view>
            <!-- 操作按钮 -->
            <view class="action-buttons-top" v-if="showActions && (repairInfo.status === '0' || repairInfo.status === '2')">
              <button class="action-button-top" type="primary" @click="receiveTask" v-if="repairInfo.status === '0'">
                接收任务
              </button>
              <button class="action-button-top" type="success" @click="submitTask" v-if="repairInfo.status === '2'">
                执行维修
              </button>
            </view>
            <!-- 非负责人提示 -->
            <view class="action-tip" v-if="!showActions && (repairInfo.status === '0' || repairInfo.status === '2')">
              <text class="action-tip-text">仅负责人可操作</text>
            </view>
          </view>
        </view>

        <!-- 基本信息卡片 -->
        <view class="detail-card">
          <view class="card-header">
            <uni-icons type="info-filled" size="16" color="#2979ff"></uni-icons>
            <text class="card-title">基本信息</text>
          </view>
          <view class="card-content">
            <view class="info-item">
              <text class="info-label">维修任务</text>
              <text class="info-value">{{ repairInfo.repairName || '未设置' }}</text>
            </view>
            <view class="info-item">
              <text class="info-label">负责人员</text>
              <text class="info-value">{{ repairInfo.assignChargeUserName || '未指定' }}</text>
            </view>
            <view class="info-item">
              <text class="info-label">分配人员</text>
              <text class="info-value">{{ repairInfo.assignUserName || '未指派' }}</text>
            </view>
            <view class="info-item">
              <text class="info-label">计划时间</text>
              <text class="info-value">{{ formatDate(repairInfo.planStartTime) }} - {{ formatDate(repairInfo.planEndTime) }}</text>
            </view>
            <view class="info-item">
              <text class="info-label">下发时间</text>
              <text class="info-value">{{ formatDateTime(repairInfo.createTime) }}</text>
            </view>
            <view class="info-item" v-if="repairInfo.receiveTime">
              <text class="info-label">接收时间</text>
              <text class="info-value">{{ formatDateTime(repairInfo.receiveTime) }}</text>
            </view>
            <view class="info-item" v-if="repairInfo.repairUserName">
              <text class="info-label">实际人员</text>
              <text class="info-value">{{ repairInfo.repairUserName }}</text>
            </view>
            <view class="info-item" v-if="repairInfo.submitTime">
              <text class="info-label">提交时间</text>
              <text class="info-value">{{ formatDateTime(repairInfo.submitTime) }}</text>
            </view>
            <view class="info-item" v-if="repairInfo.reviewTime">
              <text class="info-label">审核时间</text>
              <text class="info-value">{{ formatDateTime(repairInfo.reviewTime) }}</text>
            </view>
            <view class="info-item" v-if="repairInfo.reviewComment">
              <text class="info-label">审核意见</text>
              <text class="info-value">{{ repairInfo.reviewComment }}</text>
            </view>
            <view class="info-item" v-if="repairInfo.remark">
              <text class="info-label">备注</text>
              <text class="info-value">{{ repairInfo.remark }}</text>
            </view>
          </view>
        </view>

        <!-- 故障信息卡片 -->
        <view class="detail-card" v-if="repairInfo.fault">
          <view class="card-header" @click="goToFaultDetail">
            <uni-icons type="notification-filled" size="16" color="#2979ff"></uni-icons>
            <text class="card-title">关联故障信息</text>
            <view class="card-action">
              <text class="action-text">查看详情</text>
              <uni-icons type="right" size="14" color="#999"></uni-icons>
            </view>
          </view>
          <view class="card-content">
            <view class="info-item">
              <text class="info-label">故障名称</text>
              <text class="info-value">{{ repairInfo.fault.faultName || '未知故障' }}</text>
            </view>
            <view class="info-item">
              <text class="info-label">故障设备</text>
              <text class="info-value">{{ getEquipmentNames(repairInfo.fault.equipmentListId) }}</text>
            </view>
            <view class="info-item">
              <text class="info-label">故障类型</text>
              <text class="info-value">{{ getFaultTypeLabel(repairInfo.fault.faultType) }}</text>
            </view>
            <view class="info-item">
              <text class="info-label">故障等级</text>
              <text class="info-value">{{ getFaultLevelLabel(repairInfo.fault.faultLevel) }}</text>
            </view>
            <view class="info-item">
              <text class="info-label">故障状态</text>
              <text class="info-value">{{ getFaultStatusLabel(repairInfo.fault.status) }}</text>
            </view>
            <view class="info-item">
              <text class="info-label">发现时间</text>
              <text class="info-value">{{ formatDate(repairInfo.fault.discoverTime) }}</text>
            </view>
            <view class="info-item" v-if="repairInfo.fault.faultDescription">
              <text class="info-label">故障描述</text>
              <text class="info-value fault-desc">{{ repairInfo.fault.faultDescription }}</text>
            </view>
          </view>
        </view>

        <!-- 维修详情卡片 -->
        <view class="detail-card">
          <view class="card-header">
            <uni-icons type="list" size="16" color="#2979ff"></uni-icons>
            <text class="card-title">维修详情</text>
          </view>
          <view class="card-content">
            <!-- 维修计划 -->
            <view class="info-item" v-if="repairInfo.repairPlan">
              <text class="info-label">维修计划</text>
              <text class="info-value">{{ repairInfo.repairPlan }}</text>
            </view>

            <!-- 更换器件记录 -->
            <view class="info-item" v-if="repairInfo.replacedParts">
              <text class="info-label">更换器件</text>
              <text class="info-value">{{ repairInfo.replacedParts }}</text>
            </view>

            <!-- 维修前照片 -->
            <view class="info-item" v-if="beforeImages.length > 0">
              <text class="info-label">维修前照片</text>
              <view class="info-value">
                <view class="image-grid">
                  <view
                    class="image-item"
                    v-for="(image, index) in beforeImages"
                    :key="index"
                    @click="previewImage(image, beforeImages)"
                  >
                    <image :src="image" mode="aspectFill" class="repair-image"></image>
                  </view>
                </view>
              </view>
            </view>

            <!-- 维修过程记录 -->
            <view class="info-item" v-if="repairInfo.repairProcess">
              <text class="info-label">维修过程</text>
              <text class="info-value">{{ repairInfo.repairProcess }}</text>
            </view>

            <!-- 维修后照片 -->
            <view class="info-item" v-if="afterImages.length > 0">
              <text class="info-label">维修后照片</text>
              <view class="info-value">
                <view class="image-grid">
                  <view
                    class="image-item"
                    v-for="(image, index) in afterImages"
                    :key="index"
                    @click="previewImage(image, afterImages)"
                  >
                    <image :src="image" mode="aspectFill" class="repair-image"></image>
                  </view>
                </view>
              </view>
            </view>

            <!-- 无维修详情提示 -->
            <view v-if="!repairInfo.repairPlan && !repairInfo.replacedParts && !repairInfo.repairProcess && beforeImages.length === 0 && afterImages.length === 0" class="empty-content">
              <uni-icons type="info" size="36" color="#ddd"></uni-icons>
              <text>暂无维修详情</text>
            </view>
          </view>
        </view>

        <!-- 维修备注卡片 -->
        <view class="detail-card" v-if="repairInfo.repairRemark">
          <view class="card-header">
            <uni-icons type="chat" size="16" color="#2979ff"></uni-icons>
            <text class="card-title">维修备注</text>
          </view>
          <view class="card-content">
            <view class="info-item">
              <text class="info-label">备注内容</text>
              <text class="info-value">{{ repairInfo.repairRemark }}</text>
            </view>
          </view>
        </view>



        <!-- 审核信息卡片 -->
        <view class="detail-card" v-if="repairInfo.status === '6' && repairInfo.reviewComment">
          <view class="card-header">
            <uni-icons type="medal" size="16" color="#2979ff"></uni-icons>
            <text class="card-title">审核信息</text>
          </view>
          <view class="card-content">
            <view class="info-item">
              <text class="info-label">审核人员</text>
              <text class="info-value">{{ repairInfo.reviewUserName || '未知' }}</text>
            </view>
            <view class="info-item">
              <text class="info-label">审核时间</text>
              <text class="info-value">{{ formatDateTime(repairInfo.reviewTime) }}</text>
            </view>
            <view class="info-item">
              <text class="info-label">审核意见</text>
              <text class="info-value">{{ repairInfo.reviewComment }}</text>
            </view>
          </view>
        </view>

      </view>

      <loading-container
        v-else
        :loading="loading"
        :error="!loading && !repairInfo"
        loadingText="正在加载维修详情..."
        errorText="获取维修详情失败"
        @retry="refreshData"
      />
    </scroll-view>
  </view>
</template>

<script>
import { getFaultRepair, receiveFaultRepair, submitFaultRepair } from '@/api/system/faultrepair'
import { getDictData } from '@/api/system/dictData'
import { listEquipmentByStationId } from '@/api/system/equipment'
import LoadingContainer from '@/components/LoadingContainer'
import { getImageUrl } from '@/utils/imageUtils'

export default {
  components: {
    LoadingContainer
  },
  data() {
    return {
      statusBarHeight: 0,
      repairId: null,
      repairInfo: null,
      equipmentList: [],
      loading: true,
      userId: null,
      dictData: {}
    }
  },
  computed: {
    // 维修前图片列表
    beforeImages() {
      if (!this.repairInfo || !this.repairInfo.beforeImages) return []
      return this.repairInfo.beforeImages.split(',').map(img => getImageUrl(img))
    },

    // 维修后图片列表
    afterImages() {
      if (!this.repairInfo || !this.repairInfo.afterImages) return []
      return this.repairInfo.afterImages.split(',').map(img => getImageUrl(img))
    },

    // 是否显示操作按钮
    showActions() {
      if (!this.repairInfo || !this.userId) return false

      // 检查当前用户是否是维修负责人
      if (this.repairInfo.assignChargeUser) {
        return this.repairInfo.assignChargeUser === this.userId.toString();
      }

      return false;
    }
  },
  onLoad(options) {
    // 设置状态栏高度
    this.statusBarHeight = uni.getSystemInfoSync().statusBarHeight

    if (options.id) {
      this.repairId = options.id

      // 获取当前登录用户ID
      this.userId = this.$store.state.user.userId

      this.getDicts()
    } else {
      this.loading = false
    }
  },
  methods: {
    // 获取字典数据
    async getDicts() {
      try {
        const response = await getDictData()
        this.dictData = this.$dict.parseDictData(response.data)
        this.getRepairDetail()
      } catch (error) {
        console.error('获取字典数据失败:', error)
        uni.showToast({
          title: '获取字典数据失败',
          icon: 'none'
        })
        this.loading = false
      }
    },

    // 获取维修详情
    async getRepairDetail() {
      try {
        const response = await getFaultRepair(this.repairId)
        this.repairInfo = response.data

        // 如果有关联故障且故障有设备信息，获取设备详情
        if (this.repairInfo.fault && this.repairInfo.fault.equipmentListId && this.repairInfo.fault.stationId) {
          await this.getEquipmentList()
        }

        this.loading = false
      } catch (error) {
        console.error('获取维修详情失败:', error)
        uni.showToast({
          title: '获取维修详情失败',
          icon: 'none'
        })
        this.loading = false
      }
    },

    // 获取相关设备列表
    async getEquipmentList() {
      try {
        if (this.repairInfo.fault.equipmentListId && this.repairInfo.fault.stationId) {
          // 获取电站下的所有设备
          const response = await listEquipmentByStationId(this.repairInfo.fault.stationId)
          const allEquipments = response.data || []

          // 根据equipmentListId筛选相关设备
          const equipmentIds = this.repairInfo.fault.equipmentListId.split(',').map(id => parseInt(id.trim()))
          this.equipmentList = allEquipments.filter(equipment =>
            equipmentIds.includes(equipment.equipmentId)
          )
        }
      } catch (error) {
        console.error('获取相关设备失败:', error)
        uni.showToast({
          title: '获取相关设备失败',
          icon: 'none'
        })
      }
    },

    // 接收任务
    receiveTask() {
      uni.navigateTo({
        url: `/pages/fault/repair-receive?id=${this.repairId}`
      });
    },

    // 提交任务
    submitTask() {
      uni.navigateTo({
        url: `/pages/fault/repair-execute?id=${this.repairId}`
      })
    },

    // 预览图片
    previewImage(current, urls) {
      uni.previewImage({
        current: current,
        urls: urls
      })
    },

    // 返回上一页
    goBack() {
      uni.navigateBack()
    },

    // 刷新数据
    refreshData() {
      this.loading = true;
      this.getDicts();
      uni.showToast({
        title: '刷新中',
        icon: 'loading',
        duration: 500
      });
    },

    // 获取状态样式类
    getStatusClass(status) {
      const statusMap = {
        '0': 'status-pending',    // 已下发
        '2': 'status-received',   // 已接收
        '4': 'status-submitted',  // 已提交
        '6': 'status-reviewed'    // 已审核
      }
      return statusMap[status] || 'status-pending'
    },

    // 获取状态图标
    getStatusIcon(status) {
      const iconMap = {
        '0': 'notification-filled',  // 已下发
        '2': 'staff-filled',         // 已接收
        '4': 'checkbox-filled',      // 已提交
        '6': 'medal-filled'          // 已审核
      }
      return iconMap[status] || 'info-filled'
    },

    // 获取状态标签
    getStatusLabel(status) {
      if (!status) return '未知'
      return this.$dict.getDictLabel('fault_repair_status', status, '未知')
    },

    // 获取状态描述
    getStatusDescription(status) {
      const statusDescMap = {
        '0': '维修任务已下发，等待接收',
        '2': '维修任务进行中，请及时完成',
        '4': '维修任务已提交，等待审核',
        '6': '维修任务已审核完成'
      }
      return statusDescMap[status] || ''
    },

    // 获取故障类型标签
    getFaultTypeLabel(type) {
      if (!type) return '未知类型'
      return this.$dict.getDictLabel('fault_type', type, '未知类型')
    },

    // 获取故障等级标签
    getFaultLevelLabel(level) {
      if (!level) return '未知等级'
      return this.$dict.getDictLabel('fault_level', level, '未知等级')
    },

    // 获取故障状态标签
    getFaultStatusLabel(status) {
      if (!status) return '未知状态'
      return this.$dict.getDictLabel('fault_status', status, '未知状态')
    },



    // 获取设备名称列表
    getEquipmentNames(equipmentListId) {
      if (!equipmentListId) return '未知设备'

      // equipmentListId 是逗号分隔的设备ID字符串
      const equipmentIds = equipmentListId.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id))
      if (equipmentIds.length === 0) return '未知设备'

      // 从已获取的设备列表中查找对应的设备名称
      if (this.equipmentList && this.equipmentList.length > 0) {
        const equipmentNames = this.equipmentList
          .filter(equipment => equipmentIds.includes(equipment.equipmentId))
          .map(equipment => equipment.equipmentName)

        if (equipmentNames.length > 0) {
          return equipmentNames.join(', ')
        }
      }

      // 如果没有找到设备信息，返回未知设备
      return '未知设备'
    },

    // 跳转到故障详情页面
    goToFaultDetail() {
      if (this.repairInfo && this.repairInfo.fault && this.repairInfo.fault.faultId) {
        uni.navigateTo({
          url: `/pages/fault/fault-detail?id=${this.repairInfo.fault.faultId}`
        })
      } else {
        uni.showToast({
          title: '故障信息不完整',
          icon: 'none'
        })
      }
    },

    // 格式化日期
    formatDate(dateString) {
      if (!dateString) return '未设置'
      const date = new Date(dateString)
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
    },

    // 格式化日期时间
    formatDateTime(dateStr) {
      if (!dateStr) return '未设置'
      const date = new Date(dateStr)
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hours = String(date.getHours()).padStart(2, '0')
      const minutes = String(date.getMinutes()).padStart(2, '0')
      return `${year}-${month}-${day} ${hours}:${minutes}`
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #ffffff;
}

/* 顶部导航栏 */
.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: linear-gradient(135deg, #1a56b3 0%, #3a7bd5 100%);
  padding: 10px 15px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.back-icon {
  padding: 5px;
}

.header-title {
  color: #ffffff;
  font-size: 18px;
  font-weight: bold;
}

/* 内容区域 */
.content {
  flex: 1;
}

.inspection-detail {
  padding: 0;
}

/* 分区样式 */
.detail-card {
  background-color: #ffffff;
  margin-bottom: 10px;
  position: relative;
}

.card-header {
  display: flex;
  align-items: center;
  padding: 15px 16px 10px;
  position: relative;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.card-header:hover {
  background-color: #f8f9fc;
}

.card-title {
  font-size: 16px;
  font-weight: bold;
  margin-left: 8px;
  color: #1a56b3;
}

.card-content {
  padding: 5px 16px 15px;
}

/* 卡片操作区域 */
.card-action {
  display: flex;
  align-items: center;
  margin-left: auto;
  gap: 4px;
}

.action-text {
  font-size: 14px;
  color: #999;
}

/* 状态横幅 */
.status-banner {
  padding: 15px 16px;
  color: #ffffff;
  position: relative;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.status-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-left {
  display: flex;
  align-items: center;
}

.status-icon {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
}

.status-text {
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0.5px;
}

/* 上方操作按钮 */
.action-buttons-top {
  display: flex;
  gap: 10px;
}

.action-button-top {
  background-color: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.4);
  color: #ffffff;
  font-size: 13px;
  padding: 4px 12px;
  border-radius: 15px;
  height: auto;
  line-height: 1.5;
  font-weight: normal;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.action-button-top:active {
  transform: scale(0.95);
  background-color: rgba(255, 255, 255, 0.3);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* 非负责人提示 */
.action-tip {
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 15px;
  padding: 4px 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.action-tip-text {
  color: #ffffff;
  font-size: 13px;
  opacity: 0.9;
}

/* 信息项样式 */
.info-item {
  display: flex;
  margin-bottom: 10px;
  align-items: center;
  position: relative;
  border-bottom: 1px solid #f9f9f9;
  padding-bottom: 10px;
}

.info-item:last-child {
  margin-bottom: 0;
  border-bottom: none;
  padding-bottom: 0;
}

.info-label {
  width: 90px;
  color: #666;
  font-size: 14px;
  flex-shrink: 0;
  position: relative;
  padding-left: 10px;
}

.info-label::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 14px;
  background-color: #1a56b3;
  opacity: 0.6;
  border-radius: 1.5px;
}

.info-value {
  flex: 1;
  color: #333;
  font-size: 14px;
  word-break: break-all;
  font-weight: 500;
}

/* 图片网格 */
.image-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
  margin-top: 8px;
}

.image-item {
  aspect-ratio: 1;
  border-radius: 6px;
  overflow: hidden;
  cursor: pointer;
}

.repair-image {
  width: 100%;
  height: 100%;
}

/* 空内容提示 */
.empty-content {
  text-align: center;
  padding: 20px 0;
  color: #999;
  font-size: 14px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 10px;
}

/* 状态样式 */
.status-pending {
  background: linear-gradient(135deg, #e6a23c 0%, #f0b95a 100%);
}

.status-received {
  background: linear-gradient(135deg, #1a56b3 0%, #3a7bd5 100%);
}

.status-submitted {
  background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%);
}

.status-reviewed {
  background: linear-gradient(135deg, #606266 0%, #909399 100%);
}

.status-unknown {
  background: linear-gradient(135deg, #f56c6c 0%, #f78989 100%);
}

.status-reviewed {
  background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%);
}

/* 故障描述样式 */
.fault-desc {
  line-height: 1.5;
  max-height: 60px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}
</style>
