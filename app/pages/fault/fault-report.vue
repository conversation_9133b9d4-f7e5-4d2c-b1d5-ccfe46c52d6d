<template>
  <view class="fault-report-container">
    <!-- 顶部导航栏 -->
    <view class="header" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="header-content">
        <view class="back-icon" @click="goBack">
          <uni-icons type="left" size="18" color="#FFFFFF"></uni-icons>
        </view>
        <view class="header-title">故障上报</view>
        <view style="width: 20px;"></view>
      </view>
    </view>

    <!-- 内容区域 -->
    <scroll-view scroll-y class="content" :style="{ paddingTop: (statusBarHeight + 44) + 'px' }">
      <view class="fault-report">
        <!-- 任务状态标识 -->
        <view class="status-banner status-reporting">
          <view class="status-content">
            <view class="status-left">
              <view class="status-icon">
                <uni-icons type="notification-filled" size="18" color="#FFFFFF"></uni-icons>
              </view>
              <text class="status-text">填写故障信息</text>
            </view>
            <!-- 操作按钮 -->
            <view class="action-buttons-top">
              <button class="action-button-top cancel" @click="goBack">
                取消
              </button>
              <button class="action-button-top submit" @click="handleSubmit" :disabled="!isFormValid">
                提交
              </button>
            </view>
          </view>
        </view>

        <!-- 故障信息区域 -->
        <view class="detail-card">
          <view class="card-header">
            <uni-icons type="notification-filled" size="16" color="#2979ff"></uni-icons>
            <text class="card-title">故障信息</text>
          </view>
          <view class="card-content">
            <!-- 电站选择 -->
            <view class="info-item">
              <text class="info-label">所属电站</text>
              <view class="info-value">
                <uni-data-select
                    v-model="faultForm.stationId"
                    :localdata="stationOptions"
                    placeholder="请选择电站"
                    @change="handleStationChange"
                    class="form-select"
                />
              </view>
            </view>

            <!-- 设备选择 -->
            <view class="info-item" v-if="faultForm.stationId">
              <text class="info-label">故障设备</text>
              <view class="info-value">
                <view class="equipment-selector-trigger" @click="openEquipmentSelector">
                  <view class="selector-display">
                    <text class="selector-text" v-if="selectedEquipmentNames.length === 0">请选择故障设备</text>
                    <text class="selector-text selected" v-else>{{ selectedEquipmentNames.join('、') }}</text>
                  </view>
                  <uni-icons type="right" size="16" color="#999"></uni-icons>
                </view>
              </view>
            </view>

            <!-- 故障名称 -->
            <view class="info-item">
              <text class="info-label">故障名称</text>
              <view class="info-value">
                <uni-easyinput
                    v-model="faultForm.faultName"
                    placeholder="请输入故障名称"
                    :maxlength="100"
                    :clearable="true"
                    :inputBorder="true"
                />
              </view>
            </view>

            <!-- 故障类型和等级 -->
            <view class="info-item">
              <text class="info-label">故障类型</text>
              <view class="info-value">
                <uni-data-select
                    v-model="faultForm.faultType"
                    :localdata="faultTypeOptions"
                    placeholder="请选择类型"
                    class="form-select"
                />
              </view>
            </view>

            <view class="info-item">
              <text class="info-label">故障等级</text>
              <view class="info-value">
                <uni-data-select
                    v-model="faultForm.faultLevel"
                    :localdata="faultLevelOptions"
                    placeholder="请选择等级"
                    class="form-select"
                />
              </view>
            </view>

            <!-- 发生时间 -->
            <view class="info-item">
              <text class="info-label">发生时间</text>
              <view class="info-value">
                <uni-datetime-picker
                    v-model="faultForm.discoverTime"
                    type="date"
                    :clear-icon="false"
                    placeholder="请选择发生时间"
                    :border="true"
                    :inputBorder="true"
                />
              </view>
            </view>

            <!-- 故障来源 -->
            <view class="info-item">
              <text class="info-label">故障来源</text>
              <view class="info-value">
                <uni-data-select
                    v-model="faultForm.faultSource"
                    :localdata="faultSourceOptions"
                    placeholder="请选择故障来源"
                    class="form-select"
                />
              </view>
            </view>

            <!-- 故障描述 -->
            <view class="info-item">
              <text class="info-label">故障描述</text>
              <view class="info-value">
                <uni-easyinput
                    v-model="faultForm.faultDescription"
                    type="textarea"
                    placeholder="请详细描述故障现象、影响范围等信息"
                    :maxlength="500"
                    :clearable="true"
                    :inputBorder="true"
                    :autoHeight="true"
                />
                <view class="char-count">{{ faultForm.faultDescription.length }}/500</view>
              </view>
            </view>

            <!-- 故障照片 -->
            <view class="info-item">
              <text class="info-label">故障照片</text>
              <view class="info-value">
                <view class="upload-container">
                  <uni-file-picker
                      v-model="faultImageList"
                      file-mediatype="image"
                      mode="grid"
                      :limit="6"
                      :image-styles="imageStyles"
                      :auto-upload="false"
                      @select="selectFaultImages"
                      @delete="deleteFaultImage"
                      :source-type="['camera', 'album']"
                  />
                  <view class="upload-tip">
                    <uni-icons type="info" size="12" color="#999"></uni-icons>
                    <text>最多可上传6张照片，建议拍摄故障现场全貌和细节</text>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 使用LoadingContainer组件 -->
        <loading-container
            v-if="loading"
            :loading="loading"
            loadingText="正在提交故障上报..."
            height="200"
        />
      </view>
    </scroll-view>

    <!-- 设备选择器 -->
    <equipment-selector
        ref="equipmentSelector"
        :equipment-options="equipmentOptions"
        :value="faultForm.equipmentIds"
        @input="handleEquipmentSelectorInput"
        @change="handleEquipmentSelectorChange"
    />
  </view>
</template>

<script>
import {reportFault} from '@/api/system/fault'
import {listStation} from '@/api/system/station'
import {listEquipmentByStationId} from '@/api/system/equipment'
import {getDictData} from '@/api/system/dictData'
import {uploadFile} from '@/api/system/common'
import LoadingContainer from '@/components/LoadingContainer'
import EquipmentSelector from '@/components/EquipmentSelector'
import {getRelativePath} from '@/utils/imageUrlUtils'

export default {
  components: {
    LoadingContainer,
    EquipmentSelector
  },
  data() {
    return {
      statusBarHeight: 0,
      loading: false,
      // 表单数据
      faultForm: {
        stationId: null,
        equipmentIds: [], // 改为数组存储多个设备ID
        faultName: '',
        faultType: null,
        faultLevel: null,
        faultDescription: '',
        faultSource: '', // 故障来源
        discoverTime: '' // 发生时间（精确到日期）
      },
      // 图片相关
      faultImageList: [],
      imageStyles: {
        width: 64,
        height: 64,
        border: {
          color: '#eee',
          width: 1,
          style: 'solid',
          radius: '5px'
        }
      },
      // 选项数据
      stationOptions: [],
      equipmentOptions: [],
      faultTypeOptions: [],
      faultLevelOptions: [],
      faultSourceOptions: [],
      // 字典数据
      dictData: {}
    }
  },
  computed: {
    // 表单验证
    isFormValid() {
      const hasImages = this.faultImageList && this.faultImageList.length > 0
      const isValid = this.faultForm.stationId &&
          this.faultForm.faultName.trim() &&
          this.faultForm.faultType &&
          this.faultForm.faultLevel &&
          this.faultForm.faultDescription.trim() &&
          this.faultForm.faultSource &&
          this.faultForm.discoverTime &&
          hasImages

      return isValid
    },

    // 已选择设备的名称数组
    selectedEquipmentNames() {
      return this.equipmentOptions
          .filter(equipment => this.faultForm.equipmentIds.includes(equipment.value))
          .map(equipment => equipment.text)
    }
  },
  onLoad() {
    // 设置状态栏高度
    this.statusBarHeight = uni.getSystemInfoSync().statusBarHeight

    // 初始化发生时间为当前日期
    const today = new Date()
    const year = today.getFullYear()
    const month = String(today.getMonth() + 1).padStart(2, '0')
    const day = String(today.getDate()).padStart(2, '0')
    this.faultForm.discoverTime = `${year}-${month}-${day}`

    // 设置默认故障来源（1巡检发现，2清洗发现，3监控发现，4用户报告，5定期检查，6其他）
    this.faultForm.faultSource = '1'

    // 获取字典数据
    this.getDicts()

    // 获取电站列表
    this.getStationList()
  },
  methods: {
    // 获取字典数据
    async getDicts() {
      try {
        const response = await getDictData()
        if (response.code === 200 && response.data) {
          this.dictData = response.data
          this.parseDictData()
        }
      } catch (error) {
        console.error('获取字典数据失败:', error)
        uni.showToast({
          title: '获取字典数据失败',
          icon: 'none'
        })
      }
    },

    // 解析字典数据
    parseDictData() {
      try {
        // 故障类型
        if (this.dictData.fault_type && Array.isArray(this.dictData.fault_type)) {
          this.faultTypeOptions = this.dictData.fault_type.map(item => ({
            value: item.dictValue,
            text: item.dictLabel
          }))
        }

        // 故障等级
        if (this.dictData.fault_level && Array.isArray(this.dictData.fault_level)) {
          this.faultLevelOptions = this.dictData.fault_level.map(item => ({
            value: item.dictValue,
            text: item.dictLabel
          }))
        }

        // 故障来源
        if (this.dictData.fault_source && Array.isArray(this.dictData.fault_source)) {
          this.faultSourceOptions = this.dictData.fault_source.map(item => ({
            value: item.dictValue,
            text: item.dictLabel
          }))
        }
      } catch (error) {
        console.error('解析字典数据失败:', error)
        this.faultTypeOptions = []
        this.faultLevelOptions = []
        this.faultSourceOptions = []
      }
    },

    // 获取电站列表
    async getStationList() {
      try {
        const response = await listStation()
        if (response.code === 200 && response.data) {
          this.stationOptions = response.data.map(station => ({
            value: station.stationId,
            text: station.stationName
          }))
        }
      } catch (error) {
        console.error('获取电站列表失败:', error)
        uni.showToast({
          title: '获取电站列表失败',
          icon: 'none'
        })
      }
    },

    // 电站选择变化
    async handleStationChange(stationId) {
      this.faultForm.equipmentIds = []
      this.equipmentOptions = []

      if (stationId) {
        await this.getEquipmentList(stationId)
      }
    },

    // 打开设备选择器
    openEquipmentSelector() {
      if (this.equipmentOptions.length === 0) {
        uni.showToast({
          title: '暂无设备数据',
          icon: 'none'
        })
        return
      }
      this.$refs.equipmentSelector.open()
    },

    // 设备选择器input事件（v-model）
    handleEquipmentSelectorInput(selectedIds) {
      this.faultForm.equipmentIds = selectedIds || []
    },

    // 设备选择器变化
    handleEquipmentSelectorChange(selectedIds) {
      this.faultForm.equipmentIds = selectedIds || []
    },

    // 获取设备列表
    async getEquipmentList(stationId) {
      try {
        const response = await listEquipmentByStationId(stationId)
        if (response.code === 200 && response.data) {
          this.equipmentOptions = response.data.map(equipment => ({
            value: equipment.equipmentId,
            text: equipment.equipmentName
          }))
        }
      } catch (error) {
        console.error('获取设备列表失败:', error)
        this.equipmentOptions = []
      }
    },

    // 选择故障图片
    async selectFaultImages(e) {
      // 获取选择的文件
      const tempFiles = e.tempFiles || []
      if (tempFiles.length === 0) return

      try {
        uni.showLoading({
          title: '正在上传...',
          mask: true
        })

        // 上传所有选择的图片
        const uploadPromises = tempFiles.map(async (file, index) => {
          try {
            // 生成自定义文件名：GZ_{时间戳}_{索引}
            const timestamp = new Date().toISOString().replace(/[-:T]/g, '').split('.')[0]
            const customFileName = `GZ_${timestamp}_${index}`

            // 构建formData，传递自定义文件名
            const formData = {
              customFileName: customFileName
            }

            const response = await uploadFile(file.url, formData)

            if (response.code === 200) {
              return {
                url: response.url,
                name: file.name,
                size: file.size
              }
            } else {
              throw new Error(response.msg || '上传失败')
            }
          } catch (error) {
            console.error('单个文件上传失败:', error)
            throw error
          }
        })

        const uploadedFiles = await Promise.all(uploadPromises)

        // 将上传成功的文件添加到现有列表中
        uploadedFiles.forEach(file => {
          this.faultImageList.push({
            url: file.url,
            name: file.name,
            size: file.size,
            extname: 'jpg'
          })
        })

        uni.hideLoading()
        uni.showToast({
          title: '图片上传成功',
          icon: 'success'
        })

        // 强制更新视图以触发表单验证
        this.$forceUpdate()
      } catch (error) {
        uni.hideLoading()
        console.error('图片上传失败:', error)
        uni.showToast({
          title: '图片上传失败',
          icon: 'none'
        })
      }
    },

    // 删除故障图片
    deleteFaultImage(e) {
      // 获取删除的索引
      const index = e.index
      if (index >= 0 && index < this.faultImageList.length) {
        // 从数组中移除对应的图片
        this.faultImageList.splice(index, 1)

        // 强制更新视图以触发表单验证
        this.$forceUpdate()
      }
    },

    // 提交故障上报
    async handleSubmit() {
      if (!this.isFormValid) {
        // 详细检查表单验证条件
        const validationChecks = {
          stationId: !!this.faultForm.stationId,
          faultName: !!this.faultForm.faultName.trim(),
          faultType: !!this.faultForm.faultType,
          faultLevel: !!this.faultForm.faultLevel,
          faultDescription: !!this.faultForm.faultDescription.trim(),
          faultSource: !!this.faultForm.faultSource,
          discoverTime: !!this.faultForm.discoverTime,
          hasImages: this.faultImageList && this.faultImageList.length > 0
        }

        // 找出未通过验证的字段
        const missingFields = []
        if (!validationChecks.stationId) missingFields.push('电站')
        if (!validationChecks.faultName) missingFields.push('故障名称')
        if (!validationChecks.faultType) missingFields.push('故障类型')
        if (!validationChecks.faultLevel) missingFields.push('故障等级')
        if (!validationChecks.faultDescription) missingFields.push('故障描述')
        if (!validationChecks.faultSource) missingFields.push('故障来源')
        if (!validationChecks.discoverTime) missingFields.push('发生时间')
        if (!validationChecks.hasImages) missingFields.push('故障照片')

        uni.showToast({
          title: `请完善：${missingFields.join('、')}`,
          icon: 'none',
          duration: 3000
        })
        return
      }

      try {
        this.loading = true
        uni.showLoading({
          title: '提交中...'
        })

        // 获取图片相对路径
        const imageUrls = await this.uploadImages()

        // 构建提交数据
        const submitData = {
          stationId: this.faultForm.stationId,
          faultName: this.faultForm.faultName,
          faultType: this.faultForm.faultType,
          faultLevel: this.faultForm.faultLevel,
          faultDescription: this.faultForm.faultDescription,
          faultSource: this.faultForm.faultSource,
          discoverTime: this.faultForm.discoverTime,
          faultImages: imageUrls.join(','),
          discoverUserId: this.$store.state.user.userId,
          equipmentListId: this.faultForm.equipmentIds && this.faultForm.equipmentIds.length > 0 ? this.faultForm.equipmentIds.join(',') : ''
        }

        // 提交故障上报
        const response = await reportFault(submitData)

        if (response.code === 200) {
          uni.hideLoading()
          uni.showToast({
            title: '上报成功',
            icon: 'success',
            duration: 2000,
            success: () => {
              setTimeout(() => {
                uni.navigateBack()
              }, 1000)
            }
          })
        } else {
          throw new Error(response.msg || '上报失败')
        }
      } catch (error) {
        uni.hideLoading()
        console.error('故障上报失败:', error)
        uni.showToast({
          title: error.message || '上报失败',
          icon: 'none',
          duration: 3000
        })
      } finally {
        this.loading = false
      }
    },

    // 上传图片（获取已上传图片的相对路径）
    async uploadImages() {
      const imageUrls = []

      // 遍历图片列表，提取相对路径
      for (let i = 0; i < this.faultImageList.length; i++) {
        const file = this.faultImageList[i]
        if (file.url) {
          // 如果图片已经上传过，直接获取相对路径
          const relativePath = getRelativePath(file.url)
          imageUrls.push(relativePath)
        }
      }

      return imageUrls
    },

    // 返回上一页
    goBack() {
      uni.navigateBack()
    }
  }
}
</script>

<style lang="scss" scoped>
page {
  background-color: #ffffff;
}

.fault-report-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #ffffff;

  /* 顶部导航栏 */
  .header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: linear-gradient(135deg, #1a56b3 0%, #3a7bd5 100%);
    padding: 10px 15px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  }

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .back-icon {
    padding: 5px;
  }

  .header-title {
    color: #ffffff;
    font-size: 18px;
    font-weight: bold;
  }

  /* 内容区域 */
  .content {
    flex: 1;
  }

  .fault-report {
    padding: 0;
  }

  /* 状态横幅 */
  .status-banner {
    padding: 15px 16px;
    color: #ffffff;
    position: relative;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  }

  .status-reporting {
    background: linear-gradient(135deg, #e6a23c 0%, #f0b95a 100%);
  }

  .status-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .status-left {
    display: flex;
    align-items: center;
  }

  .status-icon {
    width: 28px;
    height: 28px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 10px;
  }

  .status-text {
    font-size: 16px;
    font-weight: 500;
    letter-spacing: 0.5px;
  }

  /* 上方操作按钮 */
  .action-buttons-top {
    display: flex;
    gap: 10px;
  }

  .action-button-top {
    background-color: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.4);
    color: #ffffff;
    font-size: 13px;
    padding: 4px 12px;
    border-radius: 15px;
    height: auto;
    line-height: 1.5;
    font-weight: normal;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .action-button-top:active {
    transform: scale(0.95);
    background-color: rgba(255, 255, 255, 0.3);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  }

  .action-button-top.cancel {
    background-color: rgba(255, 255, 255, 0.15);
  }

  .action-button-top.submit {
    background-color: rgba(255, 255, 255, 0.25);

    &:disabled {
      opacity: 0.5;
      background-color: rgba(255, 255, 255, 0.1);
      color: rgba(255, 255, 255, 0.6);
      cursor: not-allowed;
    }
  }
}

// 表单容器样式
.form-container {
  padding: 0 16px;
}

/* 分区样式 */
.detail-card {
  background-color: #ffffff;
  margin-bottom: 10px;
  position: relative;
}

.card-header {
  display: flex;
  align-items: center;
  padding: 15px 16px 10px;
  position: relative;
}

.card-title {
  font-size: 16px;
  font-weight: bold;
  margin-left: 8px;
  color: #1a56b3;
}

.card-content {
  padding: 5px 16px 15px;
}

/* 信息项样式 */
.info-item {
  display: flex;
  margin-bottom: 15px;
  align-items: flex-start;
  position: relative;
  border-bottom: 1px solid #f9f9f9;
  padding-bottom: 15px;
}

.info-item:last-child {
  margin-bottom: 0;
  border-bottom: none;
  padding-bottom: 0;
}

.info-label {
  width: 90px;
  color: #666;
  font-size: 14px;
  flex-shrink: 0;
  position: relative;
  padding-left: 10px;
  padding-top: 8px;
}

.info-label::before {
  content: '';
  position: absolute;
  left: 0;
  top: 64%;
  transform: translateY(-50%);
  width: 3px;
  height: 14px;
  background-color: #1a56b3;
  opacity: 0.6;
  border-radius: 1.5px;
}

.info-value {
  flex: 1;
  color: #333;
  font-size: 14px;
  word-break: break-all;
  font-weight: 500;

  .form-input {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid #e1e8f0;
    border-radius: 8px;
    font-size: 14px;
    background-color: #ffffff;
    transition: all 0.2s;
    box-sizing: border-box;

    &:focus {
      border-color: #1a56b3;
      outline: none;
      box-shadow: 0 0 0 3px rgba(26, 86, 179, 0.1);
    }

    &::placeholder {
      color: #a0a8b8;
    }
  }

  .form-textarea {
    width: 100%;
    min-height: 100px;
    padding: 12px 16px;
    border: 1px solid #e1e8f0;
    border-radius: 8px;
    font-size: 14px;
    background-color: #ffffff;
    resize: vertical;
    transition: all 0.2s;
    box-sizing: border-box;
    line-height: 1.5;

    &:focus {
      border-color: #1a56b3;
      outline: none;
      box-shadow: 0 0 0 3px rgba(26, 86, 179, 0.1);
    }

    &::placeholder {
      color: #a0a8b8;
    }
  }

  .form-select {
    width: 100%;
  }

  // 字符计数样式
  .char-count {
    text-align: right;
    font-size: 12px;
    color: #999999;
    margin-top: 6px;
    letter-spacing: 0.2px;
  }

  // 设备选择触发器样式
  .equipment-selector-trigger {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    background-color: #ffffff;
    border: 1px solid #e1e8f0;
    border-radius: 8px;
    transition: all 0.2s;

    &:active {
      background-color: #f8f9fc;
      border-color: #1a56b3;
    }

    .selector-display {
      flex: 1;

      .selector-text {
        font-size: 14px;
        color: #a0a8b8;

        &.selected {
          color: #333;
        }
      }
    }
  }

  // 图片上传区域样式
  .upload-container {
    .upload-tip {
      display: flex;
      align-items: center;
      margin-top: 12px;
      padding: 8px 12px;
      background-color: #f8f9fc;
      border-radius: 6px;

      text {
        font-size: 12px;
        color: #666666;
        margin-left: 6px;
        line-height: 1.4;
      }
    }
  }
}

// 加载组件样式
.loading-container {
  margin: 20px 16px;
  border-radius: 12px;
  overflow: hidden;
}

// 全局样式优化
::v-deep .uni-data-select {
  .uni-stat__select {
    border: 1px solid #e1e8f0 !important;
    border-radius: 8px !important;
    background-color: #ffffff !important;
    transition: all 0.2s !important;

    &:focus {
      border-color: #1a56b3 !important;
      box-shadow: 0 0 0 3px rgba(26, 86, 179, 0.1) !important;
    }
  }

  .uni-stat__select-text {
    color: #333333 !important;
    font-size: 14px !important;
  }

  .uni-stat__select-placeholder {
    color: #a0a8b8 !important;
    font-size: 14px !important;
  }
}

::v-deep .uni-file-picker__container {
  .file-picker__box {
    border-radius: 8px !important;
    background-color: #fafbfc !important;
    transition: all 0.2s !important;

    &:hover {
      border-color: #1a56b3 !important;
      background-color: rgba(26, 86, 179, 0.02) !important;
    }
  }

  .file-picker__box-content {
    color: #666666 !important;
    font-size: 14px !important;
  }
}
</style>
