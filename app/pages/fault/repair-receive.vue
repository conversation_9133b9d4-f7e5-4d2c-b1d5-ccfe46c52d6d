<template>
  <view class="container">
    <!-- 顶部导航栏 -->
    <view class="header" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="header-content">
        <view class="back-icon" @click="goBack">
          <uni-icons type="left" size="18" color="#FFFFFF"></uni-icons>
        </view>
        <view class="header-title">接收任务</view>
        <view style="width: 20px;"></view>
      </view>
    </view>

    <!-- 内容区域 -->
    <scroll-view scroll-y class="content" :style="{ paddingTop: (statusBarHeight + 44) + 'px' }">
      <view class="repair-receive">
        <!-- 任务状态标识 -->
        <view class="status-banner status-pending">
          <view class="status-content">
            <view class="status-left">
              <view class="status-icon">
                <uni-icons type="notification-filled" size="18" color="#FFFFFF"></uni-icons>
              </view>
              <text class="status-text">待接收</text>
            </view>
            <!-- 操作按钮 -->
            <view class="action-buttons-top">
              <button class="action-button-top cancel" @click="goBack">
                取消
              </button>
              <button class="action-button-top confirm" @click="handleConfirm" :disabled="!selectedUserIds.length">
                确定
              </button>
            </view>
          </view>
        </view>

        <!-- 用户选择区域 -->
        <view class="detail-card">
          <view class="card-header">
            <uni-icons type="staff" size="16" color="#1a56b3"></uni-icons>
            <text class="card-title">实际维修人员</text>
            <text class="edit-tip">点击可多选</text>
          </view>
          <view class="card-content">
            <!-- 提示信息 -->
            <view class="selection-tip">
              <uni-icons type="info" size="14" color="#909399"></uni-icons>
              <text class="tip-text">请选择实际执行维修的人员，可多选</text>
            </view>

            <view class="user-list">
              <!-- 使用自定义复选框实现 -->
              <view class="user-item" v-for="user in userList" :key="user.userId" @click="handleUserClick(user.userId)" :class="{ 'charge-user': isChargeUser(user.userId) }">
                <view class="user-info">
                  <view class="checkbox-custom" :class="{ 'checked': isUserSelected(user.userId), 'charge': isChargeUser(user.userId) }">
                    <uni-icons v-if="isUserSelected(user.userId)" type="checkbox-filled" size="18" :color="isChargeUser(user.userId) ? '#e6a23c' : '#1a56b3'"></uni-icons>
                    <uni-icons v-else type="square" size="18" color="#999"></uni-icons>
                  </view>
                  <text class="user-name" :class="{ 'charge-name': isChargeUser(user.userId) }">{{ user.nickName }}</text>
                  <text v-if="isChargeUser(user.userId)" class="charge-tag">负责人</text>
                </view>
              </view>
            </view>

            <!-- 已选择人员提示 -->
            <view class="selected-info" v-if="selectedUserIds.length > 0">
              <text class="selected-count">已选择 {{ selectedUserIds.length }} 人</text>
              <view class="selected-names">
                <text>{{ getSelectedUserNames() }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <loading-container
        v-if="loading"
        :loading="loading"
        :error="false"
        loadingText="正在加载用户列表..."
        @retry="loadUserList"
      />
    </scroll-view>
  </view>
</template>

<script>
import { getFaultRepair, receiveFaultRepair } from '@/api/system/faultrepair'
import LoadingContainer from '@/components/LoadingContainer'
import { listUser } from '@/api/system/user'

export default {
  components: {
    LoadingContainer
  },
  data() {
    return {
      statusBarHeight: 0,
      repairId: null,
      repairInfo: null,
      userList: [],
      selectedUserIds: [],
      loading: true,
      chargeUserIds: [] // 负责人ID列表
    }
  },
  onLoad(options) {
    // 设置状态栏高度
    this.statusBarHeight = uni.getSystemInfoSync().statusBarHeight;

    if (options.id) {
      this.repairId = options.id;
      // 先加载用户列表，再获取维修详情
      this.loadUserList().then(() => {
        this.getRepairDetail();
      });
    } else {
      this.loading = false;
      uni.showToast({
        title: '缺少任务ID',
        icon: 'none'
      });
    }
  },
  methods: {
    // 获取维修详情
    async getRepairDetail() {
      try {
        const response = await getFaultRepair(this.repairId);
        this.repairInfo = response.data;

        // 清空当前选中的用户ID
        this.selectedUserIds = [];
        this.chargeUserIds = [];

        // 如果有负责人，解析并设置负责人ID列表
        if (this.repairInfo && this.repairInfo.assignChargeUser) {
          const chargeUserIds = this.repairInfo.assignChargeUser.split(',');
          this.chargeUserIds = chargeUserIds.map(id => id.toString());
        }

        // 如果有分配的维修人员，解析并设置选中的用户ID
        if (this.repairInfo && this.repairInfo.assignUser) {
          // 将字符串ID转换为数组
          const assignUserIds = this.repairInfo.assignUser.split(',');

          // 将每个ID转换为字符串并设置到selectedUserIds
          this.$nextTick(() => {
            this.selectedUserIds = assignUserIds.map(id => id.toString());
          });
        }
      } catch (error) {
        console.error('获取维修详情失败:', error);
        uni.showToast({
          title: '获取维修详情失败',
          icon: 'none'
        });
      }
    },

    // 加载用户列表
    loadUserList() {
      this.loading = true;
      return new Promise((resolve, reject) => {
        listUser({ pageSize: 999, status: '0' }).then(response => {
          this.userList = response.rows || [];
          this.loading = false;
          resolve();
        }).catch(error => {
          console.error('获取用户列表失败:', error);
          uni.showToast({
            title: '获取用户列表失败',
            icon: 'none'
          });
          this.loading = false;
          reject(error);
        });
      });
    },

    // 判断用户是否被选中
    isUserSelected(userId) {
      return this.selectedUserIds.includes(userId.toString());
    },

    // 判断用户是否为负责人
    isChargeUser(userId) {
      return this.chargeUserIds.includes(userId.toString());
    },

    // 处理用户点击事件
    handleUserClick(userId) {
      const userIdStr = userId.toString();

      // 如果是负责人且已选中，给出提示不允许取消
      if (this.isChargeUser(userId) && this.selectedUserIds.includes(userIdStr)) {
        uni.showToast({
          title: '负责人不可取消选中',
          icon: 'none',
          duration: 2000
        });
        return;
      }

      // 否则正常切换选中状态
      this.toggleUserSelection(userId);
    },

    // 切换用户选中状态
    toggleUserSelection(userId) {
      const userIdStr = userId.toString();

      // 如果是负责人，不允许取消选中
      if (this.isChargeUser(userId)) {
        // 如果负责人未选中，则选中
        if (!this.selectedUserIds.includes(userIdStr)) {
          this.selectedUserIds.push(userIdStr);
        }
        // 如果已选中，不做任何操作（不允许取消）
        return;
      }

      const index = this.selectedUserIds.indexOf(userIdStr);

      if (index === -1) {
        // 如果不在选中列表中，添加
        this.selectedUserIds.push(userIdStr);
      } else {
        // 如果已经选中，移除
        this.selectedUserIds.splice(index, 1);
      }
    },

    // 确认接收任务
    async handleConfirm() {
      if (!this.selectedUserIds || !this.selectedUserIds.length) {
        uni.showToast({
          title: '请选择实际维修人员',
          icon: 'none'
        });
        return;
      }

      try {
        uni.showLoading({
          title: '提交中...'
        });

        // 获取选中用户的名称
        const selectedUserNames = this.userList
          .filter(user => this.selectedUserIds.includes(user.userId.toString()))
          .map(user => user.nickName)
          .join(',');

        // 构建提交数据
        const data = {
          repairId: this.repairId,
          repairUser: this.selectedUserIds.join(','),
          status: '2' // 已接收状态
        };

        await receiveFaultRepair(data);

        uni.hideLoading();
        uni.showToast({
          title: '接收成功',
          icon: 'success',
          duration: 2000,
          success: () => {
            // 返回上一页并刷新
            setTimeout(() => {
              const pages = getCurrentPages();
              const prevPage = pages[pages.length - 2];
              // 触发上一页的刷新方法
              if (prevPage && prevPage.$vm && prevPage.$vm.refreshData) {
                prevPage.$vm.refreshData();
              }
              uni.navigateBack();
            }, 1000);
          }
        });
      } catch (error) {
        uni.hideLoading();
        console.error('接收任务失败:', error);
        uni.showToast({
          title: '接收任务失败',
          icon: 'none'
        });
      }
    },

    // 返回上一页
    goBack() {
      uni.navigateBack();
    },

    // 获取已选择的用户名称
    getSelectedUserNames() {
      if (!this.selectedUserIds.length) return '';

      const selectedUsers = this.userList.filter(user =>
        this.selectedUserIds.includes(user.userId.toString())
      );

      return selectedUsers.map(user => user.nickName).join('、');
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #ffffff;
}

/* 顶部导航栏 */
.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: linear-gradient(135deg, #1a56b3 0%, #3a7bd5 100%);
  padding: 10px 15px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.back-icon {
  padding: 5px;
}

.header-title {
  color: #ffffff;
  font-size: 18px;
  font-weight: bold;
}

/* 内容区域 */
.content {
  flex: 1;
}

.repair-receive {
  padding: 0;
}

/* 状态横幅 */
.status-banner {
  padding: 15px 16px;
  color: #ffffff;
  position: relative;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.status-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-left {
  display: flex;
  align-items: center;
}

.status-icon {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
}

.status-text {
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0.5px;
}

/* 上方操作按钮 */
.action-buttons-top {
  display: flex;
  gap: 10px;
}

.action-button-top {
  background-color: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.4);
  color: #ffffff;
  font-size: 13px;
  padding: 4px 12px;
  border-radius: 15px;
  height: auto;
  line-height: 1.5;
  font-weight: normal;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.action-button-top:active {
  transform: scale(0.95);
  background-color: rgba(255, 255, 255, 0.3);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.action-button-top.cancel {
  background-color: rgba(255, 255, 255, 0.15);
}

.action-button-top.confirm {
  background-color: rgba(255, 255, 255, 0.25);
}

.action-button-top.confirm[disabled] {
  opacity: 0.6;
  background-color: rgba(255, 255, 255, 0.1);
}

/* 分区样式 */
.detail-card {
  background-color: #ffffff;
  margin-bottom: 10px;
  position: relative;
}

.card-header {
  display: flex;
  align-items: center;
  padding: 15px 16px 10px;
  position: relative;
}

.card-title {
  font-size: 16px;
  font-weight: bold;
  margin-left: 8px;
  color: #1a56b3;
}

.card-content {
  padding: 5px 16px 15px;
}

/* 用户选择区域 */
.edit-tip {
  font-size: 12px;
  color: #909399;
  margin-left: auto;
  background-color: rgba(144, 147, 153, 0.1);
  padding: 2px 8px;
  border-radius: 10px;
}

.selection-tip {
  display: flex;
  align-items: center;
  background-color: #f8f9fc;
  padding: 8px 12px;
  border-radius: 4px;
  margin-bottom: 12px;
}

.tip-text {
  font-size: 13px;
  color: #606266;
  margin-left: 6px;
}

.user-list {
  display: flex;
  flex-direction: column;
  padding: 0;
  margin-bottom: 15px;
}

.user-item {
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
  position: relative;
}

.user-item:last-child {
  border-bottom: none;
}

.user-item:active {
  background-color: #f9f9f9;
}

.user-info {
  display: flex;
  align-items: center;
  position: relative;
  padding-left: 10px;
}

.user-info::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 14px;
  background-color: #1a56b3;
  opacity: 0.6;
  border-radius: 1.5px;
}

.checkbox-custom {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 22px;
  height: 22px;
}

.checkbox-custom.checked {
  color: #1a56b3;
}

.checkbox-custom.charge {
  color: #e6a23c;
}

.user-name {
  margin-left: 10px;
  font-size: 15px;
  color: #333;
  font-weight: 500;
  flex: 1;
}

.user-name.charge-name {
  color: #e6a23c;
  font-weight: 600;
}

.charge-tag {
  font-size: 12px;
  color: #e6a23c;
  background-color: rgba(230, 162, 60, 0.1);
  padding: 2px 6px;
  border-radius: 10px;
  margin-left: 8px;
}

.selected-info {
  background-color: #f0f7ff;
  padding: 10px 12px;
  border-radius: 4px;
  border-left: 3px solid #1a56b3;
}

.selected-count {
  font-size: 14px;
  font-weight: bold;
  color: #1a56b3;
  display: block;
  margin-bottom: 5px;
}

.selected-names {
  font-size: 13px;
  color: #606266;
  line-height: 1.4;
}

/* 状态样式 */
.status-pending {
  background: linear-gradient(135deg, #e6a23c 0%, #f0b95a 100%);
}
</style>
