<template>
  <view class="container">
    <!-- 顶部导航栏 -->
    <view class="header" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="header-content">
        <view class="back-icon" @click="goBack">
          <uni-icons type="left" size="18" color="#FFFFFF"></uni-icons>
        </view>
        <view class="header-title">故障详情</view>
        <view class="header-right">
          <view class="refresh-icon" @click="refreshData">
            <uni-icons type="refresh" size="18" color="#FFFFFF"></uni-icons>
          </view>
        </view>
      </view>
    </view>

    <!-- 内容区域 -->
    <scroll-view scroll-y class="content" :style="{ paddingTop: (statusBarHeight + 44) + 'px' }">
      <view v-if="faultInfo" class="inspection-detail">
        <!-- 故障状态标识 -->
        <view class="status-banner" :class="getStatusClass(faultInfo.status)">
          <view class="status-content">
            <view class="status-left">
              <view class="status-icon">
                <uni-icons :type="getStatusIcon(faultInfo.status)" size="18" color="#FFFFFF"></uni-icons>
              </view>
              <text class="status-text">{{ getStatusLabel(faultInfo.status) }}</text>
            </view>
          </view>
          <!-- 状态描述 -->
          <view class="status-description" v-if="getStatusDescription(faultInfo.status)">
            <text class="status-desc-text">{{ getStatusDescription(faultInfo.status) }}</text>
          </view>
        </view>

        <!-- 基本信息卡片 -->
        <view class="detail-card">
          <view class="card-header">
            <uni-icons type="info-filled" size="16" color="#2979ff"></uni-icons>
            <text class="card-title">基本信息</text>
          </view>
          <view class="card-content">
            <view class="info-item">
              <text class="info-label">故障名称</text>
              <text class="info-value">{{ faultInfo.faultName }}</text>
            </view>
            <view class="info-item">
              <text class="info-label">所属电站</text>
              <text class="info-value">{{ faultInfo.station ? faultInfo.station.stationName : '未知' }}</text>
            </view>
            <view class="info-item">
              <text class="info-label">故障类型</text>
              <text class="info-value">{{ getFaultTypeLabel(faultInfo.faultType) }}</text>
            </view>
            <view class="info-item">
              <text class="info-label">故障等级</text>
              <text class="info-value" :class="getFaultLevelClass(faultInfo.faultLevel)">{{ getFaultLevelLabel(faultInfo.faultLevel) }}</text>
            </view>
            <view class="info-item">
              <text class="info-label">故障来源</text>
              <text class="info-value">{{ getFaultSourceLabel(faultInfo.faultSource) }}</text>
            </view>
            <view class="info-item">
              <text class="info-label">发现人员</text>
              <text class="info-value">{{ faultInfo.discoverUserName || '未知' }}</text>
            </view>
            <view class="info-item">
              <text class="info-label">发现时间</text>
              <text class="info-value">{{ formatDate(faultInfo.discoverTime) }}</text>
            </view>
            <view class="info-item" v-if="faultInfo.reviewUserName">
              <text class="info-label">审核人员</text>
              <text class="info-value">{{ faultInfo.reviewUserName }}</text>
            </view>
            <view class="info-item" v-if="faultInfo.reviewTime">
              <text class="info-label">审核时间</text>
              <text class="info-value">{{ formatDateTime(faultInfo.reviewTime) }}</text>
            </view>
          </view>
        </view>

        <!-- 故障描述卡片 -->
        <view class="detail-card" v-if="faultInfo.faultDescription">
          <view class="card-header">
            <uni-icons type="compose" size="16" color="#2979ff"></uni-icons>
            <text class="card-title">故障描述</text>
          </view>
          <view class="card-content">
            <view class="description-content">
              <text class="description-text">{{ faultInfo.faultDescription }}</text>
            </view>
          </view>
        </view>

        <!-- 相关设备卡片 -->
        <view class="detail-card">
          <view class="card-header">
            <uni-icons type="gear-filled" size="16" color="#2979ff"></uni-icons>
            <text class="card-title">相关设备</text>
          </view>
          <view class="card-content">
            <view v-if="equipmentList.length > 0">
              <view class="equipment-list">
                <view class="equipment-item" v-for="(item, index) in equipmentList" :key="index" @click="goToEquipmentDetail(item)">
                  <view class="equipment-info">
                    <view class="equipment-name">{{ item.equipmentName }}</view>
                    <view class="equipment-type">{{ getEquipmentTypeLabel(item.equipmentType) }}</view>
                  </view>
                  <view class="equipment-arrow">
                    <uni-icons type="right" size="14" color="#999"></uni-icons>
                  </view>
                </view>
              </view>
            </view>
            <view v-else class="empty-equipment">
              <uni-icons type="info" size="36" color="#ddd"></uni-icons>
              <text>暂无关联设备</text>
            </view>
          </view>
        </view>

        <!-- 故障图片卡片 -->
        <view class="detail-card" v-if="faultImages.length > 0">
          <view class="card-header">
            <uni-icons type="camera-filled" size="16" color="#2979ff"></uni-icons>
            <text class="card-title">故障图片</text>
          </view>
          <view class="card-content">
            <view class="fault-images">
              <image
                v-for="(image, index) in faultImages"
                :key="index"
                :src="getImageUrl(image)"
                class="fault-image"
                mode="aspectFill"
                @click="previewImage(faultImages, index)"
              />
            </view>
          </view>
        </view>

        <!-- 审核意见卡片 -->
        <view class="detail-card" v-if="faultInfo.reviewComment">
          <view class="card-header">
            <uni-icons type="chat" size="16" color="#2979ff"></uni-icons>
            <text class="card-title">审核意见</text>
          </view>
          <view class="card-content">
            <view class="review-content">
              <text class="review-text">{{ faultInfo.reviewComment }}</text>
            </view>
          </view>
        </view>
      </view>

      <loading-container
        v-else
        :loading="loading"
        :error="!loading && !faultInfo"
        loadingText="正在加载故障详情..."
        errorText="获取故障详情失败"
        @retry="refreshData"
      />
    </scroll-view>
  </view>
</template>

<script>
import { getFault } from '@/api/system/fault'
import { listEquipmentByStationId } from '@/api/system/equipment'
import { getDictData } from '@/api/system/dictData'
import LoadingContainer from '@/components/LoadingContainer'
import { getImageUrl } from '@/utils/imageUtils'

export default {
  components: {
    LoadingContainer
  },
  data() {
    return {
      statusBarHeight: 0,
      faultId: null,
      faultInfo: null,
      equipmentList: [],
      faultImages: [],
      loading: true,
      userId: null,
      dictData: {}
    }
  },
  onLoad(options) {
    // 设置状态栏高度
    this.statusBarHeight = uni.getSystemInfoSync().statusBarHeight;

    if (options.id) {
      this.faultId = options.id;

      // 获取当前登录用户ID
      this.userId = this.$store.state.user.userId;

      this.getDicts();
    } else {
      this.loading = false;
    }
  },
  methods: {
    // 获取字典数据
    async getDicts() {
      try {
        const response = await getDictData();
        this.dictData = this.$dict.parseDictData(response.data);
        this.getFaultDetail();
      } catch (error) {
        console.error('获取字典数据失败:', error);
        uni.showToast({
          title: '获取字典数据失败',
          icon: 'none'
        });
        this.loading = false;
      }
    },

    // 获取故障详情
    async getFaultDetail() {
      try {
        const response = await getFault(this.faultId);
        this.faultInfo = response.data;

        // 解析故障图片
        this.parseFaultImages();

        // 获取相关设备列表
        this.getEquipmentList();
      } catch (error) {
        console.error('获取故障详情失败:', error);
        uni.showToast({
          title: '获取故障详情失败',
          icon: 'none'
        });
        this.loading = false;
      }
    },

    // 解析故障图片
    parseFaultImages() {
      if (this.faultInfo.faultImages) {
        try {
          // 如果是JSON字符串，解析为数组
          if (typeof this.faultInfo.faultImages === 'string') {
            this.faultImages = this.faultInfo.faultImages.split(',').filter(img => img.trim());
          } else if (Array.isArray(this.faultInfo.faultImages)) {
            this.faultImages = this.faultInfo.faultImages;
          }
        } catch (error) {
          console.error('解析故障图片失败:', error);
          this.faultImages = [];
        }
      }
    },

    // 获取相关设备列表
    async getEquipmentList() {
      try {
        if (this.faultInfo.equipmentListId && this.faultInfo.stationId) {
          // 获取电站下的所有设备
          const response = await listEquipmentByStationId(this.faultInfo.stationId);
          const allEquipments = response.data || [];
          
          // 根据equipmentListId筛选相关设备
          const equipmentIds = this.faultInfo.equipmentListId.split(',').map(id => parseInt(id.trim()));
          this.equipmentList = allEquipments.filter(equipment => 
            equipmentIds.includes(equipment.equipmentId)
          );
        }
      } catch (error) {
        console.error('获取相关设备失败:', error);
        uni.showToast({
          title: '获取相关设备失败',
          icon: 'none'
        });
      } finally {
        this.loading = false;
      }
    },

    // 获取状态标签
    getStatusLabel(status) {
      if (!status) return '未知';
      return this.$dict.getDictLabel('fault_status', status, '未知');
    },

    // 获取状态样式类
    getStatusClass(status) {
      const statusClassMap = {
        '0': 'status-pending',     // 待确认
        '2': 'status-rejected',    // 已驳回
        '4': 'status-confirmed',   // 已确认
        '6': 'status-completed'    // 已完成
      };
      return statusClassMap[status] || 'status-unknown';
    },

    // 获取状态图标
    getStatusIcon(status) {
      const statusIconMap = {
        '0': 'notification-filled',  // 待确认
        '2': 'close',               // 已驳回
        '4': 'checkmarkempty',      // 已确认
        '6': 'medal-filled'         // 已完成
      };
      return this.validateIcon(statusIconMap[status]) || 'info-filled';
    },

    // 验证图标是否存在，如果不存在返回备用图标
    validateIcon(iconName) {
      const validIcons = [
        'notification-filled', 'close', 'checkmarkempty', 'medal-filled',
        'info-filled', 'gear-filled', 'camera-filled', 'compose', 'chat',
        'right', 'left', 'refresh'
      ];
      return validIcons.includes(iconName) ? iconName : null;
    },

    // 获取状态描述
    getStatusDescription(status) {
      const statusDescMap = {
        '0': '故障已上报，等待管理员确认',
        '2': '故障已被驳回，请重新核实',
        '4': '故障已确认，等待处理',
        '6': '故障已处理完成'
      };
      return statusDescMap[status] || '';
    },

    // 获取故障类型标签
    getFaultTypeLabel(type) {
      if (!type) return '未知类型';
      return this.$dict.getDictLabel('fault_type', type, '未知类型');
    },

    // 获取故障等级标签
    getFaultLevelLabel(level) {
      if (!level) return '未知等级';
      return this.$dict.getDictLabel('fault_level', level, '未知等级');
    },

    // 获取故障等级样式类
    getFaultLevelClass(level) {
      const levelClassMap = {
        '0': 'level-minor',     // 轻微
        '1': 'level-general',   // 一般
        '2': 'level-serious',   // 严重
        '3': 'level-urgent'     // 紧急
      };
      return levelClassMap[level] || '';
    },

    // 获取故障来源标签
    getFaultSourceLabel(source) {
      if (!source) return '未知来源';
      return this.$dict.getDictLabel('fault_source', source, '未知来源');
    },

    // 获取设备类型标签
    getEquipmentTypeLabel(type) {
      if (!type || !this.dictData.equipment_type) return '未知类型';
      return this.$dict.getDictLabel('equipment_type', type, '未知类型');
    },

    // 格式化日期
    formatDate(dateString) {
      if (!dateString) return '未设置';
      const date = new Date(dateString);
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
    },

    // 格式化日期时间
    formatDateTime(dateString) {
      if (!dateString) return '未设置';
      const date = new Date(dateString);
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}:${String(date.getSeconds()).padStart(2, '0')}`;
    },

    // 跳转到设备详情
    goToEquipmentDetail(equipment) {
      if (equipment && equipment.equipmentId) {
        uni.navigateTo({
          url: `/pages/equipment/detail?id=${equipment.equipmentId}`
        });
      }
    },

    // 获取图片URL
    getImageUrl(path) {
      return getImageUrl(path);
    },

    // 预览图片
    previewImage(images, current) {
      const urls = images.map(image => getImageUrl(image));
      uni.previewImage({
        urls: urls,
        current: current
      });
    },

    // 返回上一页
    goBack() {
      uni.navigateBack();
    },

    // 刷新数据
    refreshData() {
      this.loading = true;
      this.getDicts();
      uni.showToast({
        title: '刷新中',
        icon: 'loading',
        duration: 500
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #ffffff;
}

/* 顶部导航栏 */
.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: linear-gradient(135deg, #1a56b3 0%, #3a7bd5 100%);
  padding: 10px 15px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.back-icon {
  padding: 5px;
}

.header-title {
  color: #ffffff;
  font-size: 18px;
  font-weight: bold;
}

.refresh-icon {
  padding: 5px;
}

/* 内容区域 */
.content {
  flex: 1;
}

.inspection-detail {
  padding: 0;
}

/* 分区样式 */
.detail-card {
  background-color: #ffffff;
  margin-bottom: 10px;
  position: relative;
}

.card-header {
  display: flex;
  align-items: center;
  padding: 12px 16px 8px;
  position: relative;
}

.card-title {
  font-size: 16px;
  font-weight: bold;
  margin-left: 8px;
  color: #1a56b3;
}

.card-content {
  padding: 5px 16px 15px;
}

/* 设备列表卡片的内容区域特殊处理 */
.detail-card .card-content {
  padding: 8px 16px 12px;
}

/* 状态横幅 */
.status-banner {
  padding: 15px 16px;
  color: #ffffff;
  position: relative;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.status-banner.status-pending {
  background: linear-gradient(135deg, #e6a23c 0%, #f0b95a 100%);
}

.status-banner.status-rejected {
  background: linear-gradient(135deg, #f56c6c 0%, #f78989 100%);
}

.status-banner.status-confirmed {
  background: linear-gradient(135deg, #1a56b3 0%, #3a7bd5 100%);
}

.status-banner.status-completed {
  background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%);
}

.status-banner.status-unknown {
  background: linear-gradient(135deg, #606266 0%, #909399 100%);
}

.status-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-left {
  display: flex;
  align-items: center;
}

.status-icon {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
}

.status-text {
  font-size: 16px;
  font-weight: 500;
  letter-spacing: 0.5px;
}

.status-description {
  margin-top: 8px;
  margin-left: 38px;
}

.status-desc-text {
  color: rgba(255, 255, 255, 0.9);
  font-size: 13px;
  line-height: 1.4;
}

/* 信息项样式 */
.info-item {
  display: flex;
  margin-bottom: 10px;
  align-items: center;
  position: relative;
  border-bottom: 1px solid #f9f9f9;
  padding-bottom: 10px;
}

.info-item:last-child {
  margin-bottom: 0;
  border-bottom: none;
  padding-bottom: 0;
}

.info-label {
  width: 90px;
  color: #666;
  font-size: 14px;
  flex-shrink: 0;
  position: relative;
  padding-left: 10px;
}

.info-label::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 14px;
  background-color: #1a56b3;
  opacity: 0.6;
  border-radius: 1.5px;
}

.info-value {
  flex: 1;
  color: #333;
  font-size: 14px;
  word-break: break-all;
  font-weight: 500;
}

/* 故障等级颜色 */
.level-minor {
  color: #52c41a;
}

.level-general {
  color: #faad14;
}

.level-serious {
  color: #ff7875;
}

.level-urgent {
  color: #f5222d;
  font-weight: bold;
}

/* 描述内容 */
.description-content {
  padding: 10px 0;
}

.description-text {
  font-size: 14px;
  color: #333333;
  line-height: 1.6;
}

/* 设备列表 */
.equipment-list {
  padding: 0;
  background-color: #ffffff;
}

.equipment-item {
  border-bottom: 1px solid #f0f0f0;
  position: relative;
  background-color: #ffffff;
  margin-bottom: 0;
  padding: 8px 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: background-color 0.2s ease;
}

.equipment-item:last-child {
  border-bottom: none;
}

.equipment-item:active {
  background-color: #f8f9fa;
}

.equipment-info {
  flex: 1;
  min-width: 0;
}

.equipment-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  position: relative;
  padding-left: 10px;
  margin-bottom: 2px;
  line-height: 1.4;
}

.equipment-name::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 12px;
  background-color: #1a56b3;
  border-radius: 1.5px;
}

.equipment-type {
  background-color: #409eff;
  color: #ffffff;
  padding: 1px 6px;
  border-radius: 3px;
  font-size: 10px;
  font-weight: 500;
  margin-left: 10px;
  display: inline-block;
}

.equipment-arrow {
  margin-left: 8px;
  padding: 4px;
}

/* 故障图片 */
.fault-images {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  padding: 8px 0;
}

.fault-image {
  width: 70px;
  height: 70px;
  border-radius: 6px;
  border: 1px solid #e0e0e0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease;
}

.fault-image:active {
  transform: scale(0.95);
}

/* 审核意见 */
.review-content {
  padding: 10px 0;
}

.review-text {
  font-size: 14px;
  color: #333333;
  line-height: 1.6;
  background-color: #f8f9fa;
  padding: 12px;
  border-radius: 8px;
  border-left: 4px solid #2979ff;
}

/* 空状态样式 */
.empty-equipment {
  text-align: center;
  padding: 20px 0;
  color: #999;
  font-size: 14px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 10px;
}
</style>
