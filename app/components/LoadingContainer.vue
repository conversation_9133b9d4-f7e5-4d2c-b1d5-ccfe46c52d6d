<template>
  <view class="loading-container" :style="{ height: containerHeight }">
    <!-- 加载中状态 -->
    <template v-if="loading">
      <view class="loading-icon rotating">
        <uni-icons type="spinner-cycle" :size="iconSize" :color="iconColor"></uni-icons>
      </view>
      <text class="loading-text">{{ loadingText }}</text>
    </template>

    <!-- 错误状态 -->
    <template v-else-if="error">
      <view class="error-icon">
        <uni-icons type="error" :size="iconSize" :color="errorIconColor"></uni-icons>
      </view>
      <text class="error-text">{{ errorText }}</text>
      <button v-if="showRetry" class="retry-button" @click="handleRetry">{{ retryText }}</button>
    </template>
  </view>
</template>

<script>
export default {
  name: 'LoadingContainer',
  props: {
    // 是否处于加载状态
    loading: {
      type: Boolean,
      default: true
    },
    // 是否处于错误状态
    error: {
      type: Boolean,
      default: false
    },
    // 加载状态显示的文字
    loadingText: {
      type: String,
      default: '正在加载...'
    },
    // 错误状态显示的文字
    errorText: {
      type: String,
      default: '加载失败'
    },
    // 重试按钮显示的文字
    retryText: {
      type: String,
      default: '重试'
    },
    // 图标颜色
    iconColor: {
      type: String,
      default: '#1a56b3'
    },
    // 错误图标颜色
    errorIconColor: {
      type: String,
      default: '#f56c6c'
    },
    // 图标大小
    iconSize: {
      type: [Number, String],
      default: 40
    },
    // 容器高度
    height: {
      type: [Number, String],
      default: 300
    },
    // 是否显示重试按钮
    showRetry: {
      type: Boolean,
      default: true
    }
  },
  computed: {
    // 计算容器高度
    containerHeight() {
      if (typeof this.height === 'number') {
        return `${this.height}px`;
      }
      return this.height;
    }
  },
  methods: {
    // 处理重试按钮点击事件
    handleRetry() {
      this.$emit('retry');
    }
  }
}
</script>

<style lang="scss" scoped>
.loading-container,
.error-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 300px;
  color: #999;
  font-size: 14px;
}

.loading-icon,
.error-icon {
  margin-bottom: 12px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.rotating {
  animation: rotate 1.5s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.loading-text,
.error-text {
  font-size: 14px;
  color: #999;
  margin-bottom: 15px;
}

.retry-button {
  margin-top: 5px;
  background-color: #1a56b3;
  color: #fff;
  font-size: 14px;
  padding: 6px 20px;
  border-radius: 4px;
  border: none;
}
</style>
