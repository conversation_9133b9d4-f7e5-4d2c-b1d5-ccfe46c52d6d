<template>
  <view class="image-preview">
    <image 
      :src="realSrc" 
      :style="`width:${realWidth};height:${realHeight};`" 
      :mode="mode" 
      @click="previewImage"
      @error="handleError"
    />
  </view>
</template>

<script>
import { getImageUrl, getImageList } from '@/utils/imageUtils'

export default {
  name: 'ImagePreview',
  props: {
    src: {
      type: String,
      default: ''
    },
    width: {
      type: [Number, String],
      default: '100%'
    },
    height: {
      type: [Number, String],
      default: '100%'
    },
    mode: {
      type: String,
      default: 'aspectFill'
    }
  },
  computed: {
    realSrc() {
      if (!this.src) {
        return '';
      }
      return getImageUrl(this.src.split(',')[0]);
    },
    realSrcList() {
      if (!this.src) {
        return [];
      }
      return getImageList(this.src);
    },
    realWidth() {
      return typeof this.width === 'string' ? this.width : `${this.width}px`;
    },
    realHeight() {
      return typeof this.height === 'string' ? this.height : `${this.height}px`;
    }
  },
  methods: {
    previewImage() {
      if (this.realSrcList.length === 0) {
        return;
      }
      
      uni.previewImage({
        current: this.realSrc,
        urls: this.realSrcList,
        indicator: 'number',
        loop: true,
        fail(err) {
          console.error('预览图片失败:', err);
        }
      });
    },
    handleError() {
      this.$emit('error');
    }
  }
}
</script>

<style lang="scss" scoped>
.image-preview {
  position: relative;
  overflow: hidden;
  border-radius: 4px;
  background-color: #f5f5f5;
  
  image {
    transition: all 0.3s;
    
    &:hover {
      opacity: 0.9;
    }
  }
}
</style>
