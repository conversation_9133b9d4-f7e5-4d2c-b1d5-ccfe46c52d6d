<template>
  <view class="power-stats-card">
    <!-- 卡片头部 -->
    <uni-collapse v-model="collapseValue" @change="onCollapseChange">
      <uni-collapse-item name="powerStats" :show-arrow="false">
        <template #title>
          <view class="card-header-custom">
            <view class="card-header-left">
              <uni-icons type="bars" size="16" color="#2979ff"></uni-icons>
              <text class="card-title">发电统计</text>
            </view>
            <view class="card-header-right">
              <text class="collapse-text" :class="{ 'expanded': isExpanded }">
                {{ isExpanded ? '收起' : '展开' }}
              </text>
              <view class="collapse-arrow" :class="{ 'expanded': isExpanded }">
                <uni-icons type="bottom" size="16" color="#2979ff"></uni-icons>
              </view>
            </view>
          </view>
        </template>
        
        <!-- 图表内容 -->
        <view class="card-content" v-if="isExpanded">
          <!-- 数据说明 -->
          <view class="data-description">
            <text class="description-text">
              {{ activeChartType === 'line' ? '反向有功总电量趋势分析' : '上一月反向有功电量对比分析' }}
            </text>
            <text class="description-note">红色为汇总数据，其他颜色为各设备数据</text>
          </view>

          <!-- 图表类型切换和全屏按钮 -->
          <view class="chart-controls">
            <view class="chart-tabs">
              <view
                class="tab-item"
                :class="{ 'active': activeChartType === 'line' }"
                @click="switchChartType('line')"
              >
                <uni-icons type="up" size="14" color="#2979ff"></uni-icons>
                <text>趋势图</text>
              </view>
              <view
                class="tab-item"
                :class="{ 'active': activeChartType === 'column' }"
                @click="switchChartType('column')"
              >
                <uni-icons type="bars" size="14" color="#2979ff"></uni-icons>
                <text>对比图</text>
              </view>
            </view>

            <!-- 全屏按钮 -->
            <view class="fullscreen-btn" @click="toggleFullscreen">
              <uni-icons type="scan" size="14" color="#2979ff"></uni-icons>
              <text class="fullscreen-text">全屏</text>
            </view>
          </view>



          <!-- 图表容器 -->
          <view
            class="chart-container"
            :class="{
              'fullscreen': isFullscreen,
              'landscape': isFullscreen && isLandscape
            }"
            v-if="!loading && chartData && chartVisible"
          >
            <qiun-data-charts
              :key="chartKey"
              :type="activeChartType"
              :opts="currentChartOpts"
              :chartData="currentChartData"
              :canvas2d="true"
              :background="'#FFFFFF'"
              @getIndex="chartClick"
            />

            <!-- 全屏模式下的控制按钮 -->
            <view v-if="isFullscreen" class="fullscreen-controls">
              <!-- 关闭按钮 -->
              <view class="fullscreen-close" @click="toggleFullscreen">
                <uni-icons type="clear" size="20" color="#666"></uni-icons>
              </view>
            </view>
          </view>

          <!-- 加载状态 -->
          <loading-container
            v-if="loading || (!loading && !chartData) || (!chartVisible && chartData)"
            :loading="loading || (!chartVisible && !!chartData)"
            :error="!loading && !chartData && chartVisible"
            loadingText="正在加载发电统计数据..."
            errorText="暂无发电统计数据"
            @retry="loadData"
            height="200"
          />
        </view>
      </uni-collapse-item>
    </uni-collapse>
  </view>
</template>

<script>
import { listMeterReadingByStationId, getMeterReadingEquipments } from '@/api/system/meterReading'
import LoadingContainer from '@/components/LoadingContainer'

export default {
  name: 'PowerGenerationStats',
  components: {
    LoadingContainer
  },
  props: {
    stationId: {
      type: [String, Number],
      required: true
    }
  },
  data() {
    return {
      // 折叠状态
      collapseValue: [],
      isExpanded: false,
      
      // 图表配置
      activeChartType: 'line', // line, column
      isFullscreen: false, // 全屏状态
      isLandscape: false, // 横屏状态
      chartVisible: true, // 图表可见性，用于强制重新渲染

      // 数据
      loading: false,
      rawData: [], // 原始数据
      chartData: null,
      chartKey: 0, // 用于强制图表重新渲染
      
      // 图表配置
      chartOpts: {
        color: ['#FF6B6B', '#2979ff', '#67c23a', '#e6a23c', '#f56c6c', '#909399', '#c23531', '#36CFC9', '#722ED1', '#EB2F96'],
        padding: [15, 15, 50, 15],
        enableScroll: false,
        legend: {
          show: true,
          position: 'bottom',
          lineHeight: 22,
          itemGap: 12,
          fontSize: 11,
          fontColor: '#666666'
        },
        xAxis: {
          disableGrid: false,
          fontSize: 11,
          fontColor: '#666666'
        },
        yAxis: {
          gridType: 'dash',
          dashLength: 2,
          fontSize: 11,
          fontColor: '#666666',
          format: (val) => {
            return val + 'kWh'
          }
        },
        extra: {
          line: {
            type: 'straight',
            width: 2,
            activeType: 'hollow'
          },
          column: {
            type: 'group',
            width: 25,
            activeBgColor: '#000000',
            activeBgOpacity: 0.08
          }
        }
      }
    }
  },
  computed: {
    currentChartData() {
      if (!this.chartData) return null

      // 根据图表类型返回不同的数据
      let series = []

      if (this.activeChartType === 'line') {
        // 趋势图：显示反向有功总数据
        series = this.chartData.totalSeries
      } else {
        // 对比图：显示上一月反向有功数据
        series = this.chartData.lastMonthSeries
      }

      return {
        categories: this.chartData.categories,
        series: series
      }
    },

    // 动态图表配置
    currentChartOpts() {
      // 深拷贝原始配置，避免修改原始对象
      const opts = JSON.parse(JSON.stringify(this.chartOpts))

      if (this.isFullscreen) {
        if (this.isLandscape) {
          // 横屏模式下的配置 - 增加内边距
          opts.padding = [40, 60, 80, 60]
          opts.legend.fontSize = 12
          opts.legend.lineHeight = 25
          opts.legend.position = 'right'
          opts.legend.itemGap = 10
          opts.xAxis.fontSize = 12
          opts.yAxis.fontSize = 12
        } else {
          // 竖屏全屏模式下的配置
          opts.padding = [50, 40, 100, 40]
          opts.legend.fontSize = 14
          opts.legend.lineHeight = 30
          opts.legend.position = 'bottom'
          opts.xAxis.fontSize = 13
          opts.yAxis.fontSize = 13
        }
      }

      return opts
    }
  },
  watch: {
    stationId: {
      handler(newVal) {
        if (newVal && this.isExpanded) {
          this.loadData()
        }
      },
      immediate: false
    }
  },
  mounted() {
    // 监听返回键事件
    this.handleBackButton()
    // 监听屏幕方向变化
    this.handleOrientationChange()
  },
  beforeDestroy() {
    // 组件销毁前确保屏幕方向恢复
    this.resetScreenOrientation()
    // 移除事件监听器
    // #ifdef H5
    if (this.handlePopstate) {
      window.removeEventListener('popstate', this.handlePopstate)
    }
    // #endif
  },
  // uni-app 生命周期
  onHide() {
    // 页面隐藏时确保屏幕方向恢复
    this.resetScreenOrientation()
  },
  onUnload() {
    // 页面卸载时确保屏幕方向恢复
    this.resetScreenOrientation()
  },
  methods: {
    // 折叠状态变化
    onCollapseChange(activeNames) {
      this.isExpanded = activeNames.includes('powerStats')
      if (this.isExpanded && !this.chartData) {
        this.loadData()
      }
    },
    
    // 切换图表类型
    switchChartType(type) {
      this.activeChartType = type
      this.chartKey++ // 强制图表重新渲染
      this.updateChartOpts()
    },
    

    
    // 更新图表配置
    updateChartOpts() {
      // 图表会通过key的变化自动重新渲染
    },

    // 切换全屏
    toggleFullscreen() {
      // 临时隐藏图表
      this.chartVisible = false

      // 切换全屏状态
      this.isFullscreen = !this.isFullscreen

      if (this.isFullscreen) {
        // 进入全屏时自动设置为横屏
        this.isLandscape = true
        this.setScreenOrientation('landscape')

        // #ifdef H5
        // 在H5环境下，进入全屏时添加一个历史状态
        history.pushState({ fullscreen: true }, null, location.href)
        // #endif
      } else {
        // 退出全屏时重置为竖屏
        this.isLandscape = false
        this.setScreenOrientation('portrait')
      }

      // 使用更长的延迟确保DOM完全更新和屏幕方向切换完成
      setTimeout(() => {
        this.chartVisible = true
        this.chartKey++ // 强制图表重新渲染以应用新配置
      }, 200)
    },



    // 设置屏幕方向
    setScreenOrientation(orientation) {
      // #ifdef APP-PLUS
      plus.screen.lockOrientation(orientation)
      // #endif

      // #ifdef H5
      if (screen.orientation && screen.orientation.lock) {
        screen.orientation.lock(orientation).catch(err => {
          console.log('屏幕方向锁定失败:', err)
        })
      }
      // #endif

      // #ifdef MP
      // 小程序环境下的处理
      try {
        uni.setScreenOrientation && uni.setScreenOrientation({
          orientation: orientation
        })
      } catch (error) {
        console.log('小程序屏幕方向设置失败:', error)
      }
      // #endif
    },
    
    // 加载数据
    async loadData() {
      if (!this.stationId) return

      this.loading = true
      try {
        // 获取电站的电量统计任务
        const response = await listMeterReadingByStationId({
          stationId: this.stationId,
          pageSize: 9999
        })
        
        const meterReadingList = response.rows || []
        
        // 过滤已完成的任务（状态为4或6）
        const completedTasks = meterReadingList.filter(task => 
          task.status === '4' || task.status === '6'
        )
        
        if (completedTasks.length === 0) {
          this.chartData = null
          return
        }
        
        // 获取每个任务的设备数据
        const allEquipmentData = []
        for (const task of completedTasks) {
          try {
            const equipmentResponse = await getMeterReadingEquipments(task.meterReadingId)
            const equipments = equipmentResponse.data || []
            
            // 过滤有效数据
            const validEquipments = equipments.filter(eq => 
              eq.meterReadingTime && 
              (eq.totalReverseActive !== null || eq.lastMonthReverseActive !== null)
            )
            
            allEquipmentData.push(...validEquipments)
          } catch (error) {
            console.error(`获取任务${task.meterReadingId}的设备数据失败:`, error)
          }
        }
        
        // 处理数据
        if (allEquipmentData.length > 0) {
          this.processChartData(allEquipmentData)
        } else {
          // 如果没有数据，创建一些模拟数据用于演示
          this.createMockData()
        }

      } catch (error) {
        console.error('加载发电统计数据失败:', error)
        // 出错时也显示模拟数据
        this.createMockData()
      } finally {
        this.loading = false
      }
    },

    // 创建模拟数据用于演示
    createMockData() {
      const mockData = []
      const currentDate = new Date()
      const equipmentList = ['逆变器A', '逆变器B', '逆变器C']

      // 生成最近6个月的模拟数据
      for (let i = 5; i >= 0; i--) {
        const date = new Date(currentDate.getFullYear(), currentDate.getMonth() - i, 1)

        equipmentList.forEach((equipmentName, index) => {
          const baseValue = 800 + index * 200
          // 反向有功总应该是累积值，呈上升趋势
          const totalValue = baseValue + i * 150 + Math.random() * 100
          // 上一月反向有功是月度增量，相对较小
          const lastMonthValue = 80 + index * 20 + i * 15 + Math.random() * 30

          mockData.push({
            meterReadingTime: date.toISOString(),
            totalReverseActive: totalValue,
            lastMonthReverseActive: lastMonthValue,
            equipment: { equipmentName: equipmentName }
          })
        })
      }

      this.processChartData(mockData)
    },
    
    // 处理图表数据
    processChartData(equipmentData) {
      if (!equipmentData || equipmentData.length === 0) {
        this.chartData = null
        return
      }
      
      // 按时间分组数据
      const timeGroups = {}
      equipmentData.forEach(item => {
        const timeKey = this.formatTimeKey(item.meterReadingTime)
        if (!timeGroups[timeKey]) {
          timeGroups[timeKey] = []
        }
        timeGroups[timeKey].push(item)
      })
      
      // 排序时间
      const sortedTimes = Object.keys(timeGroups).sort()
      
      // 获取所有设备名称
      const equipmentNames = [...new Set(equipmentData.map(item => 
        item.equipment ? item.equipment.equipmentName : '未知设备'
      ))]
      
      // 准备图表数据
      const categories = sortedTimes

      // 汇总数据系列
      const summaryTotalData = []
      const summaryLastMonthData = []

      sortedTimes.forEach(timeKey => {
        const timeData = timeGroups[timeKey]

        // 计算汇总
        const totalSum = timeData.reduce((sum, item) =>
          sum + (parseFloat(item.totalReverseActive) || 0), 0)
        const lastMonthSum = timeData.reduce((sum, item) =>
          sum + (parseFloat(item.lastMonthReverseActive) || 0), 0)

        summaryTotalData.push(totalSum)
        summaryLastMonthData.push(lastMonthSum)
      })

      // 生成反向有功总数据系列（用于趋势图）
      const totalSeries = []

      // 添加汇总数据系列 - 反向有功总
      totalSeries.push({
        name: '汇总数据',
        data: summaryTotalData
      })

      // 分设备数据系列 - 反向有功总
      equipmentNames.forEach(equipmentName => {
        const equipmentTotalData = []

        sortedTimes.forEach(timeKey => {
          const timeData = timeGroups[timeKey]
          const equipmentItem = timeData.find(item =>
            (item.equipment ? item.equipment.equipmentName : '未知设备') === equipmentName
          )

          equipmentTotalData.push(equipmentItem ? (parseFloat(equipmentItem.totalReverseActive) || 0) : 0)
        })

        totalSeries.push({
          name: equipmentName,
          data: equipmentTotalData
        })
      })

      // 生成上一月反向有功数据系列（用于对比图）
      const lastMonthSeries = []

      // 添加汇总数据系列 - 上一月反向有功
      lastMonthSeries.push({
        name: '汇总数据',
        data: summaryLastMonthData
      })

      // 分设备数据系列 - 上一月反向有功
      equipmentNames.forEach(equipmentName => {
        const equipmentLastMonthData = []

        sortedTimes.forEach(timeKey => {
          const timeData = timeGroups[timeKey]
          const equipmentItem = timeData.find(item =>
            (item.equipment ? item.equipment.equipmentName : '未知设备') === equipmentName
          )

          equipmentLastMonthData.push(equipmentItem ? (parseFloat(equipmentItem.lastMonthReverseActive) || 0) : 0)
        })

        lastMonthSeries.push({
          name: equipmentName,
          data: equipmentLastMonthData
        })
      })

      this.chartData = {
        categories,
        totalSeries,      // 反向有功总数据（趋势图用）
        lastMonthSeries   // 上一月反向有功数据（对比图用）
      }
    },
    
    // 格式化时间键
    formatTimeKey(timeStr) {
      if (!timeStr) return ''
      const date = new Date(timeStr)
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`
    },
    
    // 图表点击事件
    chartClick(e) {
      console.log('图表点击:', e)
    },

    // 处理返回键事件
    handleBackButton() {
      // #ifdef APP-PLUS
      // 监听Android返回键
      plus.key.addEventListener('backbutton', (e) => {
        if (this.isFullscreen) {
          // 阻止默认返回行为
          e.preventDefault && e.preventDefault()
          this.exitFullscreen()
          return false
        }
      })
      // #endif

      // #ifdef H5
      // 监听浏览器返回事件
      this.handlePopstate = (e) => {
        if (this.isFullscreen) {
          // 阻止默认返回行为
          e.preventDefault && e.preventDefault()
          this.exitFullscreen()
          // 推送一个新的历史状态，防止页面返回
          history.pushState(null, null, location.href)
          return false
        }
      }
      window.addEventListener('popstate', this.handlePopstate)
      // #endif
    },

    // 退出全屏（不切换状态，只恢复屏幕方向）
    exitFullscreen() {
      if (this.isFullscreen) {
        this.isFullscreen = false
        this.isLandscape = false
        this.setScreenOrientation('portrait')
        this.chartVisible = false
        setTimeout(() => {
          this.chartVisible = true
          this.chartKey++
        }, 200)
      }
    },

    // 重置屏幕方向
    resetScreenOrientation() {
      if (this.isFullscreen || this.isLandscape) {
        this.setScreenOrientation('portrait')
        this.isFullscreen = false
        this.isLandscape = false
      }
    },

    // 监听屏幕方向变化
    handleOrientationChange() {
      // #ifdef H5
      if (screen.orientation) {
        screen.orientation.addEventListener('change', () => {
          // 如果不是全屏状态但屏幕变成了横屏，强制恢复竖屏
          if (!this.isFullscreen && screen.orientation.angle !== 0) {
            this.setScreenOrientation('portrait')
          }
        })
      }
      // #endif

      // #ifdef APP-PLUS
      // 监听设备方向变化
      plus.orientation.addEventListener('orientationchange', (e) => {
        // 如果不是全屏状态但屏幕变成了横屏，强制恢复竖屏
        if (!this.isFullscreen && (e.orientation === 'landscape-primary' || e.orientation === 'landscape-secondary')) {
          this.setScreenOrientation('portrait')
        }
      })
      // #endif
    }
  }
}
</script>

<style lang="scss" scoped>
.power-stats-card {
  background-color: #ffffff;
  margin-bottom: 10px;
  position: relative;
}

/* 自定义卡片头部样式 */
.card-header-custom {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  position: relative;
  width: 100%;
  background-color: #ffffff;
  border-radius: 8px 8px 0 0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.card-header-left {
  display: flex;
  align-items: center;
}

.card-title {
  font-size: 15px;
  font-weight: bold;
  margin-left: 6px;
  color: #2979ff;
}

.card-header-right {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.collapse-text {
  font-size: 12px;
  color: #2979ff;
  font-weight: 500;
  transition: all 0.3s ease;
  border-radius: 12px;
}

.collapse-arrow {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  transition: all 0.3s ease;
  transform: rotate(0deg);
}

.collapse-arrow.expanded {
  transform: rotate(-180deg);
}

/* 卡片内容 */
.card-content {
  padding: 5px 16px 15px;
}

/* 数据说明 */
.data-description {
  margin-bottom: 10px;
  padding: 5px 8px;
  background-color: #f8f9fa;
  border-radius: 3px;
  border-left: 2px solid #2979ff;
}

.description-text {
  display: block;
  font-size: 12px;
  color: #333;
  font-weight: 600;
  margin-bottom: 1px;
  line-height: 1.3;
}

.description-note {
  display: block;
  font-size: 10px;
  color: #666;
  line-height: 1.2;
}

/* 图表控制区域 */
.chart-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

/* 图表类型切换标签 */
.chart-tabs {
  display: flex;
  background-color: #f8f9fa;
  border-radius: 4px;
  padding: 3px;
  flex: 1;
  margin-right: 10px;
}

.tab-item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 6px 10px;
  border-radius: 3px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.tab-item text {
  font-size: 12px;
  color: #666;
  margin-left: 3px;
  font-weight: 500;
}

.tab-item.active {
  background-color: #2979ff;
  color: #ffffff;
}

.tab-item.active text {
  color: #ffffff;
}



/* 全屏按钮 */
.fullscreen-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 6px 12px;
  background-color: #f8f9fa;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid #e9ecef;
}

.fullscreen-btn:hover {
  background-color: #e9ecef;
  border-color: #2979ff;
}

.fullscreen-btn:active {
  transform: scale(0.95);
}

.fullscreen-text {
  font-size: 12px;
  color: #2979ff;
  margin-left: 4px;
  font-weight: 500;
}

/* 图表容器 */
.chart-container {
  height: 350px;
  width: 100%;
  background-color: #ffffff;
  border-radius: 6px;
  overflow: hidden;
  position: relative;
}

/* 全屏模式 */
.chart-container.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9999;
  border-radius: 0;
  background-color: #ffffff;
  padding: 10px;
  box-sizing: border-box;
}

/* 横屏模式 */
.chart-container.landscape {
  /* 横屏模式下的特殊样式 */
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-container.landscape ::v-deep .chartsview {
  width: 100% !important;
  height: 100% !important;
}

/* 全屏控制按钮组 */
.fullscreen-controls {
  position: absolute;
  top: 30px;
  right: 15px;
  display: flex;
  gap: 10px;
}



/* 全屏关闭按钮 */
.fullscreen-close {
  width: 36px;
  height: 36px;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
}

.fullscreen-close:hover {
  background-color: rgba(255, 255, 255, 1);
  transform: scale(1.1);
}

.fullscreen-close:active {
  transform: scale(0.95);
}

/* 重写uni-collapse样式 */
.power-stats-card ::v-deep .uni-collapse-item__title {
  background-color: #ffffff;
  border-bottom: none;
}

.power-stats-card ::v-deep .uni-collapse-item__title-box {
  padding: 0;
  height: auto;
  background-color: transparent;
}

.power-stats-card ::v-deep .uni-collapse-item__wrap-content {
  background-color: #ffffff;
}
</style>
