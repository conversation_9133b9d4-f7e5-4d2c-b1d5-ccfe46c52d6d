<template>
  <view class="image-list">
    <view 
      class="image-item" 
      v-for="(item, index) in imageList" 
      :key="index"
      :style="`width:${itemWidth};height:${itemHeight};`"
      @click="previewImage(index)"
    >
      <image 
        :src="item" 
        mode="aspectFill" 
        class="image"
      />
    </view>
    <view class="image-item empty" v-if="imageList.length === 0">
      <uni-icons type="image" size="24" color="#c0c0c0"></uni-icons>
    </view>
  </view>
</template>

<script>
import { getImageList } from '@/utils/imageUtils'

export default {
  name: 'ImageList',
  props: {
    images: {
      type: String,
      default: ''
    },
    maxCount: {
      type: Number,
      default: 3
    },
    itemWidth: {
      type: String,
      default: '80px'
    },
    itemHeight: {
      type: String,
      default: '80px'
    }
  },
  computed: {
    imageList() {
      if (!this.images) {
        return [];
      }
      const list = getImageList(this.images);
      return list.slice(0, this.maxCount);
    }
  },
  methods: {
    previewImage(index) {
      if (this.imageList.length === 0) {
        return;
      }
      
      uni.previewImage({
        current: this.imageList[index],
        urls: getImageList(this.images),
        indicator: 'number',
        loop: true,
        fail(err) {
          console.error('预览图片失败:', err);
        }
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.image-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  
  .image-item {
    position: relative;
    overflow: hidden;
    border-radius: 4px;
    background-color: #f5f5f5;
    
    .image {
      width: 100%;
      height: 100%;
      transition: all 0.3s;
      
      &:hover {
        opacity: 0.9;
      }
    }
    
    &.empty {
      display: flex;
      justify-content: center;
      align-items: center;
      border: 1px dashed #dcdfe6;
    }
  }
}
</style>
