<template>
  <uni-popup ref="popup" type="bottom" :safe-area="true" :border-radius="'16px 16px 0 0'">
    <view class="equipment-selector">
      <!-- 头部 -->
      <view class="selector-header">
        <view class="header-left">
          <button class="header-btn cancel" @click="handleCancel">
            <text>取消</text>
          </button>
        </view>
        <view class="header-center">
          <text class="header-title">选择设备</text>
        </view>
        <view class="header-right">
          <button class="header-btn confirm" @click="handleConfirm">
            <text>确定</text>
          </button>
        </view>
      </view>

      <!-- 内容区域 -->
      <view class="selector-content">
        <scroll-view scroll-y class="equipment-list" :style="{ height: listHeight + 'px' }">
          <view class="equipment-checkbox-group" v-if="equipmentOptions.length > 0">
            <checkbox-group @change="handleEquipmentChange" :value="selectedStringIds">
              <label
                  class="equipment-checkbox-item"
                  :class="{ 'selected': selectedIds.includes(equipment.value) }"
                  v-for="equipment in equipmentOptions"
                  :key="equipment.value"
              >
                <checkbox
                    :value="equipment.value.toString()"
                    :checked="selectedIds.includes(equipment.value)"
                    color="#1a56b3"
                />
                <text class="equipment-name">{{ equipment.text }}</text>
                <view class="selected-indicator" v-if="selectedIds.includes(equipment.value)">
                  <uni-icons type="checkmarkempty" size="16" color="#1a56b3"></uni-icons>
                </view>
              </label>
            </checkbox-group>
          </view>

          <view class="equipment-empty" v-else>
            <view class="empty-icon">
              <uni-icons type="info-filled" size="32" color="#1a56b3"></uni-icons>
            </view>
            <text class="empty-text">暂无设备数据</text>
            <text class="empty-desc">请先选择电站后查看设备列表</text>
          </view>
        </scroll-view>
      </view>

      <!-- 底部统计 -->
      <view class="selector-footer" v-if="equipmentOptions.length > 0">
        <text class="selected-count">已选择 {{ selectedIds.length }} 个设备</text>
      </view>
    </view>
  </uni-popup>
</template>

<script>
export default {
  name: 'EquipmentSelector',
  props: {
    // 设备选项列表
    equipmentOptions: {
      type: Array,
      default: () => []
    },
    // 已选择的设备ID数组
    value: {
      type: Array,
      default: () => []
    },
    // 是否多选
    multiple: {
      type: Boolean,
      default: true
    },
    // 最大选择数量
    maxCount: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      selectedIds: [],
      listHeight: 400
    }
  },
  computed: {
    // 计算当前选中的设备ID字符串数组，用于checkbox-group
    selectedStringIds() {
      return this.selectedIds.map(id => id.toString())
    }
  },
  watch: {
    value: {
      handler(newVal) {
        this.selectedIds = [...newVal]
      },
      immediate: true
    }
  },
  mounted() {
    // 计算列表高度
    this.calculateListHeight()
  },
  methods: {
    // 打开选择器
    open() {
      this.$refs.popup.open('bottom')
    },

    // 关闭选择器
    close() {
      this.$refs.popup.close()
    },

    // 计算列表高度
    calculateListHeight() {
      const systemInfo = uni.getSystemInfoSync()
      const screenHeight = systemInfo.screenHeight
      const safeAreaBottom = systemInfo.safeAreaInsets ? systemInfo.safeAreaInsets.bottom : 0

      // 头部高度60px + 底部高度50px + 安全区域 + 预留空间
      this.listHeight = screenHeight * 0.6 - 60 - 50 - safeAreaBottom - 20
    },

    // 设备选择变化
    handleEquipmentChange(e) {
      const selectedValues = e.detail.value.map(id => parseInt(id))

      if (!this.multiple) {
        // 单选模式
        this.selectedIds = selectedValues.slice(-1) // 只保留最后一个
      } else {
        // 多选模式
        if (this.maxCount > 0 && selectedValues.length > this.maxCount) {
          uni.showToast({
            title: `最多只能选择${this.maxCount}个设备`,
            icon: 'none'
          })
          return
        }
        this.selectedIds = selectedValues
      }

      // 强制更新视图
      this.$forceUpdate()
    },

    // 取消选择
    handleCancel() {
      this.selectedIds = [...this.value] // 恢复原始值
      this.close()
    },

    // 确认选择
    handleConfirm() {
      this.$emit('input', this.selectedIds)
      this.$emit('change', this.selectedIds)
      this.close()
    }
  }
}
</script>

<style lang="scss" scoped>
.equipment-selector {
  background-color: #ffffff;
  border-radius: 20px 20px 0 0;
  overflow: hidden;
  box-shadow: 0 -8px 32px rgba(26, 86, 179, 0.08), 0 -2px 16px rgba(0, 0, 0, 0.04);
  backdrop-filter: blur(10px);

  .selector-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 50px;
    padding: 0 24px;
    background: linear-gradient(180deg, #ffffff 0%, #fafbfc 100%);
    position: relative;

    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 24px;
      right: 24px;
      height: 1px;
      background: linear-gradient(90deg, transparent 0%, rgba(26, 86, 179, 0.08) 50%, transparent 100%);
    }

    .header-left,
    .header-right {
      width: 88px;
    }

    .header-center {
      flex: 1;
      text-align: center;

      .header-title {
        font-size: 17px;
        font-weight: 600;
        color: #1a1a1a;
        letter-spacing: 0.2px;
        line-height: 1.3;
      }
    }

    .header-btn {
      padding: 0;
      font-size: 12px;
      font-weight: 500;
      background: none;
      width: 60px;

      &.cancel {
        color: #6b7280;
      }

      &.confirm {
        color: #1a56b3;
        font-weight: 600;
      }
    }
  }

  .selector-content {
    .equipment-list {
      padding: 8px 0;
    }

    .equipment-checkbox-group {
      display: flex;
      flex-direction: column;
      gap: 0;
    }

    .equipment-checkbox-item {
      display: flex;
      align-items: center;
      padding: 10px 16px;
      margin: 0 12px;
      position: relative;
      cursor: pointer;

      &::before {
        content: '';
        position: absolute;
        bottom: -1px;
        left: 24px;
        right: 24px;
        height: 1px;
      }

      &:last-child::before {
        display: none;
      }

      &.selected {
        .equipment-name {
          color: #1a56b3;
          font-weight: 600;
        }

        &::before {
          display: none;
        }
      }

      .equipment-name {
        font-size: 14px;
        color: #2d3748;
        flex: 1;
        line-height: 1.5;
        font-weight: 500;
        padding-left: 10px;
      }
    }

    .equipment-empty {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 80px 32px;
      text-align: center;

      .empty-icon {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        background: linear-gradient(135deg, rgba(26, 86, 179, 0.08) 0%, rgba(26, 86, 179, 0.04) 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 24px;
        box-shadow: 0 4px 20px rgba(26, 86, 179, 0.08);
      }

      .empty-text {
        font-size: 18px;
        color: #4a5568;
        font-weight: 600;
        margin-bottom: 12px;
        letter-spacing: 0.1px;
        line-height: 1.4;
      }

      .empty-desc {
        font-size: 15px;
        color: #9ca3af;
        font-weight: 400;
        line-height: 1.5;
        letter-spacing: 0.05px;
      }
    }
  }

  .selector-footer {
    padding: 10px 16px;
    background: linear-gradient(180deg, rgba(26, 86, 179, 0.02) 0%, rgba(26, 86, 179, 0.04) 100%);
    position: relative;
    backdrop-filter: blur(8px);

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 24px;
      right: 24px;
      height: 1px;
      background: linear-gradient(90deg, transparent 0%, rgba(26, 86, 179, 0.12) 50%, transparent 100%);
    }

    .selected-count {
      font-size: 13px;
      color: #1a56b3;
      font-weight: 600;
      text-align: center;
      letter-spacing: 0.1px;
      line-height: 1.4;
    }
  }
}
</style>
