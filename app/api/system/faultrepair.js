import request from '@/utils/request';

// 查询故障检修任务列表
export function listFaultRepair(query, userId) {
  return request({
    url: '/system/faultrepair/app_list/' + userId,
    method: 'get',
    params: query
  });
}

// 查询故障检修任务详细
export function getFaultRepair(repairId) {
  return request({
    url: '/system/faultrepair/app/' + repairId,
    method: 'get'
  });
}

// 获取故障检修任务统计信息
export function getFaultRepairStats(userId) {
  return request({
    url: '/system/faultrepair/stats',
    method: 'get',
    params: { userId }
  });
}

// 接收故障检修任务
export function receiveFaultRepair(data) {
  return request({
    url: '/system/faultrepair/app_receive',
    method: 'put',
    data: data
  });
}

// 提交故障检修任务
export function submitFaultRepair(data) {
  return request({
    url: '/system/faultrepair/app_submit',
    method: 'post',
    data: data
  });
}

// 更新故障检修任务状态
export function updateFaultRepairStatus(data) {
  return request({
    url: '/system/faultrepair/app_updateStatus',
    method: 'put',
    data: data
  });
}

// 根据电站ID查询故障检修任务列表
export function getFaultRepairsByStationId(stationId) {
  return request({
    url: '/system/faultrepair/station/' + stationId,
    method: 'get'
  });
}

// 更新故障检修任务内容
export function updateFaultRepair(data) {
  return request({
    url: '/system/faultrepair/app_update',
    method: 'put',
    data: data
  });
}

// 根据故障ID查询故障检修任务列表
export function getFaultRepairsByFaultId(faultId) {
  return request({
    url: '/system/faultrepair/fault/' + faultId,
    method: 'get'
  });
}
