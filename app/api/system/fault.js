import request from '@/utils/request';

// 查询故障信息列表
export function listFault(query) {
  return request({
    url: '/system/fault/app_list',
    method: 'get',
    params: query
  });
}

// 查询故障信息详细
export function getFault(faultId) {
  return request({
    url: '/system/fault/app/' + faultId,
    method: 'get'
  });
}

// 故障上报（从巡检/清洗发现）
export function reportFault(data) {
  return request({
    url: '/system/fault/app_report',
    method: 'post',
    data: data
  });
}

// 获取故障统计信息
export function getFaultStats(userId) {
  return request({
    url: '/system/fault/stats',
    method: 'get',
    params: { userId }
  });
}

// 查询待确认故障数量
export function getPendingFaultCount(userId) {
  return request({
    url: '/system/fault/count/pending',
    method: 'get',
    params: { userId }
  });
}

// 查询已确认故障数量
export function getConfirmedFaultCount(userId) {
  return request({
    url: '/system/fault/count/confirmed',
    method: 'get',
    params: { userId }
  });
}

// 查询已完成故障数量
export function getCompletedFaultCount(userId) {
  return request({
    url: '/system/fault/count/completed',
    method: 'get',
    params: { userId }
  });
}

// 根据电站ID查询故障信息列表
export function getFaultsByStationId(stationId) {
  return request({
    url: '/system/fault/station/' + stationId,
    method: 'get'
  });
}

// 根据设备ID查询故障信息列表
export function getFaultsByEquipmentId(equipmentId) {
  return request({
    url: '/system/fault/equipment/' + equipmentId,
    method: 'get'
  });
}

// 根据故障来源查询故障信息列表
export function getFaultsBySource(faultSource) {
  return request({
    url: '/system/fault/source/' + faultSource,
    method: 'get'
  });
}
