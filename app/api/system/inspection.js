import request from '@/utils/request';

// 查询巡检任务列表
export function listInspection(query, userId) {
  return request({
    url: '/system/inspection/app_list/' + userId,
    method: 'get',
    params: query
  });
}

// 查询巡检任务列表 - stationId
export function listInspectionByStationId(query) {
  return request({
    url: '/system/inspection/app/listByStationId',
    method: 'get',
    params: query
  });
}

// 查询巡检任务列表 - equipmentId
export function listInspectionByEquipmentId(query) {
  return request({
    url: '/system/inspection/app/listByEquipmentId',
    method: 'get',
    params: query
  });
}

// 查询巡检任务详细
export function getInspection(inspectionId) {
  return request({
    url: '/system/inspection/app/' + inspectionId,
    method: 'get'
  });
}

// 获取巡检任务关联的设备列表
export function getInspectionEquipments(inspectionId) {
  return request({
    url: '/system/inspection/equipments/app/' + inspectionId,
    method: 'get'
  });
}

// 获取巡检任务统计信息
export function getInspectionStats(userId) {
  return request({
    url: '/system/inspection/stats',
    method: 'get',
    params: { userId }
  });
}

// 接收巡检任务
export function receiveInspection(data) {
  return request({
    url: '/system/inspection/app_receive',
    method: 'put',
    data: data
  });
}

// 更新设备巡检信息
export function updateEquipmentInspection(data) {
  return request({
    url: '/system/inspection/app_updateEquipmentInspection',
    method: 'post',
    data: data
  });
}

// 提交巡检任务
export function submitInspection(data) {
  return request({
    url: '/system/inspection/app_submit',
    method: 'post',
    data: data
  });
}
