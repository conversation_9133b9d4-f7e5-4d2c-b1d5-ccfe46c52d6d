import request from '@/utils/request';

// 查询清洗任务列表
export function listCleaning(query, userId) {
  return request({
    url: '/system/cleaning/app_list/' + userId,
    method: 'get',
    params: query
  });
}

// 查询清洗任务列表 - stationId
export function listCleaningByStationId(query) {
  return request({
    url: '/system/cleaning/app/listByStationId',
    method: 'get',
    params: query
  });
}

// 查询清洗任务详细
export function getCleaning(cleaningId) {
  return request({
    url: '/system/cleaning/app/' + cleaningId,
    method: 'get'
  });
}

// 获取清洗任务关联的设备列表
export function getCleaningEquipments(cleaningId) {
  return request({
    url: '/system/cleaning/equipments/app/' + cleaningId,
    method: 'get'
  });
}

// 获取清洗任务统计信息
export function getCleaningStats(userId) {
  return request({
    url: '/system/cleaning/stats',
    method: 'get',
    params: { userId }
  });
}

// 接收清洗任务
export function receiveCleaning(data) {
  return request({
    url: '/system/cleaning/app_receive',
    method: 'put',
    data: data
  });
}

// 更新设备清洗信息
export function updateEquipmentCleaning(data) {
  return request({
    url: '/system/cleaning/app_updateEquipmentCleaning',
    method: 'post',
    data: data
  });
}

// 提交清洗任务
export function submitCleaning(data) {
  return request({
    url: '/system/cleaning/app_submit',
    method: 'post',
    data: data
  });
}

// 审核清洗任务
export function reviewCleaning(data) {
  return request({
    url: '/system/cleaning/review',
    method: 'put',
    data: data
  });
}

// 新增清洗任务
export function addCleaning(data) {
  return request({
    url: '/system/cleaning',
    method: 'post',
    data: data
  });
}

// 修改清洗任务
export function updateCleaning(data) {
  return request({
    url: '/system/cleaning',
    method: 'put',
    data: data
  });
}

// 删除清洗任务
export function delCleaning(cleaningIds) {
  return request({
    url: '/system/cleaning/' + cleaningIds,
    method: 'delete'
  });
}

// 导出清洗任务
export function exportCleaning(query) {
  return request({
    url: '/system/cleaning/export',
    method: 'post',
    params: query
  });
}
