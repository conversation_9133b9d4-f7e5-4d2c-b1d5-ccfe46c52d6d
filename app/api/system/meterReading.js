import request from '@/utils/request';

// 查询电量统计任务列表
export function listMeterReading(query, userId) {
  return request({
    url: '/system/meterReading/app_list/' + userId,
    method: 'get',
    params: query
  });
}

// 查询电量统计任务列表 - stationId
export function listMeterReadingByStationId(query) {
  return request({
    url: '/system/meterReading/app/listByStationId',
    method: 'get',
    params: query
  });
}

// 查询电量统计任务详细
export function getMeterReading(meterReadingId) {
  return request({
    url: '/system/meterReading/app/' + meterReadingId,
    method: 'get'
  });
}

// 获取电量统计任务关联的设备列表
export function getMeterReadingEquipments(meterReadingId) {
  return request({
    url: '/system/meterReading/equipments/app/' + meterReadingId,
    method: 'get'
  });
}

// 获取电量统计任务统计信息
export function getMeterReadingStats(userId) {
  return request({
    url: '/system/meterReading/stats',
    method: 'get',
    params: { userId }
  });
}

// 接收电量统计任务
export function receiveMeterReading(data) {
  return request({
    url: '/system/meterReading/app_receive',
    method: 'put',
    data: data
  });
}

// 更新设备电量统计信息
export function updateEquipmentMeterReading(data) {
  return request({
    url: '/system/meterReading/app_updateEquipmentMeterReading',
    method: 'post',
    data: data
  });
}

// 提交电量统计任务
export function submitMeterReading(data) {
  return request({
    url: '/system/meterReading/app_submit',
    method: 'post',
    data: data
  });
}

// 审核电量统计任务
export function reviewMeterReading(data) {
  return request({
    url: '/system/meterReading/review',
    method: 'put',
    data: data
  });
}

// 新增电量统计任务
export function addMeterReading(data) {
  return request({
    url: '/system/meterReading',
    method: 'post',
    data: data
  });
}

// 修改电量统计任务
export function updateMeterReading(data) {
  return request({
    url: '/system/meterReading',
    method: 'put',
    data: data
  });
}

// 删除电量统计任务
export function delMeterReading(meterReadingIds) {
  return request({
    url: '/system/meterReading/' + meterReadingIds,
    method: 'delete'
  });
}

// 导出电量统计任务
export function exportMeterReading(query) {
  return request({
    url: '/system/meterReading/export',
    method: 'post',
    params: query
  });
}

// 获取电站的电表读数统计数据
export function getStationMeterReadingStats(stationId) {
  return request({
    url: '/system/meterReading/station/stats/' + stationId,
    method: 'get'
  });
}
