import request from "@/utils/request";

// 查询设备信息列表
export function listEquipment(query) {
  return request({
    url: "/system/equipment/app_list",
    method: "get",
    params: query,
  });
}

// 根据stationId查询设备信息
export function listEquipmentByStationId(stationId) {
  return request({
    url: "/system/equipment/app_listByStationId/" + stationId,
    method: "get",
  });
}

// 查询设备信息详细
export function getEquipment(equipmentId) {
  return request({
    url: "/system/equipment/app/" + equipmentId,
    method: "get",
  });
}

// 查询设备巡检记录
export function getEquipmentInspectionHistory(equipmentId) {
  return request({
    url: "/system/equipment/app_inspection/" + equipmentId,
    method: "get",
  });
}
