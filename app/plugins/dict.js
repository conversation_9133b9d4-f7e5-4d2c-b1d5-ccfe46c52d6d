/**
 * 字典数据处理插件
 * 用于解析后端返回的字典数据，提供字典标签和值的转换功能
 */

// 字典缓存
let dictCache = {};

/**
 * 设置字典缓存
 * @param {String} key 字典类型
 * @param {Array} value 字典数据
 */
function setDictCache(key, value) {
  if (key && value) {
    dictCache[key] = value;
  }
}

/**
 * 获取字典缓存
 * @param {String} key 字典类型
 */
function getDictCache(key) {
  if (key) {
    return dictCache[key];
  }
  return null;
}

/**
 * 清除字典缓存
 * @param {String} key 字典类型，如果不传则清除所有
 */
function clearDictCache(key) {
  if (key) {
    delete dictCache[key];
  } else {
    dictCache = {};
  }
}

/**
 * 格式化字典数据，将后端返回的字典数据转换为前端可用的格式
 * @param {Array} dictData 字典数据
 */
function formatDictData(dictData) {
  if (!dictData || dictData.length === 0) {
    return [];
  }
  
  return dictData.map(item => ({
    label: item.dictLabel,
    value: item.dictValue,
    elTagType: item.listClass,
    elTagClass: item.cssClass,
    isDefault: item.isDefault === 'Y'
  }));
}

/**
 * 解析字典数据
 * @param {Object} dictDataObj 包含多个字典类型的对象
 */
function parseDictData(dictDataObj) {
  if (!dictDataObj) {
    return {};
  }
  
  const result = {};
  
  // 遍历字典数据对象的所有属性
  Object.keys(dictDataObj).forEach(key => {
    const dictData = dictDataObj[key];
    if (Array.isArray(dictData)) {
      // 格式化字典数据并缓存
      const formattedData = formatDictData(dictData);
      setDictCache(key, formattedData);
      result[key] = formattedData;
    }
  });
  
  return result;
}

/**
 * 根据字典类型和值获取字典标签
 * @param {String} dictType 字典类型
 * @param {String} value 字典值
 * @param {String} defaultLabel 默认标签，当找不到对应标签时返回
 */
function getDictLabel(dictType, value, defaultLabel = '') {
  const dictData = getDictCache(dictType);
  if (!dictData) {
    return defaultLabel;
  }
  
  const dictItem = dictData.find(item => item.value === value);
  if (dictItem) {
    return dictItem.label;
  }
  return defaultLabel;
}

/**
 * 根据字典类型和标签获取字典值
 * @param {String} dictType 字典类型
 * @param {String} label 字典标签
 * @param {String} defaultValue 默认值，当找不到对应值时返回
 */
function getDictValue(dictType, label, defaultValue = '') {
  const dictData = getDictCache(dictType);
  if (!dictData) {
    return defaultValue;
  }
  
  const dictItem = dictData.find(item => item.label === label);
  if (dictItem) {
    return dictItem.value;
  }
  return defaultValue;
}

/**
 * 获取字典数据列表
 * @param {String} dictType 字典类型
 */
function getDictList(dictType) {
  return getDictCache(dictType) || [];
}

export default {
  parseDictData,
  getDictLabel,
  getDictValue,
  getDictList,
  setDictCache,
  clearDictCache
}
