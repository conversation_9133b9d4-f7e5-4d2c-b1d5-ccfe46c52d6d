import config from '@/config'
import storage from '@/utils/storage'
import constant from '@/utils/constant'
import { isHttp, isEmpty } from "@/utils/validate"
import { login, logout, getInfo } from '@/api/login'
import { getToken, setToken, removeToken } from '@/utils/auth'
import defAva from '@/static/images/profile.jpg'

const baseUrl = config.baseUrl

const user = {
  state: {
    token: getToken(),
    name: storage.get(constant.name),
    nickName: storage.get(constant.nickName),
    avatar: storage.get(constant.avatar),
    roles: storage.get(constant.roles),
    permissions: storage.get(constant.permissions),
    userId: storage.get(constant.userId),
    lastUsername: storage.get(constant.lastUsername),
    lastPassword: storage.get(constant.lastPassword)
  },

  mutations: {
    SET_TOKEN: (state, token) => {
      state.token = token
    },
    SET_NAME: (state, name) => {
      state.name = name
      storage.set(constant.name, name)
    },
    SET_NICKNAME: (state, nickName) => {
      state.nickName = nickName
      storage.set(constant.nickName, nickName)
    },
    SET_AVATAR: (state, avatar) => {
      state.avatar = avatar
      storage.set(constant.avatar, avatar)
    },
    SET_ROLES: (state, roles) => {
      state.roles = roles
      storage.set(constant.roles, roles)
    },
    SET_PERMISSIONS: (state, permissions) => {
      state.permissions = permissions
      storage.set(constant.permissions, permissions)
    },
    SET_USERID: (state, userId) => {
      state.userId = userId
      storage.set(constant.userId, userId)
    },
    SET_LAST_USERNAME: (state, username) => {
      state.lastUsername = username
      storage.set(constant.lastUsername, username)
    },
    SET_LAST_PASSWORD: (state, password) => {
      state.lastPassword = password
      storage.set(constant.lastPassword, password)
    }
  },

  actions: {
    // 登录
    Login({ commit }, userInfo) {
      const username = userInfo.username.trim()
      const password = userInfo.password
      const code = userInfo.code
      const uuid = userInfo.uuid
      return new Promise((resolve, reject) => {
        login(username, password, code, uuid).then(res => {
          setToken(res.token)
          commit('SET_TOKEN', res.token)
          // 保存上次登录的账号密码
          commit('SET_LAST_USERNAME', username)
          commit('SET_LAST_PASSWORD', password)
          resolve()
        }).catch(error => {
          reject(error)
        })
      })
    },

    // 获取用户信息
    GetInfo({ commit, state }) {
      return new Promise((resolve, reject) => {
        getInfo().then(res => {
          const user = res.user
		  let avatar = user.avatar || ""
		  if (!isHttp(avatar)) {
            avatar = (isEmpty(avatar)) ? defAva : baseUrl + avatar
          }
          const username = (isEmpty(user) || isEmpty(user.userName)) ? "" : user.userName
          const nickName = (isEmpty(user) || isEmpty(user.nickName)) ? "" : user.nickName
          const userId = (isEmpty(user) || isEmpty(user.userId)) ? "" : user.userId
		  if (res.roles && res.roles.length > 0) {
            commit('SET_ROLES', res.roles)
            commit('SET_PERMISSIONS', res.permissions)
          } else {
            commit('SET_ROLES', ['ROLE_DEFAULT'])
          }
          commit('SET_NAME', username)
          commit('SET_NICKNAME', nickName)
          commit('SET_AVATAR', avatar)
          commit('SET_USERID', userId)
          resolve(res)
        }).catch(error => {
          reject(error)
        })
      })
    },

    // 退出系统
    LogOut({ commit, state }) {
      return new Promise((resolve, reject) => {
        logout(state.token).then(() => {
          // 保存账号密码信息
          const lastUsername = state.lastUsername
          const lastPassword = state.lastPassword

          commit('SET_TOKEN', '')
          commit('SET_ROLES', [])
          commit('SET_PERMISSIONS', [])
          removeToken()
          storage.clean()

          // 重新设置账号密码信息
          commit('SET_LAST_USERNAME', lastUsername)
          commit('SET_LAST_PASSWORD', lastPassword)

          resolve()
        }).catch(error => {
          reject(error)
        })
      })
    }
  }
}

export default user
