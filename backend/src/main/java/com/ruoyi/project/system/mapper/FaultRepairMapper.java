package com.ruoyi.project.system.mapper;

import java.util.List;
import java.util.Map;
import org.apache.ibatis.annotations.Param;
import com.ruoyi.project.system.domain.FaultRepair;

/**
 * 维修任务Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-14
 */
public interface FaultRepairMapper 
{
    /**
     * 查询维修任务
     * 
     * @param repairId 维修任务主键
     * @return 维修任务
     */
    public FaultRepair selectFaultRepairByRepairId(Long repairId);

    /**
     * 查询维修任务列表
     * 
     * @param faultRepair 维修任务
     * @return 维修任务集合
     */
    public List<FaultRepair> selectFaultRepairList(FaultRepair faultRepair);

    /**
     * 新增维修任务
     * 
     * @param faultRepair 维修任务
     * @return 结果
     */
    public int insertFaultRepair(FaultRepair faultRepair);

    /**
     * 修改维修任务
     * 
     * @param faultRepair 维修任务
     * @return 结果
     */
    public int updateFaultRepair(FaultRepair faultRepair);

    /**
     * 删除维修任务
     * 
     * @param repairId 维修任务主键
     * @return 结果
     */
    public int deleteFaultRepairByRepairId(Long repairId);

    /**
     * 批量删除维修任务
     *
     * @param repairIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteFaultRepairByRepairIds(Long[] repairIds);

    /**
     * App查询维修任务列表
     *
     * @param faultRepair 维修任务
     * @param userId 用户ID
     * @return 维修任务集合
     */
    public List<FaultRepair> selectFaultRepairAppList(@Param("faultRepair") FaultRepair faultRepair, @Param("userId") String userId);

    /**
     * 查询维修任务统计
     *
     * @param userId 用户ID
     * @return 统计信息
     */
    public Map<String, Object> selectFaultRepairStats(@Param("userId") String userId);

    /**
     * 根据故障ID查询维修任务列表
     *
     * @param faultId 故障ID
     * @return 维修任务集合
     */
    public List<FaultRepair> selectFaultRepairListByFaultId(Long faultId);

    /**
     * 根据电站ID查询维修任务列表
     *
     * @param stationId 电站ID
     * @return 维修任务集合
     */
    public List<FaultRepair> selectFaultRepairListByStationId(Long stationId);

    /**
     * 查询待接收维修任务数量
     *
     * @param userId 用户ID
     * @return 待接收维修任务数量
     */
    public int selectPendingRepairCount(@Param("userId") String userId);

    /**
     * 查询进行中维修任务数量
     *
     * @param userId 用户ID
     * @return 进行中维修任务数量
     */
    public int selectProcessingRepairCount(@Param("userId") String userId);

    /**
     * 查询已完成维修任务数量
     *
     * @param userId 用户ID
     * @return 已完成维修任务数量
     */
    public int selectCompletedRepairCount(@Param("userId") String userId);

    /**
     * 查询已审核维修任务数量
     *
     * @param userId 用户ID
     * @return 已审核维修任务数量
     */
    public int selectReviewedRepairCount(@Param("userId") String userId);
}
