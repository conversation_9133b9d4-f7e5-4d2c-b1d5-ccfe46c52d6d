package com.ruoyi.project.system.mapper;

import java.util.List;
import java.util.Map;
import com.ruoyi.project.system.domain.Inspection;

/**
 * 巡检任务Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-27
 */
public interface InspectionMapper
{
    /**
     * 查询巡检任务
     *
     * @param inspectionId 巡检任务主键
     * @return 巡检任务
     */
    public Inspection selectInspectionByInspectionId(Long inspectionId);

    /**
     * 查询巡检任务列表
     *
     * @param inspection 巡检任务
     * @return 巡检任务集合
     */
    public List<Inspection> selectInspectionList(Inspection inspection);

    /**
     * 新增巡检任务
     *
     * @param inspection 巡检任务
     * @return 结果
     */
    public int insertInspection(Inspection inspection);

    /**
     * 修改巡检任务
     *
     * @param inspection 巡检任务
     * @return 结果
     */
    public int updateInspection(Inspection inspection);

    /**
     * 删除巡检任务
     *
     * @param inspectionId 巡检任务主键
     * @return 结果
     */
    public int deleteInspectionByInspectionId(Long inspectionId);

    /**
     * 批量删除巡检任务
     *
     * @param inspectionIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteInspectionByInspectionIds(Long[] inspectionIds);

    /**
     * App查询巡检任务列表
     *
     * @param inspection 巡检任务
     * @return 巡检任务集合
     */
    public List<Inspection> selectInspectionAppList(Inspection inspection);

    /**
     * 查询巡检任务统计信息
     *
     * @param userId 用户ID
     * @return 统计信息
     */
    public Map<String, Object> selectInspectionStats(String userId);
}
