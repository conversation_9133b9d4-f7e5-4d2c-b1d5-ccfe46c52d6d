package com.ruoyi.project.system.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.framework.aspectj.lang.annotation.Excel;
import com.ruoyi.framework.web.domain.BaseEntity;

/**
 * 巡检和设备关联对象 inspection_equipment
 *
 * <AUTHOR>
 * @date 2025-04-29
 */
public class InspectionEquipment extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 巡检ID */
    private Long inspectionId;

    /** 设备ID */
    private Long equipmentId;

    /** 巡检人员ID */
    @Excel(name = "巡检人员ID")
    private Long userId;

    /** 巡检内容 */
    @Excel(name = "巡检内容")
    private String inspectionContent;

    /** 巡检时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Excel(name = "巡检时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date inspectionTime;

    /** 检查备注 */
    @Excel(name = "检查备注")
    private String equipmentRemark;

    /** 关联的设备信息 */
    private Equipment equipment;

    /** 关联的巡检任务信息 */
    private Inspection inspection;

    public void setInspectionId(Long inspectionId)
    {
        this.inspectionId = inspectionId;
    }

    public Long getInspectionId()
    {
        return inspectionId;
    }

    public void setEquipmentId(Long equipmentId)
    {
        this.equipmentId = equipmentId;
    }

    public Long getEquipmentId()
    {
        return equipmentId;
    }

    public void setUserId(Long userId)
    {
        this.userId = userId;
    }

    public Long getUserId()
    {
        return userId;
    }

    public void setInspectionContent(String inspectionContent)
    {
        this.inspectionContent = inspectionContent;
    }

    public String getInspectionContent()
    {
        return inspectionContent;
    }

    public void setInspectionTime(Date inspectionTime)
    {
        this.inspectionTime = inspectionTime;
    }

    public Date getInspectionTime()
    {
        return inspectionTime;
    }

    public void setEquipmentRemark(String equipmentRemark)
    {
        this.equipmentRemark = equipmentRemark;
    }

    public String getEquipmentRemark()
    {
        return equipmentRemark;
    }

    public void setEquipment(Equipment equipment)
    {
        this.equipment = equipment;
    }

    public Equipment getEquipment()
    {
        return equipment;
    }

    public void setInspection(Inspection inspection)
    {
        this.inspection = inspection;
    }

    public Inspection getInspection()
    {
        return inspection;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("inspectionId", getInspectionId())
            .append("equipmentId", getEquipmentId())
            .append("userId", getUserId())
            .append("inspectionContent", getInspectionContent())
            .append("inspectionTime", getInspectionTime())
            .append("equipmentRemark", getEquipmentRemark())
            .append("equipment", getEquipment())
            .append("inspection", getInspection())
            .toString();
    }
}
