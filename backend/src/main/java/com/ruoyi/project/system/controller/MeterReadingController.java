package com.ruoyi.project.system.controller;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.project.system.domain.MeterReadingEquipment;
import com.ruoyi.project.system.service.IMeterReadingEquipmentService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.framework.aspectj.lang.annotation.Log;
import com.ruoyi.framework.aspectj.lang.enums.BusinessType;
import com.ruoyi.project.system.domain.MeterReading;
import com.ruoyi.project.system.service.IMeterReadingService;
import com.ruoyi.framework.web.controller.BaseController;
import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.framework.web.page.TableDataInfo;

/**
 * 电量统计任务Controller
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
@RestController
@RequestMapping("/system/meterReading")
public class MeterReadingController extends BaseController {
    private static final Logger logger = LoggerFactory.getLogger(MeterReadingController.class);

    @Autowired
    private IMeterReadingService meterReadingService;

    @Autowired
    private IMeterReadingEquipmentService meterReadingEquipmentService;

    /**
     * 查询电量统计任务列表
     */
    @PreAuthorize("@ss.hasPermi('system:meterReading:list')")
    @GetMapping("/list")
    public TableDataInfo list(MeterReading meterReading) {
        startPage();
        List<MeterReading> list = meterReadingService.selectMeterReadingList(meterReading);
        return getDataTable(list);
    }

    /**
     * 导出电量统计任务列表
     */
    @PreAuthorize("@ss.hasPermi('system:meterReading:export')")
    @Log(title = "电量统计任务", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MeterReading meterReading) {
        List<MeterReading> list = meterReadingService.selectMeterReadingList(meterReading);
        ExcelUtil<MeterReading> util = new ExcelUtil<MeterReading>(MeterReading.class);
        util.exportExcel(response, list, "电量统计任务数据");
    }

    /**
     * 获取电量统计任务详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:meterReading:query')")
    @GetMapping(value = "/{meterReadingId}")
    public AjaxResult getInfo(@PathVariable("meterReadingId") Long meterReadingId) {
        return success(meterReadingService.selectMeterReadingByMeterReadingId(meterReadingId));
    }

    /**
     * 新增电量统计任务
     */
    @PreAuthorize("@ss.hasPermi('system:meterReading:add')")
    @Log(title = "电量统计任务", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody java.util.Map<String, Object> requestMap) {
        try {
            // 从请求中提取meterReading和equipments字段
            MeterReading meterReading = null;
            List<MeterReadingEquipment> equipments = null;

            if (requestMap.containsKey("meterReading")) {
                ObjectMapper objectMapper = new ObjectMapper();
                meterReading = objectMapper.convertValue(requestMap.get("meterReading"), MeterReading.class);
            }

            if (meterReading == null) {
                return AjaxResult.error("缺少电量统计任务信息");
            }

            // 设置默认状态为已下发
            meterReading.setStatus("0");

            // 保存电量统计任务
            int result = meterReadingService.insertMeterReading(meterReading);
            if (result <= 0) {
                return AjaxResult.error("保存电量统计任务失败");
            }

            // 处理设备关联信息
            if (requestMap.containsKey("equipments") && requestMap.get("equipments") != null) {
                try {
                    ObjectMapper objectMapper = new ObjectMapper();
                    List<Map<String, Object>> equipmentMaps = (List<Map<String, Object>>) requestMap.get("equipments");
                    equipments = new ArrayList<>();

                    for (Map<String, Object> equipmentMap : equipmentMaps) {
                        MeterReadingEquipment equipment = new MeterReadingEquipment();
                        equipment.setMeterReadingId(meterReading.getMeterReadingId());
                        equipment.setEquipmentId(Long.valueOf(equipmentMap.get("equipmentId").toString()));

                        equipments.add(equipment);
                    }

                    // 保存设备关联信息
                    meterReadingEquipmentService.batchSaveMeterReadingEquipments(meterReading.getMeterReadingId(), equipments);
                } catch (Exception e) {
                    return AjaxResult.error("处理设备关联信息失败: " + e.getMessage());
                }
            }

            return success(meterReading.getMeterReadingId());
        } catch (Exception e) {
            return AjaxResult.error("操作失败: " + e.getMessage());
        }
    }

    /**
     * 修改电量统计任务
     */
    @PreAuthorize("@ss.hasPermi('system:meterReading:edit')")
    @Log(title = "电量统计任务", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody java.util.Map<String, Object> requestMap) {
        try {
            // 从请求中提取meterReading和equipments字段
            MeterReading meterReading = null;
            List<MeterReadingEquipment> equipments = null;

            if (requestMap.containsKey("meterReading")) {
                ObjectMapper objectMapper = new ObjectMapper();
                meterReading = objectMapper.convertValue(requestMap.get("meterReading"), MeterReading.class);
            }

            if (meterReading == null) {
                return AjaxResult.error("缺少电量统计任务信息");
            }

            // 更新电量统计任务
            int rows = meterReadingService.updateMeterReading(meterReading);

            // 处理设备关联信息
            if (requestMap.containsKey("equipments") && requestMap.get("equipments") != null) {
                try {
                    ObjectMapper objectMapper = new ObjectMapper();
                    List<Map<String, Object>> equipmentMaps = (List<Map<String, Object>>) requestMap.get("equipments");
                    equipments = new ArrayList<>();

                    for (Map<String, Object> equipmentMap : equipmentMaps) {
                        MeterReadingEquipment equipment = new MeterReadingEquipment();
                        equipment.setMeterReadingId(meterReading.getMeterReadingId());
                        equipment.setEquipmentId(Long.valueOf(equipmentMap.get("equipmentId").toString()));

                        equipments.add(equipment);
                    }

                    // 保存设备关联信息
                    meterReadingEquipmentService.batchSaveMeterReadingEquipments(meterReading.getMeterReadingId(), equipments);
                } catch (Exception e) {
                    return AjaxResult.error("处理设备关联信息失败: " + e.getMessage());
                }
            }

            return toAjax(rows);
        } catch (Exception e) {
            return AjaxResult.error("操作失败: " + e.getMessage());
        }
    }

    /**
     * 删除电量统计任务
     */
    @PreAuthorize("@ss.hasPermi('system:meterReading:remove')")
    @Log(title = "电量统计任务", businessType = BusinessType.DELETE)
    @DeleteMapping("/{meterReadingIds}")
    public AjaxResult remove(@PathVariable Long[] meterReadingIds) {
        return toAjax(meterReadingService.deleteMeterReadingByMeterReadingIds(meterReadingIds));
    }

    /**
     * 审核电量统计任务
     */
    @PreAuthorize("@ss.hasPermi('system:meterReading:review')")
    @Log(title = "审核电量统计任务", businessType = BusinessType.UPDATE)
    @PutMapping("/review")
    public AjaxResult reviewMeterReading(@RequestBody MeterReading meterReading) {
        try {
            if (meterReading == null || meterReading.getMeterReadingId() == null) {
                return AjaxResult.error("缺少电量统计任务ID");
            }

            // 设置审核时间为当前时间
            meterReading.setReviewTime(new java.util.Date());

            // 状态由前端传入，支持审核通过(6)和审核不通过
            if (meterReading.getStatus() == null) {
                meterReading.setStatus("6"); // 默认为审核通过
            }

            // 更新电量统计任务信息
            int rows = meterReadingService.updateMeterReading(meterReading);

            return toAjax(rows);
        } catch (Exception e) {
            logger.error("处理审核电量统计任务请求时出错", e);
            return AjaxResult.error("操作失败: " + e.getMessage());
        }
    }

    // ==================== 移动端API接口 ====================

    /**
     * App查询电量统计任务列表
     */
    @GetMapping("/app_list/{userId}")
    public TableDataInfo appList(MeterReading meterReading, @PathVariable String userId) {
        startPage();
        List<MeterReading> list = meterReadingService.selectMeterReadingAppList(meterReading, userId);
        return getDataTable(list);
    }

    /**
     * APP根据电站ID查询电量统计任务列表
     */
    @GetMapping("/app/listByStationId")
    public TableDataInfo appListByStationId(MeterReading meterReading) {
        startPage();
        List<MeterReading> list = meterReadingService.selectMeterReadingList(meterReading);
        return getDataTable(list);
    }

    /**
     * APP查询电量统计任务详细信息
     */
    @GetMapping("/app/{meterReadingId}")
    public AjaxResult appGetInfo(@PathVariable("meterReadingId") Long meterReadingId) {
        return success(meterReadingService.selectMeterReadingByMeterReadingId(meterReadingId));
    }

    /**
     * 获取电量统计任务关联的设备列表
     */
    @PreAuthorize("@ss.hasPermi('system:meterReading:query')")
    @GetMapping("/equipments/{meterReadingId}")
    public AjaxResult getMeterReadingEquipments(@PathVariable Long meterReadingId) {
        return success(meterReadingService.selectMeterReadingEquipmentList(meterReadingId));
    }

    /**
     * 获取电量统计任务关联的设备列表 - APP接口
     */
    @GetMapping("/equipments/app/{meterReadingId}")
    public AjaxResult getMeterReadingEquipmentsApp(@PathVariable Long meterReadingId) {
        return success(meterReadingService.selectMeterReadingEquipmentList(meterReadingId));
    }

    /**
     * 获取电量统计任务统计信息
     */
    @GetMapping("/stats")
    public AjaxResult getMeterReadingStats(String userId) {
        return success(meterReadingService.selectMeterReadingStats(userId));
    }

    /**
     * 更新设备电量统计信息
     */
    @Log(title = "更新设备电量统计信息", businessType = BusinessType.UPDATE)
    @PostMapping("/app_updateEquipmentMeterReading")
    public AjaxResult updateEquipmentMeterReading(@RequestBody java.util.Map<String, Object> requestMap) {
        try {
            return success(meterReadingService.updateEquipmentMeterReading(requestMap));
        } catch (Exception e) {
            logger.error("处理更新设备电量统计信息请求时出错", e);
            return AjaxResult.error("操作失败: " + e.getMessage());
        }
    }

    /**
     * 接收电量统计任务
     */
    @Log(title = "接收电量统计任务", businessType = BusinessType.UPDATE)
    @PutMapping("/app_receive")
    public AjaxResult receiveMeterReading(@RequestBody MeterReading meterReading) {
        try {
            if (meterReading == null || meterReading.getMeterReadingId() == null) {
                return AjaxResult.error("缺少电量统计任务ID");
            }

            // 设置接收时间为当前时间
            meterReading.setReceiveTime(new java.util.Date());
            meterReading.setStatus("2"); // 设置为进行中状态

            // 更新电量统计任务信息
            int rows = meterReadingService.updateMeterReading(meterReading);

            return toAjax(rows);
        } catch (Exception e) {
            logger.error("处理接收电量统计任务请求时出错", e);
            return AjaxResult.error("操作失败: " + e.getMessage());
        }
    }

    /**
     * 提交电量统计任务
     */
    @Log(title = "提交电量统计任务", businessType = BusinessType.UPDATE)
    @PostMapping("/app_submit")
    public AjaxResult submitMeterReading(@RequestBody java.util.Map<String, Object> requestMap) {
        try {
            return success(meterReadingService.submitMeterReading(requestMap));
        } catch (Exception e) {
            logger.error("处理提交电量统计任务请求时出错", e);
            return AjaxResult.error("操作失败: " + e.getMessage());
        }
    }
}
