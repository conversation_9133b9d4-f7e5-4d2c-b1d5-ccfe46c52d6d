package com.ruoyi.project.system.mapper;

import java.util.List;
import java.util.Map;
import org.apache.ibatis.annotations.Param;
import com.ruoyi.project.system.domain.Cleaning;

/**
 * 组件清洗任务Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
public interface CleaningMapper 
{
    /**
     * 查询组件清洗任务
     * 
     * @param cleaningId 组件清洗任务主键
     * @return 组件清洗任务
     */
    public Cleaning selectCleaningByCleaningId(Long cleaningId);

    /**
     * 查询组件清洗任务列表
     * 
     * @param cleaning 组件清洗任务
     * @return 组件清洗任务集合
     */
    public List<Cleaning> selectCleaningList(Cleaning cleaning);

    /**
     * 新增组件清洗任务
     * 
     * @param cleaning 组件清洗任务
     * @return 结果
     */
    public int insertCleaning(Cleaning cleaning);

    /**
     * 修改组件清洗任务
     * 
     * @param cleaning 组件清洗任务
     * @return 结果
     */
    public int updateCleaning(Cleaning cleaning);

    /**
     * 删除组件清洗任务
     * 
     * @param cleaningId 组件清洗任务主键
     * @return 结果
     */
    public int deleteCleaningByCleaningId(Long cleaningId);

    /**
     * 批量删除组件清洗任务
     *
     * @param cleaningIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCleaningByCleaningIds(Long[] cleaningIds);

    /**
     * App查询清洗任务列表
     *
     * @param cleaning 清洗任务
     * @param userId 用户ID
     * @return 清洗任务集合
     */
    public List<Cleaning> selectCleaningAppList(@Param("cleaning") Cleaning cleaning, @Param("userId") String userId);

    /**
     * 查询清洗任务统计信息
     *
     * @param userId 用户ID
     * @return 统计信息
     */
    public Map<String, Object> selectCleaningStats(String userId);
}
