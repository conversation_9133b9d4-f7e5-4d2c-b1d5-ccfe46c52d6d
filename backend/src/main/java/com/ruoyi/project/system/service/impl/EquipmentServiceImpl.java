package com.ruoyi.project.system.service.impl;

import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.bean.BeanValidators;
import com.ruoyi.project.system.domain.Equipment;
import com.ruoyi.project.system.mapper.EquipmentMapper;
import com.ruoyi.project.system.service.IEquipmentService;
import com.ruoyi.project.system.utils.InspectionPresetUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.validation.Validator;
import java.util.List;

/**
 * 设备信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-23
 */
@Service
public class EquipmentServiceImpl implements IEquipmentService {
    @Autowired
    protected Validator validator;
    @Autowired
    private EquipmentMapper equipmentMapper;

    /**
     * 查询设备信息
     *
     * @param equipmentId 设备信息主键
     * @return 设备信息
     */
    @Override
    public Equipment selectEquipmentByEquipmentId(Long equipmentId) {
        return equipmentMapper.selectEquipmentByEquipmentId(equipmentId);
    }

    /**
     * 查询设备信息列表
     *
     * @param equipment 设备信息
     * @return 设备信息
     */
    @Override
    public List<Equipment> selectEquipmentList(Equipment equipment) {
        return equipmentMapper.selectEquipmentList(equipment);
    }

    /**
     * 新增设备信息
     *
     * @param equipment 设备信息
     * @return 结果
     */
    @Override
    public int insertEquipment(Equipment equipment) {
        equipment.setCreateBy(SecurityUtils.getUsername());
        equipment.setCreateTime(DateUtils.getNowDate());

        // 根据设备类型自动生成巡检规则
        if (equipment.getEquipmentType() != null && equipment.getInspectionRule() == null) {
            String inspectionRule = InspectionPresetUtils.generateInspectionRuleJson(equipment.getEquipmentType());
            equipment.setInspectionRule(inspectionRule);
        }

        return equipmentMapper.insertEquipment(equipment);
    }

    /**
     * 修改设备信息
     *
     * @param equipment 设备信息
     * @return 结果
     */
    @Override
    public int updateEquipment(Equipment equipment) {
        equipment.setUpdateBy(SecurityUtils.getUsername());
        equipment.setUpdateTime(DateUtils.getNowDate());
        return equipmentMapper.updateEquipment(equipment);
    }

    /**
     * 批量删除设备信息
     *
     * @param equipmentIds 需要删除的设备信息主键
     * @return 结果
     */
    @Override
    public int deleteEquipmentByEquipmentIds(Long[] equipmentIds) {
        return equipmentMapper.deleteEquipmentByEquipmentIds(equipmentIds);
    }

    /**
     * 删除设备信息信息
     *
     * @param equipmentId 设备信息主键
     * @return 结果
     */
    @Override
    public int deleteEquipmentByEquipmentId(Long equipmentId) {
        return equipmentMapper.deleteEquipmentByEquipmentId(equipmentId);
    }

    /**
     * 导入设备信息数据
     *
     * @param equipmentList   设备信息数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName        操作用户
     * @param stationId       指定的电站ID，如果提供则覆盖Excel中的电站ID
     * @return 结果
     */
    @Override
    public String importEquipment(List<Equipment> equipmentList, Boolean isUpdateSupport, String operName, Long stationId) {
        if (StringUtils.isNull(equipmentList) || equipmentList.isEmpty()) {
            throw new ServiceException("导入设备数据不能为空！");
        }

        // 先进行数据验证，确保所有数据都符合要求
        StringBuilder validationMsg = new StringBuilder();
        int validationFailureNum = 0;

        for (int i = 0; i < equipmentList.size(); i++) {
            Equipment equipment = equipmentList.get(i);
            String rowInfo = "第" + (i + 1) + "行";

            // 验证设备名称不能为空
            if (StringUtils.isEmpty(equipment.getEquipmentName())) {
                validationFailureNum++;
                validationMsg.append("<br/>")
                        .append(validationFailureNum)
                        .append("、").append(rowInfo)
                        .append("：设备名称不能为空");
                continue;
            }

            // 验证设备类型不能为空
            if (StringUtils.isEmpty(equipment.getEquipmentType())) {
                validationFailureNum++;
                validationMsg.append("<br/>")
                        .append(validationFailureNum)
                        .append("、")
                        .append(rowInfo)
                        .append("：设备类型不能为空");
            }
        }

        // 如果有验证失败的数据，直接返回错误信息
        if (validationFailureNum > 0) {
            validationMsg.insert(0, "数据验证失败！共 " + validationFailureNum + " 条数据不符合要求，错误如下：");
            throw new ServiceException(validationMsg.toString());
        }

        // 数据验证通过后，开始导入处理
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();

        for (Equipment equipment : equipmentList) {
            try {
                // 设置电站ID
                equipment.setStationId(stationId);

                // 验证是否存在这个设备（根据所属电站ID和设备名称共同判断）
                Equipment existEquipment = null;
                Equipment queryEquipment = new Equipment();
                queryEquipment.setStationId(equipment.getStationId());
                queryEquipment.setEquipmentName(equipment.getEquipmentName());
                List<Equipment> existList = equipmentMapper.selectEquipmentList(queryEquipment);
                if (existList != null && !existList.isEmpty()) {
                    existEquipment = existList.get(0);
                }

                if (StringUtils.isNull(existEquipment)) {
                    BeanValidators.validateWithException(validator, equipment);
                    equipment.setCreateBy(operName);
                    equipment.setCreateTime(DateUtils.getNowDate());

                    // 根据设备类型自动生成巡检规则
                    if (equipment.getEquipmentType() != null && equipment.getInspectionRule() == null) {
                        String inspectionRule = InspectionPresetUtils.generateInspectionRuleJson(equipment.getEquipmentType());
                        equipment.setInspectionRule(inspectionRule);
                    }

                    this.insertEquipment(equipment);
                    successNum++;
                    successMsg.append("<br/>")
                            .append(successNum)
                            .append("、设备 ")
                            .append(equipment.getEquipmentName())
                            .append(" 导入成功");
                } else if (isUpdateSupport) {
                    BeanValidators.validateWithException(validator, equipment);
                    equipment.setEquipmentId(existEquipment.getEquipmentId());
                    equipment.setUpdateBy(operName);
                    equipment.setUpdateTime(DateUtils.getNowDate());

                    // 根据设备类型自动生成巡检规则
                    if (equipment.getEquipmentType() != null && equipment.getInspectionRule() == null) {
                        String inspectionRule = InspectionPresetUtils.generateInspectionRuleJson(equipment.getEquipmentType());
                        equipment.setInspectionRule(inspectionRule);
                    }

                    this.updateEquipment(equipment);
                    successNum++;
                    successMsg.append("<br/>")
                            .append(successNum)
                            .append("、设备 ")
                            .append(equipment.getEquipmentName())
                            .append(" 更新成功");
                } else {
                    failureNum++;
                    failureMsg.append("<br/>")
                            .append(failureNum)
                            .append("、设备 ")
                            .append(equipment.getEquipmentName())
                            .append(" (电站ID:")
                            .append(equipment.getStationId())
                            .append(") 已存在");
                }
            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、设备 " + equipment.getEquipmentName() + " 导入失败：";
                failureMsg.append(msg).append(e.getMessage());
            }
        }

        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }
}
