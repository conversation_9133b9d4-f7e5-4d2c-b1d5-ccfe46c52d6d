package com.ruoyi.project.system.controller;

import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.utils.StringUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.framework.aspectj.lang.annotation.Log;
import com.ruoyi.framework.aspectj.lang.enums.BusinessType;
import com.ruoyi.project.system.domain.Fault;
import com.ruoyi.project.system.service.IFaultService;
import com.ruoyi.framework.web.controller.BaseController;
import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.framework.web.page.TableDataInfo;

/**
 * 故障信息Controller
 * 
 * <AUTHOR>
 * @date 2025-01-14
 */
@RestController
@RequestMapping("/system/fault")
public class FaultController extends BaseController
{
    @Autowired
    private IFaultService faultService;

    /**
     * 查询故障信息列表
     */
    @PreAuthorize("@ss.hasPermi('system:fault:list')")
    @GetMapping("/list")
    public TableDataInfo list(Fault fault)
    {
        startPage();
        List<Fault> list = faultService.selectFaultList(fault);
        return getDataTable(list);
    }

    /**
     * 导出故障信息列表
     */
    @PreAuthorize("@ss.hasPermi('system:fault:export')")
    @Log(title = "故障信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Fault fault)
    {
        List<Fault> list = faultService.selectFaultList(fault);
        ExcelUtil<Fault> util = new ExcelUtil<Fault>(Fault.class);
        util.exportExcel(response, list, "故障信息数据");
    }

    /**
     * 获取故障信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:fault:query')")
    @GetMapping(value = "/{faultId}")
    public AjaxResult getInfo(@PathVariable("faultId") Long faultId)
    {
        return success(faultService.selectFaultByFaultId(faultId));
    }

    /**
     * 新增故障信息
     */
    @PreAuthorize("@ss.hasPermi('system:fault:add')")
    @Log(title = "故障信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Fault fault)
    {
        return toAjax(faultService.insertFault(fault));
    }

    /**
     * 修改故障信息
     */
    @PreAuthorize("@ss.hasPermi('system:fault:edit')")
    @Log(title = "故障信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Fault fault)
    {
        return toAjax(faultService.updateFault(fault));
    }

    /**
     * 删除故障信息
     */
    @PreAuthorize("@ss.hasPermi('system:fault:remove')")
    @Log(title = "故障信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{faultIds}")
    public AjaxResult remove(@PathVariable Long[] faultIds)
    {
        return toAjax(faultService.deleteFaultByFaultIds(faultIds));
    }

    /**
     * 审核故障信息
     */
    @PreAuthorize("@ss.hasPermi('system:fault:review')")
    @Log(title = "审核故障信息", businessType = BusinessType.UPDATE)
    @PutMapping("/review")
    public AjaxResult reviewFault(@RequestBody Fault fault)
    {
        try {
            if (fault == null || fault.getFaultId() == null) {
                return AjaxResult.error("缺少故障ID");
            }

            // 设置审核时间为当前时间
            fault.setReviewTime(new java.util.Date());

            // 状态由前端传入，支持确认(4)和驳回(2)
            if (fault.getStatus() == null) {
                fault.setStatus("4"); // 默认为已确认
            }

            // 更新故障信息
            int rows = faultService.reviewFault(fault);

            return toAjax(rows);
        } catch (Exception e) {
            logger.error("处理审核故障请求时出错", e);
            return AjaxResult.error("操作失败: " + e.getMessage());
        }
    }

    /**
     * 更新故障状态
     */
    @PreAuthorize("@ss.hasPermi('system:fault:edit')")
    @Log(title = "更新故障状态", businessType = BusinessType.UPDATE)
    @PutMapping("/status")
    public AjaxResult updateStatus(@RequestBody Map<String, Object> requestMap)
    {
        try {
            Long faultId = Long.valueOf(requestMap.get("faultId").toString());
            String status = (String) requestMap.get("status");
            
            int rows = faultService.updateFaultStatus(faultId, status);
            return toAjax(rows);
        } catch (Exception e) {
            logger.error("更新故障状态时出错", e);
            return AjaxResult.error("操作失败: " + e.getMessage());
        }
    }

    // ==================== 移动端API接口 ====================

    /**
     * App查询故障信息列表
     */
    @GetMapping("/app_list")
    public TableDataInfo appList(Fault fault, String discoverUserId)
    {
        // 手动处理discoverUserId参数
        if (StringUtils.isNotEmpty(discoverUserId)) {
            fault.getParams().put("discoverUserId", discoverUserId);
        }

        startPage();
        List<Fault> list = faultService.selectFaultAppList(fault);
        return getDataTable(list);
    }

    /**
     * APP查询故障信息详细信息
     */
    @GetMapping("/app/{faultId}")
    public AjaxResult appGetInfo(@PathVariable("faultId") Long faultId)
    {
        return success(faultService.selectFaultByFaultId(faultId));
    }

    /**
     * 获取故障信息统计
     */
    @GetMapping("/stats")
    public AjaxResult getFaultStats(String userId)
    {
        return success(faultService.selectFaultStats(userId));
    }

    /**
     * 根据电站ID查询故障信息列表
     */
    @GetMapping("/station/{stationId}")
    public TableDataInfo getByStationId(@PathVariable Long stationId)
    {
        startPage();
        List<Fault> list = faultService.selectFaultListByStationId(stationId);
        return getDataTable(list);
    }

    /**
     * 根据设备ID查询故障信息列表
     */
    @GetMapping("/equipment/{equipmentId}")
    public TableDataInfo getByEquipmentId(@PathVariable Long equipmentId)
    {
        startPage();
        List<Fault> list = faultService.selectFaultListByEquipmentId(equipmentId);
        return getDataTable(list);
    }

    /**
     * 根据故障来源查询故障信息列表
     */
    @GetMapping("/source/{faultSource}")
    public TableDataInfo getBySource(@PathVariable String faultSource)
    {
        startPage();
        List<Fault> list = faultService.selectFaultListBySource(faultSource);
        return getDataTable(list);
    }

    /**
     * 故障上报（从巡检/清洗发现）
     */
    @Log(title = "故障上报", businessType = BusinessType.INSERT)
    @PostMapping("/app_report")
    public AjaxResult reportFault(@RequestBody Map<String, Object> requestMap)
    {
        try {
            Map<String, Object> result = faultService.reportFault(requestMap);
            
            if ((Boolean) result.get("success")) {
                return success(result.get("message")).put("faultId", result.get("faultId"));
            } else {
                return AjaxResult.error((String) result.get("message"));
            }
        } catch (Exception e) {
            logger.error("处理故障上报请求时出错", e);
            return AjaxResult.error("操作失败: " + e.getMessage());
        }
    }

    /**
     * 查询待确认故障数量
     */
    @GetMapping("/count/pending")
    public AjaxResult getPendingCount(String userId)
    {
        return success(faultService.selectPendingFaultCount(userId));
    }

    /**
     * 查询已确认故障数量
     */
    @GetMapping("/count/confirmed")
    public AjaxResult getConfirmedCount(String userId)
    {
        return success(faultService.selectProcessingFaultCount(userId));
    }

    /**
     * 查询已完成故障数量
     */
    @GetMapping("/count/completed")
    public AjaxResult getCompletedCount(String userId)
    {
        return success(faultService.selectCompletedFaultCount(userId));
    }
}
