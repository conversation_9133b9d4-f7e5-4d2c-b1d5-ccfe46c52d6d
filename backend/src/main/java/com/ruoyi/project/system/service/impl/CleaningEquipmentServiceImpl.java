package com.ruoyi.project.system.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.project.system.mapper.CleaningEquipmentMapper;
import com.ruoyi.project.system.domain.CleaningEquipment;
import com.ruoyi.project.system.service.ICleaningEquipmentService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 清洗和设备关联Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
@Service
public class CleaningEquipmentServiceImpl implements ICleaningEquipmentService
{
    @Autowired
    private CleaningEquipmentMapper cleaningEquipmentMapper;

    /**
     * 查询清洗和设备关联
     *
     * @param cleaningId 清洗ID
     * @param equipmentId 设备ID
     * @return 清洗和设备关联
     */
    @Override
    public CleaningEquipment selectCleaningEquipmentByIds(Long cleaningId, Long equipmentId)
    {
        return cleaningEquipmentMapper.selectCleaningEquipmentByIds(cleaningId, equipmentId);
    }

    /**
     * 查询清洗任务关联的设备列表
     *
     * @param cleaningId 清洗任务ID
     * @return 清洗和设备关联集合
     */
    @Override
    public List<CleaningEquipment> selectCleaningEquipmentList(Long cleaningId)
    {
        return cleaningEquipmentMapper.selectCleaningEquipmentList(cleaningId);
    }

    /**
     * 新增清洗和设备关联
     *
     * @param cleaningEquipment 清洗和设备关联
     * @return 结果
     */
    @Override
    public int insertCleaningEquipment(CleaningEquipment cleaningEquipment)
    {
        return cleaningEquipmentMapper.insertCleaningEquipment(cleaningEquipment);
    }

    /**
     * 修改清洗和设备关联
     *
     * @param cleaningEquipment 清洗和设备关联
     * @return 结果
     */
    @Override
    public int updateCleaningEquipment(CleaningEquipment cleaningEquipment)
    {
        return cleaningEquipmentMapper.updateCleaningEquipment(cleaningEquipment);
    }

    /**
     * 删除清洗和设备关联
     *
     * @param cleaningId 清洗ID
     * @param equipmentId 设备ID
     * @return 结果
     */
    @Override
    public int deleteCleaningEquipmentByIds(Long cleaningId, Long equipmentId)
    {
        return cleaningEquipmentMapper.deleteCleaningEquipmentByIds(cleaningId, equipmentId);
    }

    /**
     * 批量删除清洗和设备关联
     *
     * @param cleaningId 清洗任务ID
     * @return 结果
     */
    @Override
    public int deleteCleaningEquipmentByCleaningId(Long cleaningId)
    {
        return cleaningEquipmentMapper.deleteCleaningEquipmentByCleaningId(cleaningId);
    }

    /**
     * 批量保存清洗和设备关联
     *
     * @param cleaningId 清洗ID
     * @param equipments 设备列表
     * @return 结果
     */
    @Override
    @Transactional
    public int batchSaveCleaningEquipments(Long cleaningId, List<CleaningEquipment> equipments) {
        // 先删除原有关联
        cleaningEquipmentMapper.deleteCleaningEquipmentByCleaningId(cleaningId);
        
        // 批量插入新关联
        int result = 0;
        for (CleaningEquipment equipment : equipments) {
            equipment.setCleaningId(cleaningId);
            result += cleaningEquipmentMapper.insertCleaningEquipment(equipment);
        }
        return result;
    }
}
