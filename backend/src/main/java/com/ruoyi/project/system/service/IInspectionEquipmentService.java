package com.ruoyi.project.system.service;

import java.util.List;
import com.ruoyi.project.system.domain.InspectionEquipment;

/**
 * 巡检和设备关联Service接口
 * 
 * <AUTHOR>
 * @date 2025-04-29
 */
public interface IInspectionEquipmentService 
{
    /**
     * 查询巡检和设备关联
     * 
     * @param inspectionId 巡检和设备关联主键
     * @return 巡检和设备关联
     */
    public InspectionEquipment selectInspectionEquipmentByInspectionId(Long inspectionId);

    /**
     * 查询巡检和设备关联列表
     * 
     * @param inspectionEquipment 巡检和设备关联
     * @return 巡检和设备关联集合
     */
    public List<InspectionEquipment> selectInspectionEquipmentList(InspectionEquipment inspectionEquipment);

    /**
     * 新增巡检和设备关联
     * 
     * @param inspectionEquipment 巡检和设备关联
     * @return 结果
     */
    public int insertInspectionEquipment(InspectionEquipment inspectionEquipment);

    /**
     * 修改巡检和设备关联
     * 
     * @param inspectionEquipment 巡检和设备关联
     * @return 结果
     */
    public int updateInspectionEquipment(InspectionEquipment inspectionEquipment);

    /**
     * 批量删除巡检和设备关联
     *
     * @param inspectionIds 需要删除的巡检和设备关联主键集合
     */
    public void deleteInspectionEquipmentByInspectionIds(Long[] inspectionIds);

    /**
     * 删除巡检和设备关联信息
     * 
     * @param inspectionId 巡检和设备关联主键
     * @return 结果
     */
    public int deleteInspectionEquipmentByInspectionId(Long inspectionId);
    
    /**
     * 批量保存巡检和设备关联
     * 
     * @param inspectionId 巡检ID
     * @param equipments 设备列表
     * @return 结果
     */
    public int batchSaveInspectionEquipments(Long inspectionId, List<InspectionEquipment> equipments);
}
