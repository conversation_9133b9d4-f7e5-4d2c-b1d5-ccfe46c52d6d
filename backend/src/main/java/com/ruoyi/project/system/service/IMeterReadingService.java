package com.ruoyi.project.system.service;

import java.util.List;
import java.util.Map;

import com.ruoyi.project.system.domain.MeterReading;
import com.ruoyi.project.system.domain.MeterReadingEquipment;

/**
 * 电量统计任务Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */
public interface IMeterReadingService 
{
    /**
     * 查询电量统计任务
     * 
     * @param meterReadingId 电量统计任务主键
     * @return 电量统计任务
     */
    public MeterReading selectMeterReadingByMeterReadingId(Long meterReadingId);

    /**
     * 查询电量统计任务列表
     * 
     * @param meterReading 电量统计任务
     * @return 电量统计任务集合
     */
    public List<MeterReading> selectMeterReadingList(MeterReading meterReading);

    /**
     * 新增电量统计任务
     * 
     * @param meterReading 电量统计任务
     * @return 结果
     */
    public int insertMeterReading(MeterReading meterReading);

    /**
     * 修改电量统计任务
     * 
     * @param meterReading 电量统计任务
     * @return 结果
     */
    public int updateMeterReading(MeterReading meterReading);

    /**
     * 批量删除电量统计任务
     * 
     * @param meterReadingIds 需要删除的电量统计任务主键集合
     * @return 结果
     */
    public int deleteMeterReadingByMeterReadingIds(Long[] meterReadingIds);

    /**
     * 删除电量统计任务信息
     *
     * @param meterReadingId 电量统计任务主键
     * @return 结果
     */
    public int deleteMeterReadingByMeterReadingId(Long meterReadingId);

    /**
     * App查询电量统计任务列表
     *
     * @param meterReading 电量统计任务
     * @param userId 用户ID
     * @return 电量统计任务集合
     */
    public List<MeterReading> selectMeterReadingAppList(MeterReading meterReading, String userId);

    /**
     * 查询电量统计任务统计信息
     *
     * @param userId 用户ID
     * @return 统计信息
     */
    public Map<String, Object> selectMeterReadingStats(String userId);

    /**
     * 查询电量统计任务关联的设备列表
     *
     * @param meterReadingId 电量统计任务ID
     * @return 设备列表
     */
    public List<MeterReadingEquipment> selectMeterReadingEquipmentList(Long meterReadingId);

    /**
     * 更新设备电量统计信息
     *
     * @param requestMap 请求参数
     * @return 结果
     */
    public Map<String, Object> updateEquipmentMeterReading(Map<String, Object> requestMap);

    /**
     * 提交电量统计任务
     *
     * @param requestMap 请求参数
     * @return 结果
     */
    public Map<String, Object> submitMeterReading(Map<String, Object> requestMap);
}
