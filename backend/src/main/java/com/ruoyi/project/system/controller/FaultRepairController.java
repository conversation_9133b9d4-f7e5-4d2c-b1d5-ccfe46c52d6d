package com.ruoyi.project.system.controller;

import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.framework.aspectj.lang.annotation.Log;
import com.ruoyi.framework.aspectj.lang.enums.BusinessType;
import com.ruoyi.project.system.domain.FaultRepair;
import com.ruoyi.project.system.service.IFaultRepairService;
import com.ruoyi.framework.web.controller.BaseController;
import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.framework.web.page.TableDataInfo;

/**
 * 维修任务Controller
 * 
 * <AUTHOR>
 * @date 2025-01-14
 */
@RestController
@RequestMapping("/system/faultrepair")
public class FaultRepairController extends BaseController
{
    @Autowired
    private IFaultRepairService faultRepairService;

    /**
     * 查询维修任务列表
     */
    @PreAuthorize("@ss.hasPermi('system:faultrepair:list')")
    @GetMapping("/list")
    public TableDataInfo list(FaultRepair faultRepair)
    {
        startPage();
        List<FaultRepair> list = faultRepairService.selectFaultRepairList(faultRepair);
        return getDataTable(list);
    }

    /**
     * 导出维修任务列表
     */
    @PreAuthorize("@ss.hasPermi('system:faultrepair:export')")
    @Log(title = "维修任务", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, FaultRepair faultRepair)
    {
        List<FaultRepair> list = faultRepairService.selectFaultRepairList(faultRepair);
        ExcelUtil<FaultRepair> util = new ExcelUtil<FaultRepair>(FaultRepair.class);
        util.exportExcel(response, list, "维修任务数据");
    }

    /**
     * 获取维修任务详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:faultrepair:query')")
    @GetMapping(value = "/{repairId}")
    public AjaxResult getInfo(@PathVariable("repairId") Long repairId)
    {
        return success(faultRepairService.selectFaultRepairByRepairId(repairId));
    }

    /**
     * 新增维修任务
     */
    @PreAuthorize("@ss.hasPermi('system:faultrepair:add')")
    @Log(title = "维修任务", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody FaultRepair faultRepair)
    {
        return toAjax(faultRepairService.insertFaultRepair(faultRepair));
    }

    /**
     * 修改维修任务
     */
    @PreAuthorize("@ss.hasPermi('system:faultrepair:edit')")
    @Log(title = "维修任务", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody FaultRepair faultRepair)
    {
        return toAjax(faultRepairService.updateFaultRepair(faultRepair));
    }

    /**
     * 删除维修任务
     */
    @PreAuthorize("@ss.hasPermi('system:faultrepair:remove')")
    @Log(title = "维修任务", businessType = BusinessType.DELETE)
    @DeleteMapping("/{repairIds}")
    public AjaxResult remove(@PathVariable Long[] repairIds)
    {
        return toAjax(faultRepairService.deleteFaultRepairByRepairIds(repairIds));
    }

    /**
     * 审核维修任务
     */
    @PreAuthorize("@ss.hasPermi('system:faultrepair:review')")
    @Log(title = "审核维修任务", businessType = BusinessType.UPDATE)
    @PutMapping("/review")
    public AjaxResult reviewFaultRepair(@RequestBody FaultRepair faultRepair)
    {
        try {
            if (faultRepair == null || faultRepair.getRepairId() == null) {
                return AjaxResult.error("缺少维修任务ID");
            }

            // 设置审核时间为当前时间
            faultRepair.setReviewTime(new java.util.Date());

            // 状态由前端传入，支持审核通过(6)和审核不通过
            if (faultRepair.getStatus() == null) {
                faultRepair.setStatus("6"); // 默认为已审核
            }

            // 更新维修任务信息
            int rows = faultRepairService.reviewFaultRepair(faultRepair);

            return toAjax(rows);
        } catch (Exception e) {
            logger.error("处理审核维修任务请求时出错", e);
            return AjaxResult.error("操作失败: " + e.getMessage());
        }
    }

    /**
     * 更新维修任务状态
     */
    @PreAuthorize("@ss.hasPermi('system:faultrepair:edit')")
    @Log(title = "更新维修任务状态", businessType = BusinessType.UPDATE)
    @PutMapping("/status")
    public AjaxResult updateStatus(@RequestBody Map<String, Object> requestMap)
    {
        try {
            Long repairId = Long.valueOf(requestMap.get("repairId").toString());
            String status = (String) requestMap.get("status");
            
            int rows = faultRepairService.updateFaultRepairStatus(repairId, status);
            return toAjax(rows);
        } catch (Exception e) {
            logger.error("更新维修任务状态时出错", e);
            return AjaxResult.error("操作失败: " + e.getMessage());
        }
    }

    /**
     * 创建维修任务（从故障创建）
     */
    @PreAuthorize("@ss.hasPermi('system:faultrepair:add')")
    @Log(title = "创建维修任务", businessType = BusinessType.INSERT)
    @PostMapping("/create")
    public AjaxResult createFromFault(@RequestBody Map<String, Object> requestMap)
    {
        try {
            Map<String, Object> result = faultRepairService.createRepairFromFault(requestMap);
            
            if ((Boolean) result.get("success")) {
                return success(result.get("message")).put("repairId", result.get("repairId"));
            } else {
                return AjaxResult.error((String) result.get("message"));
            }
        } catch (Exception e) {
            logger.error("处理创建维修任务请求时出错", e);
            return AjaxResult.error("操作失败: " + e.getMessage());
        }
    }

    // ==================== 移动端API接口 ====================

    /**
     * App查询维修任务列表
     */
    @GetMapping("/app_list/{userId}")
    public TableDataInfo appList(FaultRepair faultRepair, @PathVariable String userId)
    {
        startPage();
        List<FaultRepair> list = faultRepairService.selectFaultRepairAppList(faultRepair, userId);
        return getDataTable(list);
    }

    /**
     * APP查询维修任务详细信息
     */
    @GetMapping("/app/{repairId}")
    public AjaxResult appGetInfo(@PathVariable("repairId") Long repairId)
    {
        return success(faultRepairService.selectFaultRepairByRepairId(repairId));
    }

    /**
     * 获取维修任务统计信息
     */
    @GetMapping("/stats")
    public AjaxResult getFaultRepairStats(String userId)
    {
        return success(faultRepairService.selectFaultRepairStats(userId));
    }

    /**
     * 根据故障ID查询维修任务列表
     */
    @GetMapping("/fault/{faultId}")
    public TableDataInfo getByFaultId(@PathVariable Long faultId)
    {
        startPage();
        List<FaultRepair> list = faultRepairService.selectFaultRepairListByFaultId(faultId);
        return getDataTable(list);
    }

    /**
     * 根据电站ID查询维修任务列表
     */
    @GetMapping("/station/{stationId}")
    public TableDataInfo getByStationId(@PathVariable Long stationId)
    {
        startPage();
        List<FaultRepair> list = faultRepairService.selectFaultRepairListByStationId(stationId);
        return getDataTable(list);
    }

    /**
     * 接收维修任务
     */
    @Log(title = "接收维修任务", businessType = BusinessType.UPDATE)
    @PutMapping("/app_receive")
    public AjaxResult receiveFaultRepair(@RequestBody FaultRepair faultRepair)
    {
        try {
            if (faultRepair == null || faultRepair.getRepairId() == null) {
                return AjaxResult.error("缺少维修任务ID");
            }

            // 接收维修任务
            int rows = faultRepairService.receiveFaultRepair(faultRepair);

            return toAjax(rows);
        } catch (Exception e) {
            logger.error("处理接收维修任务请求时出错", e);
            return AjaxResult.error("操作失败: " + e.getMessage());
        }
    }

    /**
     * 更新维修任务内容
     */
    @Log(title = "更新维修任务内容", businessType = BusinessType.UPDATE)
    @PutMapping("/app_update")
    public AjaxResult updateFaultRepair(@RequestBody FaultRepair faultRepair)
    {
        try {
            if (faultRepair == null || faultRepair.getRepairId() == null) {
                return AjaxResult.error("缺少维修任务ID");
            }

            // 更新维修任务信息
            int rows = faultRepairService.updateFaultRepair(faultRepair);

            return toAjax(rows);
        } catch (Exception e) {
            logger.error("处理更新维修任务请求时出错", e);
            return AjaxResult.error("操作失败: " + e.getMessage());
        }
    }

    /**
     * 提交维修任务
     */
    @Log(title = "提交维修任务", businessType = BusinessType.UPDATE)
    @PutMapping("/app_submit")
    public AjaxResult submitFaultRepair(@RequestBody Map<String, Object> requestMap)
    {
        try {
            Map<String, Object> result = faultRepairService.submitFaultRepair(requestMap);

            if ((Boolean) result.get("success")) {
                return success(result.get("message"));
            } else {
                return AjaxResult.error((String) result.get("message"));
            }
        } catch (Exception e) {
            logger.error("处理提交维修任务请求时出错", e);
            return AjaxResult.error("操作失败: " + e.getMessage());
        }
    }

    /**
     * 查询待接收维修任务数量
     */
    @GetMapping("/count/pending")
    public AjaxResult getPendingCount(String userId)
    {
        return success(faultRepairService.selectPendingRepairCount(userId));
    }

    /**
     * 查询进行中维修任务数量
     */
    @GetMapping("/count/processing")
    public AjaxResult getProcessingCount(String userId)
    {
        return success(faultRepairService.selectProcessingRepairCount(userId));
    }

    /**
     * 查询已完成维修任务数量
     */
    @GetMapping("/count/completed")
    public AjaxResult getCompletedCount(String userId)
    {
        return success(faultRepairService.selectCompletedRepairCount(userId));
    }

    /**
     * 查询已审核维修任务数量
     */
    @GetMapping("/count/reviewed")
    public AjaxResult getReviewedCount(String userId)
    {
        return success(faultRepairService.selectReviewedRepairCount(userId));
    }
}
