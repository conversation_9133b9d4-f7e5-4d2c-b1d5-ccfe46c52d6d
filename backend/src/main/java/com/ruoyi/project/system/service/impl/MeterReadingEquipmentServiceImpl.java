package com.ruoyi.project.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.project.system.mapper.MeterReadingEquipmentMapper;
import com.ruoyi.project.system.domain.MeterReadingEquipment;
import com.ruoyi.project.system.service.IMeterReadingEquipmentService;

/**
 * 电量统计和设备关联Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
@Service
public class MeterReadingEquipmentServiceImpl implements IMeterReadingEquipmentService
{
    @Autowired
    private MeterReadingEquipmentMapper meterReadingEquipmentMapper;

    /**
     * 查询电量统计和设备关联
     *
     * @param meterReadingId 电量统计ID
     * @param equipmentId 设备ID
     * @return 电量统计和设备关联
     */
    @Override
    public MeterReadingEquipment selectMeterReadingEquipmentByIds(Long meterReadingId, Long equipmentId)
    {
        return meterReadingEquipmentMapper.selectMeterReadingEquipmentByIds(meterReadingId, equipmentId);
    }

    /**
     * 查询电量统计任务关联的设备列表
     *
     * @param meterReadingId 电量统计任务ID
     * @return 电量统计和设备关联集合
     */
    @Override
    public List<MeterReadingEquipment> selectMeterReadingEquipmentList(Long meterReadingId)
    {
        return meterReadingEquipmentMapper.selectMeterReadingEquipmentList(meterReadingId);
    }

    /**
     * 新增电量统计和设备关联
     *
     * @param meterReadingEquipment 电量统计和设备关联
     * @return 结果
     */
    @Override
    public int insertMeterReadingEquipment(MeterReadingEquipment meterReadingEquipment)
    {
        return meterReadingEquipmentMapper.insertMeterReadingEquipment(meterReadingEquipment);
    }

    /**
     * 修改电量统计和设备关联
     *
     * @param meterReadingEquipment 电量统计和设备关联
     * @return 结果
     */
    @Override
    public int updateMeterReadingEquipment(MeterReadingEquipment meterReadingEquipment)
    {
        return meterReadingEquipmentMapper.updateMeterReadingEquipment(meterReadingEquipment);
    }

    /**
     * 删除电量统计和设备关联
     *
     * @param meterReadingId 电量统计ID
     * @param equipmentId 设备ID
     * @return 结果
     */
    @Override
    public int deleteMeterReadingEquipmentByIds(Long meterReadingId, Long equipmentId)
    {
        return meterReadingEquipmentMapper.deleteMeterReadingEquipmentByIds(meterReadingId, equipmentId);
    }

    /**
     * 批量删除电量统计和设备关联
     *
     * @param meterReadingId 电量统计任务ID
     * @return 结果
     */
    @Override
    public int deleteMeterReadingEquipmentByMeterReadingId(Long meterReadingId)
    {
        return meterReadingEquipmentMapper.deleteMeterReadingEquipmentByMeterReadingId(meterReadingId);
    }

    /**
     * 批量保存电量统计和设备关联
     *
     * @param meterReadingId 电量统计ID
     * @param equipments 设备列表
     * @return 结果
     */
    @Override
    @Transactional
    public int batchSaveMeterReadingEquipments(Long meterReadingId, List<MeterReadingEquipment> equipments) {
        // 先删除原有关联
        meterReadingEquipmentMapper.deleteMeterReadingEquipmentByMeterReadingId(meterReadingId);
        
        // 批量插入新关联
        int result = 0;
        for (MeterReadingEquipment equipment : equipments) {
            equipment.setMeterReadingId(meterReadingId);
            result += meterReadingEquipmentMapper.insertMeterReadingEquipment(equipment);
        }
        return result;
    }
}
