package com.ruoyi.project.system.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.framework.aspectj.lang.annotation.Excel;
import com.ruoyi.framework.web.domain.BaseEntity;

/**
 * 电量统计任务对象 meter_reading
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
public class MeterReading extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 电量统计任务ID
     */
    private Long meterReadingId;

    /**
     * 电量统计任务名称
     */
    @Excel(name = "电量统计任务名称")
    private String meterReadingName;

    /**
     * 电站ID
     */
    private Long stationId;

    /**
     * 计划开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "计划开始时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date planStartTime;

    /**
     * 计划结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "计划结束时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date planEndTime;

    /**
     * 设定统计人员
     */
    private String assignUser;

    /**
     * 设定负责人
     */
    private String assignChargeUser;

    /**
     * 设定统计人员名称
     */
    @Excel(name = "设定统计人员名称")
    private String assignUserName;

    /**
     * 设定负责人名称
     */
    @Excel(name = "设定负责人名称")
    private String assignChargeUserName;

    /**
     * 实际统计人员
     */
    private String meterReadingUser;

    /**
     * 实际统计人员名称
     */
    @Excel(name = "实际统计人员名称")
    private String meterReadingUserName;

    /**
     * 接收时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Excel(name = "接收时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date receiveTime;

    /**
     * 提交时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Excel(name = "提交时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date submitTime;

    /**
     * 审核时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Excel(name = "审核时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date reviewTime;

    /**
     * 审核备注
     */
    @Excel(name = "审核备注")
    private String reviewRemark;

    /**
     * 状态 0已下发 2已接收 4已提交 6已审核
     */
    @Excel(name = "状态", dictType = "meter_reading_status")
    private String status;

    /**
     * 删除标志 0存在 2删除
     */
    private String delFlag;
    
    /**
     * 关联的电站信息
     */
    private Station station;
    
    /**
     * 关联的用户信息
     */
    private SysUser user;

    public Long getMeterReadingId() {
        return meterReadingId;
    }

    public void setMeterReadingId(Long meterReadingId) {
        this.meterReadingId = meterReadingId;
    }

    public String getMeterReadingName() {
        return meterReadingName;
    }

    public void setMeterReadingName(String meterReadingName) {
        this.meterReadingName = meterReadingName;
    }

    public Long getStationId() {
        return stationId;
    }

    public void setStationId(Long stationId) {
        this.stationId = stationId;
    }

    public Date getPlanStartTime() {
        return planStartTime;
    }

    public void setPlanStartTime(Date planStartTime) {
        this.planStartTime = planStartTime;
    }

    public Date getPlanEndTime() {
        return planEndTime;
    }

    public void setPlanEndTime(Date planEndTime) {
        this.planEndTime = planEndTime;
    }

    public String getAssignUser() {
        return assignUser;
    }

    public void setAssignUser(String assignUser) {
        this.assignUser = assignUser;
    }

    public String getAssignChargeUser() {
        return assignChargeUser;
    }

    public void setAssignChargeUser(String assignChargeUser) {
        this.assignChargeUser = assignChargeUser;
    }

    public String getAssignUserName() {
        return assignUserName;
    }

    public void setAssignUserName(String assignUserName) {
        this.assignUserName = assignUserName;
    }

    public String getAssignChargeUserName() {
        return assignChargeUserName;
    }

    public void setAssignChargeUserName(String assignChargeUserName) {
        this.assignChargeUserName = assignChargeUserName;
    }

    public String getMeterReadingUser() {
        return meterReadingUser;
    }

    public void setMeterReadingUser(String meterReadingUser) {
        this.meterReadingUser = meterReadingUser;
    }

    public String getMeterReadingUserName() {
        return meterReadingUserName;
    }

    public void setMeterReadingUserName(String meterReadingUserName) {
        this.meterReadingUserName = meterReadingUserName;
    }

    public Date getReceiveTime() {
        return receiveTime;
    }

    public void setReceiveTime(Date receiveTime) {
        this.receiveTime = receiveTime;
    }

    public Date getSubmitTime() {
        return submitTime;
    }

    public void setSubmitTime(Date submitTime) {
        this.submitTime = submitTime;
    }

    public Date getReviewTime() {
        return reviewTime;
    }

    public void setReviewTime(Date reviewTime) {
        this.reviewTime = reviewTime;
    }

    public String getReviewRemark() {
        return reviewRemark;
    }

    public void setReviewRemark(String reviewRemark) {
        this.reviewRemark = reviewRemark;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    public Station getStation() {
        return station;
    }

    public void setStation(Station station) {
        this.station = station;
    }

    public SysUser getUser() {
        return user;
    }

    public void setUser(SysUser user) {
        this.user = user;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("meterReadingId", getMeterReadingId())
                .append("meterReadingName", getMeterReadingName())
                .append("stationId", getStationId())
                .append("planStartTime", getPlanStartTime())
                .append("planEndTime", getPlanEndTime())
                .append("assignUser", getAssignUser())
                .append("assignChargeUser", getAssignChargeUser())
                .append("assignUserName", getAssignUserName())
                .append("assignChargeUserName", getAssignChargeUserName())
                .append("meterReadingUser", getMeterReadingUser())
                .append("meterReadingUserName", getMeterReadingUserName())
                .append("receiveTime", getReceiveTime())
                .append("submitTime", getSubmitTime())
                .append("reviewTime", getReviewTime())
                .append("reviewRemark", getReviewRemark())
                .append("status", getStatus())
                .append("delFlag", getDelFlag())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("remark", getRemark())
                .toString();
    }
}
