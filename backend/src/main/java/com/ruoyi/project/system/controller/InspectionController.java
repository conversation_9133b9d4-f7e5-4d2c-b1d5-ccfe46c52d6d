package com.ruoyi.project.system.controller;

import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.framework.aspectj.lang.annotation.Log;
import com.ruoyi.framework.aspectj.lang.enums.BusinessType;
import com.ruoyi.framework.web.controller.BaseController;
import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.framework.web.page.TableDataInfo;
import com.ruoyi.project.system.domain.Inspection;
import com.ruoyi.project.system.domain.InspectionEquipment;
import com.ruoyi.project.system.service.IInspectionEquipmentService;
import com.ruoyi.project.system.service.IInspectionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

/**
 * 巡检任务Controller
 *
 * <AUTHOR>
 * @date 2025-04-27
 */
@RestController
@RequestMapping("/system/inspection")
public class InspectionController extends BaseController {
    @Autowired
    private IInspectionService inspectionService;

    @Autowired
    private IInspectionEquipmentService inspectionEquipmentService;

    /**
     * 查询巡检任务列表
     */
    @PreAuthorize("@ss.hasPermi('system:inspection:list')")
    @GetMapping("/list")
    public TableDataInfo list(Inspection inspection) {
        startPage();
        List<Inspection> list = inspectionService.selectInspectionList(inspection);
        return getDataTable(list);
    }

    /**
     * 导出巡检任务列表
     */
    @PreAuthorize("@ss.hasPermi('system:inspection:export')")
    @Log(title = "巡检任务", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Inspection inspection) {
        List<Inspection> list = inspectionService.selectInspectionList(inspection);
        ExcelUtil<Inspection> util = new ExcelUtil<Inspection>(Inspection.class);
        util.exportExcel(response, list, "巡检任务数据");
    }

    /**
     * 获取巡检任务详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:inspection:query')")
    @GetMapping(value = "/{inspectionId}")
    public AjaxResult getInfo(@PathVariable("inspectionId") Long inspectionId) {
        return success(inspectionService.selectInspectionByInspectionId(inspectionId));
    }

    /**
     * 新增巡检任务
     */
    @PreAuthorize("@ss.hasPermi('system:inspection:add')")
    @Log(title = "巡检任务", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody java.util.Map<String, Object> requestMap) {
        try {
            // 从请求中提取inspection和equipments字段
            Inspection inspection = null;
            List<InspectionEquipment> equipments = null;

            // 处理inspection字段
            if (requestMap.containsKey("inspection")) {
                // 使用Jackson将Map转换为Inspection对象
                com.fasterxml.jackson.databind.ObjectMapper mapper = new com.fasterxml.jackson.databind.ObjectMapper();
                inspection = mapper.convertValue(requestMap.get("inspection"), Inspection.class);
            } else {
                // 如果没有inspection字段，尝试直接将整个请求转换为Inspection
                inspection = new com.fasterxml.jackson.databind.ObjectMapper().convertValue(requestMap, Inspection.class);
            }

            // 先新增巡检任务信息
            int rows = inspectionService.insertInspection(inspection);

            Long inspectionId = inspection.getInspectionId();

            // 如果有equipments字段，则处理设备关联信息
            if (requestMap.containsKey("equipments") && inspection.getInspectionId() != null) {
                try {
                    com.fasterxml.jackson.databind.ObjectMapper mapper = new com.fasterxml.jackson.databind.ObjectMapper();
                    com.fasterxml.jackson.core.type.TypeReference<List<java.util.Map<String, Object>>> typeRef =
                            new com.fasterxml.jackson.core.type.TypeReference<List<java.util.Map<String, Object>>>() {
                            };

                    List<java.util.Map<String, Object>> equipmentMaps = mapper.convertValue(requestMap.get("equipments"), typeRef);
                    equipments = new ArrayList<>();

                    for (java.util.Map<String, Object> equipMap : equipmentMaps) {
                        InspectionEquipment equipment = new InspectionEquipment();
                        equipment.setInspectionId(inspection.getInspectionId());

                        if (equipMap.containsKey("equipmentId")) {
                            if (equipMap.get("equipmentId") instanceof Integer) {
                                equipment.setEquipmentId(((Integer) equipMap.get("equipmentId")).longValue());
                            } else if (equipMap.get("equipmentId") instanceof Long) {
                                equipment.setEquipmentId((Long) equipMap.get("equipmentId"));
                            } else if (equipMap.get("equipmentId") instanceof String) {
                                equipment.setEquipmentId(Long.parseLong((String) equipMap.get("equipmentId")));
                            }
                        }

                        if (equipMap.containsKey("inspectionContent")) {
                            equipment.setInspectionContent((String) equipMap.get("inspectionContent"));
                        }

                        equipments.add(equipment);
                    }

                    // 保存设备关联信息
                    inspectionEquipmentService.batchSaveInspectionEquipments(inspection.getInspectionId(), equipments);
                } catch (Exception e) {
                    return AjaxResult.error("处理设备关联信息失败: " + e.getMessage());
                }
            }

            return success(inspectionId);
        } catch (Exception e) {
            return AjaxResult.error("操作失败: " + e.getMessage());
        }
    }

    /**
     * 修改巡检任务
     */
    @PreAuthorize("@ss.hasPermi('system:inspection:edit')")
    @Log(title = "巡检任务", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody java.util.Map<String, Object> requestMap) {
        try {

            // 从请求中提取inspection和equipments字段
            Inspection inspection = null;
            List<InspectionEquipment> equipments = null;

            // 处理inspection字段
            if (requestMap.containsKey("inspection")) {
                // 使用Jackson将Map转换为Inspection对象
                com.fasterxml.jackson.databind.ObjectMapper mapper = new com.fasterxml.jackson.databind.ObjectMapper();
                inspection = mapper.convertValue(requestMap.get("inspection"), Inspection.class);
            } else {
                // 如果没有inspection字段，尝试直接将整个请求转换为Inspection
                inspection = new com.fasterxml.jackson.databind.ObjectMapper().convertValue(requestMap, Inspection.class);
            }

            if (inspection == null || inspection.getInspectionId() == null) {
                return AjaxResult.error("缺少巡检任务ID");
            }

            // 先更新巡检任务信息
            int rows = inspectionService.updateInspection(inspection);

            // 如果有equipments字段，则处理设备关联信息
            if (requestMap.containsKey("equipments")) {
                try {
                    com.fasterxml.jackson.databind.ObjectMapper mapper = new com.fasterxml.jackson.databind.ObjectMapper();
                    com.fasterxml.jackson.core.type.TypeReference<List<java.util.Map<String, Object>>> typeRef =
                            new com.fasterxml.jackson.core.type.TypeReference<List<java.util.Map<String, Object>>>() {
                            };

                    List<java.util.Map<String, Object>> equipmentMaps = mapper.convertValue(requestMap.get("equipments"), typeRef);
                    equipments = new ArrayList<>();

                    for (java.util.Map<String, Object> equipMap : equipmentMaps) {
                        InspectionEquipment equipment = new InspectionEquipment();
                        equipment.setInspectionId(inspection.getInspectionId());

                        if (equipMap.containsKey("equipmentId")) {
                            if (equipMap.get("equipmentId") instanceof Integer) {
                                equipment.setEquipmentId(((Integer) equipMap.get("equipmentId")).longValue());
                            } else if (equipMap.get("equipmentId") instanceof Long) {
                                equipment.setEquipmentId((Long) equipMap.get("equipmentId"));
                            } else if (equipMap.get("equipmentId") instanceof String) {
                                equipment.setEquipmentId(Long.parseLong((String) equipMap.get("equipmentId")));
                            }
                        }

                        if (equipMap.containsKey("inspectionContent")) {
                            equipment.setInspectionContent((String) equipMap.get("inspectionContent"));
                        }

                        equipments.add(equipment);
                    }

                    // 保存设备关联信息
                    inspectionEquipmentService.batchSaveInspectionEquipments(inspection.getInspectionId(), equipments);
                } catch (Exception e) {
                    return AjaxResult.error("处理设备关联信息失败: " + e.getMessage());
                }
            }

            return toAjax(rows);
        } catch (Exception e) {
            return AjaxResult.error("操作失败: " + e.getMessage());
        }
    }

    /**
     * 删除巡检任务
     */
    @PreAuthorize("@ss.hasPermi('system:inspection:remove')")
    @Log(title = "巡检任务", businessType = BusinessType.DELETE)
    @DeleteMapping("/{inspectionIds}")
    public AjaxResult remove(@PathVariable Long[] inspectionIds) {
        int rows = inspectionService.deleteInspectionByInspectionIds(inspectionIds);
        if (rows > 0) {
            // 删除关联的设备信息
            inspectionEquipmentService.deleteInspectionEquipmentByInspectionIds(inspectionIds);
        }
        return toAjax(rows);
    }

    /**
     * 获取巡检任务关联的设备列表
     */
    @PreAuthorize("@ss.hasPermi('system:inspection:query')")
    @GetMapping("/equipments/{inspectionId}")
    public AjaxResult getInspectionEquipments(@PathVariable("inspectionId") Long inspectionId) {
        InspectionEquipment inspectionEquipment = new InspectionEquipment();
        inspectionEquipment.setInspectionId(inspectionId);
        List<InspectionEquipment> list = inspectionEquipmentService.selectInspectionEquipmentList(inspectionEquipment);
        return success(list);
    }

    /**
     * 保存巡检任务关联的设备信息
     */
    @PreAuthorize("@ss.hasPermi('system:inspection:edit')")
    @Log(title = "巡检任务设备", businessType = BusinessType.UPDATE)
    @PostMapping("/equipments/{inspectionId}")
    public AjaxResult saveInspectionEquipments(@PathVariable("inspectionId") Long inspectionId, @RequestBody List<InspectionEquipment> equipments) {
        return toAjax(inspectionEquipmentService.batchSaveInspectionEquipments(inspectionId, equipments));
    }

    /**
     * 审核巡检任务
     */
    @PreAuthorize("@ss.hasPermi('system:inspection:review')")
    @Log(title = "审核巡检任务", businessType = BusinessType.UPDATE)
    @PutMapping("/review")
    public AjaxResult reviewInspection(@RequestBody Inspection inspection) {
        try {
            if (inspection == null || inspection.getInspectionId() == null) {
                return AjaxResult.error("缺少巡检任务ID");
            }

            // 设置审核时间为当前时间
            inspection.setReviewTime(new java.util.Date());

            // 设置状态为已审核
            inspection.setStatus("6");

            // 更新巡检任务信息
            int rows = inspectionService.updateInspection(inspection);

            return toAjax(rows);
        } catch (Exception e) {
            return AjaxResult.error("操作失败: " + e.getMessage());
        }
    }

    // ============================ APP接口 ============================

    /**
     * APP查询巡检任务列表
     */
    @GetMapping("/app_list/{userId}")
    public TableDataInfo appList(Inspection inspection, @PathVariable String userId) {
        startPage();
        List<Inspection> list = inspectionService.selectInspectionAppList(inspection, userId);
        return getDataTable(list);
    }

    /**
     * APP根据电站ID查询巡检任务列表
     */
    @GetMapping("/app/listByStationId")
    public TableDataInfo appListByStationId(Inspection inspection) {
        startPage();
        List<Inspection> list = inspectionService.selectInspectionList(inspection);
        return getDataTable(list);
    }

    /**
     * APP获取巡检任务详细信息
     */
    @GetMapping(value = "/app/{inspectionId}")
    public AjaxResult appGetInfo(@PathVariable("inspectionId") Long inspectionId) {
        return success(inspectionService.selectInspectionByInspectionId(inspectionId));
    }

    /**
     * APP获取巡检任务关联的设备列表
     */
    @GetMapping("/equipments/app/{inspectionId}")
    public AjaxResult appGetInspectionEquipments(@PathVariable("inspectionId") Long inspectionId) {
        InspectionEquipment inspectionEquipment = new InspectionEquipment();
        inspectionEquipment.setInspectionId(inspectionId);
        List<InspectionEquipment> list = inspectionEquipmentService.selectInspectionEquipmentList(inspectionEquipment);
        return success(list);
    }

    /**
     * 获取巡检任务统计信息
     */
    @GetMapping("/stats")
    public AjaxResult getInspectionStats(String userId) {
        return success(inspectionService.selectInspectionStats(userId));
    }

    /**
     * 接收巡检任务
     */
    @Log(title = "接收巡检任务", businessType = BusinessType.UPDATE)
    @PutMapping("/app_receive")
    public AjaxResult receiveInspection(@RequestBody Inspection inspection) {
        try {
            if (inspection == null || inspection.getInspectionId() == null) {
                return AjaxResult.error("缺少巡检任务ID");
            }

            // 设置接收时间为当前时间
            inspection.setReceiveTime(new java.util.Date());

            // 更新巡检任务信息
            int rows = inspectionService.updateInspection(inspection);

            return toAjax(rows);
        } catch (Exception e) {
            logger.error("处理接收巡检任务请求时出错", e);
            return AjaxResult.error("操作失败: " + e.getMessage());
        }
    }

    /**
     * APP更新设备巡检信息
     */
    @Log(title = "更新设备巡检", businessType = BusinessType.UPDATE)
    @PostMapping("/app_updateEquipmentInspection")
    public AjaxResult appUpdateEquipmentInspection(@RequestBody InspectionEquipment inspectionEquipment) {
        try {
            // 记录请求数据便于调试
            logger.info("接收到的设备巡检更新请求数据: " + inspectionEquipment);

            if (inspectionEquipment == null || inspectionEquipment.getInspectionId() == null || inspectionEquipment.getEquipmentId() == null) {
                return AjaxResult.error("缺少巡检ID或设备ID");
            }

            // 设置巡检时间为当前时间（如果未提供）
            if (inspectionEquipment.getInspectionTime() == null) {
                inspectionEquipment.setInspectionTime(new java.util.Date());
            }

            // 查询是否已存在该巡检设备记录
            InspectionEquipment query = new InspectionEquipment();
            query.setInspectionId(inspectionEquipment.getInspectionId());
            query.setEquipmentId(inspectionEquipment.getEquipmentId());
            List<InspectionEquipment> existingList = inspectionEquipmentService.selectInspectionEquipmentList(query);

            int rows = 0;
            if (existingList != null && !existingList.isEmpty()) {
                // 已存在记录，执行更新
                rows = inspectionEquipmentService.updateInspectionEquipment(inspectionEquipment);
            } else {
                // 不存在记录，执行新增
                rows = inspectionEquipmentService.insertInspectionEquipment(inspectionEquipment);
            }

            return toAjax(rows);
        } catch (Exception e) {
            logger.error("处理设备巡检更新请求时出错", e);
            return AjaxResult.error("操作失败: " + e.getMessage());
        }
    }

    /**
     * APP提交巡检任务
     */
    @Log(title = "提交巡检任务", businessType = BusinessType.UPDATE)
    @PostMapping("/app_submit")
    public AjaxResult appSubmitInspection(@RequestBody Inspection inspection) {
        try {
            if (inspection == null || inspection.getInspectionId() == null) {
                return AjaxResult.error("缺少巡检任务ID");
            }

            // 设置提交时间为当前时间
            inspection.setSubmitTime(new java.util.Date());

            // 更新巡检任务信息
            int rows = inspectionService.updateInspection(inspection);

            return toAjax(rows);
        } catch (Exception e) {
            logger.error("处理提交巡检任务请求时出错", e);
            return AjaxResult.error("操作失败: " + e.getMessage());
        }
    }
}
