package com.ruoyi.project.system.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Param;
import com.ruoyi.project.system.domain.MeterReadingEquipment;

/**
 * 电量统计和设备关联Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */
public interface MeterReadingEquipmentMapper 
{
    /**
     * 查询电量统计和设备关联
     * 
     * @param meterReadingId 电量统计ID
     * @param equipmentId 设备ID
     * @return 电量统计和设备关联
     */
    public MeterReadingEquipment selectMeterReadingEquipmentByIds(@Param("meterReadingId") Long meterReadingId, @Param("equipmentId") Long equipmentId);

    /**
     * 查询电量统计任务关联的设备列表
     * 
     * @param meterReadingId 电量统计任务ID
     * @return 电量统计和设备关联集合
     */
    public List<MeterReadingEquipment> selectMeterReadingEquipmentList(Long meterReadingId);

    /**
     * 新增电量统计和设备关联
     * 
     * @param meterReadingEquipment 电量统计和设备关联
     * @return 结果
     */
    public int insertMeterReadingEquipment(MeterReadingEquipment meterReadingEquipment);

    /**
     * 修改电量统计和设备关联
     * 
     * @param meterReadingEquipment 电量统计和设备关联
     * @return 结果
     */
    public int updateMeterReadingEquipment(MeterReadingEquipment meterReadingEquipment);

    /**
     * 删除电量统计和设备关联
     * 
     * @param meterReadingId 电量统计ID
     * @param equipmentId 设备ID
     * @return 结果
     */
    public int deleteMeterReadingEquipmentByIds(@Param("meterReadingId") Long meterReadingId, @Param("equipmentId") Long equipmentId);

    /**
     * 批量删除电量统计和设备关联
     * 
     * @param meterReadingId 电量统计任务ID
     * @return 结果
     */
    public int deleteMeterReadingEquipmentByMeterReadingId(Long meterReadingId);
}
