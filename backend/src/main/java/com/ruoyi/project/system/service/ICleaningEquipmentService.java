package com.ruoyi.project.system.service;

import java.util.List;
import com.ruoyi.project.system.domain.CleaningEquipment;

/**
 * 清洗和设备关联Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
public interface ICleaningEquipmentService 
{
    /**
     * 查询清洗和设备关联
     * 
     * @param cleaningId 清洗ID
     * @param equipmentId 设备ID
     * @return 清洗和设备关联
     */
    public CleaningEquipment selectCleaningEquipmentByIds(Long cleaningId, Long equipmentId);

    /**
     * 查询清洗任务关联的设备列表
     * 
     * @param cleaningId 清洗任务ID
     * @return 清洗和设备关联集合
     */
    public List<CleaningEquipment> selectCleaningEquipmentList(Long cleaningId);

    /**
     * 新增清洗和设备关联
     * 
     * @param cleaningEquipment 清洗和设备关联
     * @return 结果
     */
    public int insertCleaningEquipment(CleaningEquipment cleaningEquipment);

    /**
     * 修改清洗和设备关联
     * 
     * @param cleaningEquipment 清洗和设备关联
     * @return 结果
     */
    public int updateCleaningEquipment(CleaningEquipment cleaningEquipment);

    /**
     * 删除清洗和设备关联
     * 
     * @param cleaningId 清洗ID
     * @param equipmentId 设备ID
     * @return 结果
     */
    public int deleteCleaningEquipmentByIds(Long cleaningId, Long equipmentId);

    /**
     * 批量删除清洗和设备关联
     * 
     * @param cleaningId 清洗任务ID
     * @return 结果
     */
    public int deleteCleaningEquipmentByCleaningId(Long cleaningId);
    
    /**
     * 批量保存清洗和设备关联
     * 
     * @param cleaningId 清洗ID
     * @param equipments 设备列表
     * @return 结果
     */
    public int batchSaveCleaningEquipments(Long cleaningId, List<CleaningEquipment> equipments);
}
