package com.ruoyi.project.system.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.framework.aspectj.lang.annotation.Excel;
import com.ruoyi.framework.web.domain.BaseEntity;

/**
 * 电站信息对象 station
 *
 * <AUTHOR>
 * @date 2025-04-21
 */
public class Station extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 电站ID */
    @Excel(name = "电站ID", type = Excel.Type.EXPORT)
    private Long stationId;

    /** 电站名称 */
    @Excel(name = "电站名称")
    private String stationName;

    /** 投资类型 */
    @Excel(name = "投资类型", dictType = "station_owner_type")
    private String ownerType;

    /** 电站类型 */
    @Excel(name = "电站类型", dictType = "station_type")
    private String stationType;

    /** 直流侧容量(MW) */
    @Excel(name = "直流侧容量(MW)")
    private BigDecimal dcCapacity;

    /** 交流侧容量(MW) */
    @Excel(name = "交流侧容量(MW)")
    private BigDecimal acCapacity;

    /** 并网时间 */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Excel(name = "并网时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date gridTime;

    /** 详细地址 */
    @Excel(name = "详细地址")
    private String address;

    /** 电站照片 */
    @Excel(name = "电站照片")
    private String images;

    /** 并网类型 */
    @Excel(name = "并网类型", dictType = "station_grid_type")
    private String gridType;

    /** 电价计算方式 */
    @Excel(name = "电价计算方式")
    private String priceType;

    /** 用户联系方式 JSON格式 */
    @Excel(name = "用户联系方式")
    private String contact;

    /** 电站状态 */
    @Excel(name = "电站状态", dictType = "station_status")
    private String status;

    /** 删除标志 0:正常 2:删除 */
    private String delFlag;

    public void setStationId(Long stationId)
    {
        this.stationId = stationId;
    }

    public Long getStationId()
    {
        return stationId;
    }

    public void setStationName(String stationName)
    {
        this.stationName = stationName;
    }

    public String getStationName()
    {
        return stationName;
    }

    public void setOwnerType(String ownerType)
    {
        this.ownerType = ownerType;
    }

    public String getOwnerType()
    {
        return ownerType;
    }

    public void setStationType(String stationType)
    {
        this.stationType = stationType;
    }

    public String getStationType()
    {
        return stationType;
    }

    public void setDcCapacity(BigDecimal dcCapacity)
    {
        this.dcCapacity = dcCapacity;
    }

    public BigDecimal getDcCapacity()
    {
        return dcCapacity;
    }

    public void setAcCapacity(BigDecimal acCapacity)
    {
        this.acCapacity = acCapacity;
    }

    public BigDecimal getAcCapacity()
    {
        return acCapacity;
    }

    public void setGridTime(Date gridTime)
    {
        this.gridTime = gridTime;
    }

    public Date getGridTime()
    {
        return gridTime;
    }

    public void setAddress(String address)
    {
        this.address = address;
    }

    public String getAddress()
    {
        return address;
    }



    public void setImages(String images)
    {
        this.images = images;
    }

    public String getImages()
    {
        return images;
    }

    public void setGridType(String gridType)
    {
        this.gridType = gridType;
    }

    public String getGridType()
    {
        return gridType;
    }

    public void setPriceType(String priceType)
    {
        this.priceType = priceType;
    }

    public String getPriceType()
    {
        return priceType;
    }

    public void setContact(String contact)
    {
        this.contact = contact;
    }

    public String getContact()
    {
        return contact;
    }

    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getStatus()
    {
        return status;
    }

    public void setDelFlag(String delFlag)
    {
        this.delFlag = delFlag;
    }

    public String getDelFlag()
    {
        return delFlag;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("stationId", getStationId())
            .append("stationName", getStationName())
            .append("ownerType", getOwnerType())
            .append("stationType", getStationType())
            .append("dcCapacity", getDcCapacity())
            .append("acCapacity", getAcCapacity())
            .append("gridTime", getGridTime())
            .append("address", getAddress())
            .append("images", getImages())
            .append("gridType", getGridType())
            .append("priceType", getPriceType())
            .append("contact", getContact())
            .append("status", getStatus())
            .append("delFlag", getDelFlag())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
