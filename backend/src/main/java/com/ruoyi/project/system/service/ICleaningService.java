package com.ruoyi.project.system.service;

import java.util.List;
import java.util.Map;

import com.ruoyi.project.system.domain.Cleaning;
import com.ruoyi.project.system.domain.CleaningEquipment;

/**
 * 组件清洗任务Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
public interface ICleaningService 
{
    /**
     * 查询组件清洗任务
     * 
     * @param cleaningId 组件清洗任务主键
     * @return 组件清洗任务
     */
    public Cleaning selectCleaningByCleaningId(Long cleaningId);

    /**
     * 查询组件清洗任务列表
     * 
     * @param cleaning 组件清洗任务
     * @return 组件清洗任务集合
     */
    public List<Cleaning> selectCleaningList(Cleaning cleaning);

    /**
     * 新增组件清洗任务
     * 
     * @param cleaning 组件清洗任务
     * @return 结果
     */
    public int insertCleaning(Cleaning cleaning);

    /**
     * 修改组件清洗任务
     * 
     * @param cleaning 组件清洗任务
     * @return 结果
     */
    public int updateCleaning(Cleaning cleaning);

    /**
     * 批量删除组件清洗任务
     * 
     * @param cleaningIds 需要删除的组件清洗任务主键集合
     * @return 结果
     */
    public int deleteCleaningByCleaningIds(Long[] cleaningIds);

    /**
     * 删除组件清洗任务信息
     *
     * @param cleaningId 组件清洗任务主键
     * @return 结果
     */
    public int deleteCleaningByCleaningId(Long cleaningId);

    /**
     * App查询清洗任务列表
     *
     * @param cleaning 清洗任务
     * @param userId 用户ID
     * @return 清洗任务集合
     */
    public List<Cleaning> selectCleaningAppList(Cleaning cleaning, String userId);

    /**
     * 查询清洗任务统计信息
     *
     * @param userId 用户ID
     * @return 统计信息
     */
    public Map<String, Object> selectCleaningStats(String userId);

    /**
     * 查询清洗任务关联的设备列表
     *
     * @param cleaningId 清洗任务ID
     * @return 设备列表
     */
    public List<CleaningEquipment> selectCleaningEquipmentList(Long cleaningId);

    /**
     * 更新设备清洗信息
     *
     * @param requestMap 请求参数
     * @return 结果
     */
    public Map<String, Object> updateEquipmentCleaning(Map<String, Object> requestMap);

    /**
     * 提交清洗任务
     *
     * @param requestMap 请求参数
     * @return 结果
     */
    public Map<String, Object> submitCleaning(Map<String, Object> requestMap);
}
