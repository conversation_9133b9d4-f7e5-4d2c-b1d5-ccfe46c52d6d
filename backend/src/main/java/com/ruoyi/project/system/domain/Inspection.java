package com.ruoyi.project.system.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.framework.aspectj.lang.annotation.Excel;
import com.ruoyi.framework.web.domain.BaseEntity;

/**
 * 巡检任务对象 inspection
 *
 * <AUTHOR>
 * @date 2025-04-27
 */
public class Inspection extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 巡检ID */
    private Long inspectionId;

    /** 电站ID */
    @Excel(name = "电站ID")
    private Long stationId;

    /** 巡检任务名称 */
    @Excel(name = "巡检任务名称")
    private String inspectionName;

    /** 设定开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Excel(name = "设定开始时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date assignStartTime;

    /** 设定结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Excel(name = "设定结束时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date assignEndTime;

    /** 设定巡检人员 */
    @Excel(name = "设定巡检人员")
    private String assignUser;

    /** 设定负责人 */
    @Excel(name = "设定负责人")
    private String assignChargeUser;

    /** 实际巡检人员 */
    @Excel(name = "实际巡检人员")
    private String inspectorUser;

    /** 设定巡检人员名称 */
    @Excel(name = "设定巡检人员名称")
    private String assignUserName;

    /** 设定负责人名称 */
    @Excel(name = "设定负责人名称")
    private String assignChargeUserName;

    /** 实际巡检人员名称 */
    @Excel(name = "实际巡检人员名称")
    private String inspectorUserName;

    /** 接收时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Excel(name = "接收时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date receiveTime;

    /** 提交时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Excel(name = "提交时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date submitTime;

    /** 审核时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Excel(name = "审核时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date reviewTime;

    /** 审核意见 */
    @Excel(name = "审核意见")
    private String reviewComment;

    /** 状态 0已下发 2已接收 4已提交 6已审核 */
    @Excel(name = "状态", dictType = "inspection_status")
    private String status;

    /** 删除标志 0存在 2删除 */
    private String delFlag;

    /** 电站名称 */
    private Station station;

    /** 用户ID (筛选使用) */
    private String userId;

    public void setInspectionId(Long inspectionId)
    {
        this.inspectionId = inspectionId;
    }

    public Long getInspectionId()
    {
        return inspectionId;
    }

    public void setStationId(Long stationId)
    {
        this.stationId = stationId;
    }

    public Long getStationId()
    {
        return stationId;
    }

    public void setInspectionName(String inspectionName)
    {
        this.inspectionName = inspectionName;
    }

    public String getInspectionName()
    {
        return inspectionName;
    }

    public void setAssignStartTime(Date assignStartTime)
    {
        this.assignStartTime = assignStartTime;
    }

    public Date getAssignStartTime()
    {
        return assignStartTime;
    }

    public void setAssignEndTime(Date assignEndTime)
    {
        this.assignEndTime = assignEndTime;
    }

    public Date getAssignEndTime()
    {
        return assignEndTime;
    }

    public void setAssignUser(String assignUser)
    {
        this.assignUser = assignUser;
    }

    public String getAssignUser()
    {
        return assignUser;
    }

    public void setAssignChargeUser(String assignChargeUser)
    {
        this.assignChargeUser = assignChargeUser;
    }

    public String getAssignChargeUser()
    {
        return assignChargeUser;
    }

    public void setInspectorUser(String inspectorUser)
    {
        this.inspectorUser = inspectorUser;
    }

    public String getInspectorUser()
    {
        return inspectorUser;
    }

    public void setAssignUserName(String assignUserName)
    {
        this.assignUserName = assignUserName;
    }

    public String getAssignUserName()
    {
        return assignUserName;
    }

    public void setAssignChargeUserName(String assignChargeUserName)
    {
        this.assignChargeUserName = assignChargeUserName;
    }

    public String getAssignChargeUserName()
    {
        return assignChargeUserName;
    }

    public void setInspectorUserName(String inspectorUserName)
    {
        this.inspectorUserName = inspectorUserName;
    }

    public String getInspectorUserName()
    {
        return inspectorUserName;
    }

    public void setReceiveTime(Date receiveTime)
    {
        this.receiveTime = receiveTime;
    }

    public Date getReceiveTime()
    {
        return receiveTime;
    }

    public void setSubmitTime(Date submitTime)
    {
        this.submitTime = submitTime;
    }

    public Date getSubmitTime()
    {
        return submitTime;
    }

    public void setReviewTime(Date reviewTime)
    {
        this.reviewTime = reviewTime;
    }

    public Date getReviewTime()
    {
        return reviewTime;
    }

    public void setReviewComment(String reviewComment)
    {
        this.reviewComment = reviewComment;
    }

    public String getReviewComment()
    {
        return reviewComment;
    }

    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getStatus()
    {
        return status;
    }

    public void setDelFlag(String delFlag)
    {
        this.delFlag = delFlag;
    }

    public String getDelFlag()
    {
        return delFlag;
    }

    public void setStation(Station station)
    {
        this.station = station;
    }

    public Station getStation()
    {
        return station;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("inspectionId", getInspectionId())
            .append("stationId", getStationId())
            .append("inspectionName", getInspectionName())
            .append("assignStartTime", getAssignStartTime())
            .append("assignEndTime", getAssignEndTime())
            .append("assignUser", getAssignUser())
            .append("assignChargeUser", getAssignChargeUser())
            .append("inspectorUser", getInspectorUser())
            .append("assignUserName", getAssignUserName())
            .append("assignChargeUserName", getAssignChargeUserName())
            .append("inspectorUserName", getInspectorUserName())
            .append("receiveTime", getReceiveTime())
            .append("submitTime", getSubmitTime())
            .append("reviewTime", getReviewTime())
            .append("reviewComment", getReviewComment())
            .append("status", getStatus())
            .append("delFlag", getDelFlag())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .append("station", getStation())
            .toString();
    }
}
