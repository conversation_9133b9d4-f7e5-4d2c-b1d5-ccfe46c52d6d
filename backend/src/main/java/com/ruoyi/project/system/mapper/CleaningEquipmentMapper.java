package com.ruoyi.project.system.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Param;
import com.ruoyi.project.system.domain.CleaningEquipment;

/**
 * 清洗和设备关联Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
public interface CleaningEquipmentMapper 
{
    /**
     * 查询清洗和设备关联
     * 
     * @param cleaningId 清洗ID
     * @param equipmentId 设备ID
     * @return 清洗和设备关联
     */
    public CleaningEquipment selectCleaningEquipmentByIds(@Param("cleaningId") Long cleaningId, @Param("equipmentId") Long equipmentId);

    /**
     * 查询清洗任务关联的设备列表
     * 
     * @param cleaningId 清洗任务ID
     * @return 清洗和设备关联集合
     */
    public List<CleaningEquipment> selectCleaningEquipmentList(Long cleaningId);

    /**
     * 新增清洗和设备关联
     * 
     * @param cleaningEquipment 清洗和设备关联
     * @return 结果
     */
    public int insertCleaningEquipment(CleaningEquipment cleaningEquipment);

    /**
     * 修改清洗和设备关联
     * 
     * @param cleaningEquipment 清洗和设备关联
     * @return 结果
     */
    public int updateCleaningEquipment(CleaningEquipment cleaningEquipment);

    /**
     * 删除清洗和设备关联
     * 
     * @param cleaningId 清洗ID
     * @param equipmentId 设备ID
     * @return 结果
     */
    public int deleteCleaningEquipmentByIds(@Param("cleaningId") Long cleaningId, @Param("equipmentId") Long equipmentId);

    /**
     * 批量删除清洗和设备关联
     * 
     * @param cleaningId 清洗任务ID
     * @return 结果
     */
    public int deleteCleaningEquipmentByCleaningId(Long cleaningId);
}
