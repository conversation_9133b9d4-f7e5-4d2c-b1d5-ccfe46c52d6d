package com.ruoyi.project.system.controller;

import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.framework.aspectj.lang.annotation.Log;
import com.ruoyi.framework.aspectj.lang.enums.BusinessType;
import com.ruoyi.framework.web.controller.BaseController;
import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.framework.web.page.TableDataInfo;
import com.ruoyi.project.system.domain.Equipment;
import com.ruoyi.project.system.domain.Station;
import com.ruoyi.project.system.service.IEquipmentService;
import com.ruoyi.project.system.service.IStationService;
import com.ruoyi.project.system.service.ISysDictTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 电站信息Controller
 *
 * <AUTHOR>
 * @date 2025-04-21
 */
@RestController
@RequestMapping("/system/station")
public class StationController extends BaseController {
    @Autowired
    private IStationService stationService;

    @Autowired
    private IEquipmentService equipmentService;

    @Autowired
    private ISysDictTypeService dictTypeService;

    /**
     * 查询电站信息列表
     */
    @PreAuthorize("@ss.hasPermi('system:station:list')")
    @GetMapping("/list")
    public TableDataInfo list(Station station) {
        startPage();
        List<Station> list = stationService.selectStationList(station);
        return getDataTable(list);
    }

    /**
     * 导出电站信息列表
     */
    @PreAuthorize("@ss.hasPermi('system:station:export')")
    @Log(title = "电站信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Station station) {
        List<Station> list = stationService.selectStationList(station);
        ExcelUtil<Station> util = new ExcelUtil<Station>(Station.class);
        util.exportExcel(response, list, "电站信息数据");
    }

    /**
     * 获取电站信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:station:query')")
    @GetMapping(value = "/{stationId}")
    public AjaxResult getInfo(@PathVariable("stationId") Long stationId) {
        return success(stationService.selectStationByStationId(stationId));
    }

    /**
     * 新增电站信息
     */
    @PreAuthorize("@ss.hasPermi('system:station:add')")
    @Log(title = "电站信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Station station) {
        return toAjax(stationService.insertStation(station));
    }

    /**
     * 修改电站信息
     */
    @PreAuthorize("@ss.hasPermi('system:station:edit')")
    @Log(title = "电站信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Station station) {
        return toAjax(stationService.updateStation(station));
    }

    /**
     * 删除电站信息
     */
    @PreAuthorize("@ss.hasPermi('system:station:remove')")
    @Log(title = "电站信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{stationIds}")
    public AjaxResult remove(@PathVariable Long[] stationIds) {
        // 先检查是否有关联的设备
        for (Long stationId : stationIds) {
            Equipment equipment = new Equipment();
            equipment.setStationId(stationId);
            List<Equipment> equipmentList = equipmentService.selectEquipmentList(equipment);
            if (!equipmentList.isEmpty()) {
                return AjaxResult.error("电站有关联的设备，不能删除！请先删除或闲置设备");
            }
        }
        return toAjax(stationService.deleteStationByStationIds(stationIds));
    }

    // ============================ APP接口 ============================

    /**
     * APP获取电站列表
     */
    @GetMapping("/app_list")
    public AjaxResult appList(Station station) {
        station.setStatus("3");
        return success(stationService.selectStationList(station));
    }

    /**
     * APP获取电站详情
     */
    @GetMapping("/app_detail/{stationId}")
    public AjaxResult appDetail(@PathVariable("stationId") Long stationId) {
        return success(stationService.selectStationByStationId(stationId));
    }
}
