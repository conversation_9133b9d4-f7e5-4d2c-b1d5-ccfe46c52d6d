package com.ruoyi.project.system.service.impl;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.project.system.mapper.InspectionMapper;
import com.ruoyi.project.system.domain.Inspection;
import com.ruoyi.project.system.service.IInspectionService;

/**
 * 巡检任务Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-27
 */
@Service
public class InspectionServiceImpl implements IInspectionService
{
    @Autowired
    private InspectionMapper inspectionMapper;

    /**
     * 查询巡检任务
     *
     * @param inspectionId 巡检任务主键
     * @return 巡检任务
     */
    @Override
    public Inspection selectInspectionByInspectionId(Long inspectionId)
    {
        return inspectionMapper.selectInspectionByInspectionId(inspectionId);
    }

    /**
     * 查询巡检任务列表
     *
     * @param inspection 巡检任务
     * @return 巡检任务
     */
    @Override
    public List<Inspection> selectInspectionList(Inspection inspection)
    {
        return inspectionMapper.selectInspectionList(inspection);
    }

    /**
     * 新增巡检任务
     *
     * @param inspection 巡检任务
     * @return 结果
     */
    @Override
    public int insertInspection(Inspection inspection)
    {
        inspection.setCreateBy(SecurityUtils.getUsername());
        inspection.setCreateTime(DateUtils.getNowDate());
        return inspectionMapper.insertInspection(inspection);
    }

    /**
     * 修改巡检任务
     *
     * @param inspection 巡检任务
     * @return 结果
     */
    @Override
    public int updateInspection(Inspection inspection)
    {
        inspection.setUpdateBy(SecurityUtils.getUsername());
        inspection.setUpdateTime(DateUtils.getNowDate());
        return inspectionMapper.updateInspection(inspection);
    }

    /**
     * 批量删除巡检任务
     *
     * @param inspectionIds 需要删除的巡检任务主键
     * @return 结果
     */
    @Override
    public int deleteInspectionByInspectionIds(Long[] inspectionIds)
    {
        return inspectionMapper.deleteInspectionByInspectionIds(inspectionIds);
    }

    /**
     * 删除巡检任务信息
     *
     * @param inspectionId 巡检任务主键
     * @return 结果
     */
    @Override
    public int deleteInspectionByInspectionId(Long inspectionId)
    {
        return inspectionMapper.deleteInspectionByInspectionId(inspectionId);
    }

    /**
     * App查询巡检任务列表
     *
     * @param inspection 巡检任务
     * @param userId 用户ID
     * @return 巡检任务集合
     */
    @Override
    public List<Inspection> selectInspectionAppList(Inspection inspection, String userId) {
        inspection.setUserId(userId);
        return inspectionMapper.selectInspectionAppList(inspection);
    }

    /**
     * 查询巡检任务统计信息
     *
     * @param userId 用户ID
     * @return 统计信息
     */
    @Override
    public Map<String, Object> selectInspectionStats(String userId) {
        return inspectionMapper.selectInspectionStats(userId);
    }
}
