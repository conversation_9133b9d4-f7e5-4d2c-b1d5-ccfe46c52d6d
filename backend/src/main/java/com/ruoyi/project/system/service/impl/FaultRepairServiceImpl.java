package com.ruoyi.project.system.service.impl;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.project.system.mapper.FaultRepairMapper;
import com.ruoyi.project.system.mapper.FaultMapper;
import com.ruoyi.project.system.domain.FaultRepair;
import com.ruoyi.project.system.domain.Fault;
import com.ruoyi.project.system.service.IFaultRepairService;

/**
 * 维修任务Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-14
 */
@Service
public class FaultRepairServiceImpl implements IFaultRepairService 
{
    @Autowired
    private FaultRepairMapper faultRepairMapper;
    
    @Autowired
    private FaultMapper faultMapper;

    /**
     * 查询维修任务
     * 
     * @param repairId 维修任务主键
     * @return 维修任务
     */
    @Override
    public FaultRepair selectFaultRepairByRepairId(Long repairId)
    {
        return faultRepairMapper.selectFaultRepairByRepairId(repairId);
    }

    /**
     * 查询维修任务列表
     * 
     * @param faultRepair 维修任务
     * @return 维修任务
     */
    @Override
    public List<FaultRepair> selectFaultRepairList(FaultRepair faultRepair)
    {
        return faultRepairMapper.selectFaultRepairList(faultRepair);
    }

    /**
     * 新增维修任务
     *
     * @param faultRepair 维修任务
     * @return 结果
     */
    @Override
    @Transactional
    public int insertFaultRepair(FaultRepair faultRepair)
    {
        faultRepair.setCreateBy(SecurityUtils.getUsername());
        faultRepair.setCreateTime(DateUtils.getNowDate());
        faultRepair.setDelFlag("0"); // 设置删除标志为未删除
        
        // 设置默认状态为已下发
        if (faultRepair.getStatus() == null) {
            faultRepair.setStatus("0");
        }
        
        int rows = faultRepairMapper.insertFaultRepair(faultRepair);
        
        // 创建维修任务后，更新故障状态为已确认
        if (rows > 0 && faultRepair.getFaultId() != null) {
            Fault fault = new Fault();
            fault.setFaultId(faultRepair.getFaultId());
            fault.setStatus("4"); // 已确认
            fault.setUpdateBy(SecurityUtils.getUsername());
            fault.setUpdateTime(DateUtils.getNowDate());
            faultMapper.updateFault(fault);
        }
        
        return rows;
    }

    /**
     * 修改维修任务
     * 
     * @param faultRepair 维修任务
     * @return 结果
     */
    @Override
    public int updateFaultRepair(FaultRepair faultRepair)
    {
        faultRepair.setUpdateBy(SecurityUtils.getUsername());
        faultRepair.setUpdateTime(DateUtils.getNowDate());
        return faultRepairMapper.updateFaultRepair(faultRepair);
    }

    /**
     * 批量删除维修任务
     * 
     * @param repairIds 需要删除的维修任务主键
     * @return 结果
     */
    @Override
    public int deleteFaultRepairByRepairIds(Long[] repairIds)
    {
        return faultRepairMapper.deleteFaultRepairByRepairIds(repairIds);
    }

    /**
     * 删除维修任务
     *
     * @param repairId 维修任务主键
     * @return 结果
     */
    @Override
    public int deleteFaultRepairByRepairId(Long repairId)
    {
        return faultRepairMapper.deleteFaultRepairByRepairId(repairId);
    }

    /**
     * App查询维修任务列表
     *
     * @param faultRepair 维修任务
     * @param userId 用户ID
     * @return 维修任务集合
     */
    @Override
    public List<FaultRepair> selectFaultRepairAppList(FaultRepair faultRepair, String userId) {
        return faultRepairMapper.selectFaultRepairAppList(faultRepair, userId);
    }

    /**
     * 查询维修任务统计
     *
     * @param userId 用户ID
     * @return 统计信息
     */
    @Override
    public Map<String, Object> selectFaultRepairStats(String userId) {
        return faultRepairMapper.selectFaultRepairStats(userId);
    }

    /**
     * 根据故障ID查询维修任务列表
     *
     * @param faultId 故障ID
     * @return 维修任务集合
     */
    @Override
    public List<FaultRepair> selectFaultRepairListByFaultId(Long faultId) {
        return faultRepairMapper.selectFaultRepairListByFaultId(faultId);
    }

    /**
     * 根据电站ID查询维修任务列表
     *
     * @param stationId 电站ID
     * @return 维修任务集合
     */
    @Override
    public List<FaultRepair> selectFaultRepairListByStationId(Long stationId) {
        return faultRepairMapper.selectFaultRepairListByStationId(stationId);
    }

    /**
     * 接收维修任务
     *
     * @param faultRepair 维修任务
     * @return 结果
     */
    @Override
    public int receiveFaultRepair(FaultRepair faultRepair) {
        faultRepair.setReceiveTime(DateUtils.getNowDate());
        faultRepair.setStatus("2"); // 已接收
        faultRepair.setUpdateBy(SecurityUtils.getUsername());
        faultRepair.setUpdateTime(DateUtils.getNowDate());
        return faultRepairMapper.updateFaultRepair(faultRepair);
    }

    /**
     * 提交维修任务
     *
     * @param requestMap 请求参数
     * @return 结果
     */
    @Override
    @Transactional
    public Map<String, Object> submitFaultRepair(Map<String, Object> requestMap) {
        Map<String, Object> result = new HashMap<>();
        try {
            Long repairId = Long.valueOf(requestMap.get("repairId").toString());
            
            FaultRepair faultRepair = new FaultRepair();
            faultRepair.setRepairId(repairId);
            faultRepair.setStatus("4"); // 已提交
            faultRepair.setSubmitTime(DateUtils.getNowDate());
            faultRepair.setUpdateBy(SecurityUtils.getUsername());
            faultRepair.setUpdateTime(DateUtils.getNowDate());
            
            // 更新维修记录信息
            if (requestMap.containsKey("repairRemark")) {
                faultRepair.setRepairRemark((String) requestMap.get("repairRemark"));
            }
            if (requestMap.containsKey("replacedParts")) {
                faultRepair.setReplacedParts((String) requestMap.get("replacedParts"));
            }
            if (requestMap.containsKey("beforeImages")) {
                faultRepair.setBeforeImages((String) requestMap.get("beforeImages"));
            }
            if (requestMap.containsKey("afterImages")) {
                faultRepair.setAfterImages((String) requestMap.get("afterImages"));
            }
            
            int rows = faultRepairMapper.updateFaultRepair(faultRepair);
            
            result.put("success", rows > 0);
            result.put("message", rows > 0 ? "提交成功" : "提交失败");
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "提交失败: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 审核维修任务
     *
     * @param faultRepair 维修任务
     * @return 结果
     */
    @Override
    @Transactional
    public int reviewFaultRepair(FaultRepair faultRepair) {
        faultRepair.setReviewTime(DateUtils.getNowDate());
        faultRepair.setUpdateBy(SecurityUtils.getUsername());
        faultRepair.setUpdateTime(DateUtils.getNowDate());
        
        int rows = faultRepairMapper.updateFaultRepair(faultRepair);
        
        // 审核通过后，更新故障状态为已完成
        if (rows > 0 && "6".equals(faultRepair.getStatus())) {
            FaultRepair repair = faultRepairMapper.selectFaultRepairByRepairId(faultRepair.getRepairId());
            if (repair != null && repair.getFaultId() != null) {
                Fault fault = new Fault();
                fault.setFaultId(repair.getFaultId());
                fault.setStatus("6"); // 已完成
                fault.setUpdateBy(SecurityUtils.getUsername());
                fault.setUpdateTime(DateUtils.getNowDate());
                faultMapper.updateFault(fault);
            }
        }
        
        return rows;
    }

    /**
     * 更新维修任务状态
     *
     * @param repairId 维修任务ID
     * @param status 状态
     * @return 结果
     */
    @Override
    public int updateFaultRepairStatus(Long repairId, String status) {
        FaultRepair faultRepair = new FaultRepair();
        faultRepair.setRepairId(repairId);
        faultRepair.setStatus(status);
        faultRepair.setUpdateBy(SecurityUtils.getUsername());
        faultRepair.setUpdateTime(DateUtils.getNowDate());
        return faultRepairMapper.updateFaultRepair(faultRepair);
    }

    /**
     * 创建维修任务（从故障创建）
     *
     * @param requestMap 请求参数
     * @return 结果
     */
    @Override
    @Transactional
    public Map<String, Object> createRepairFromFault(Map<String, Object> requestMap) {
        Map<String, Object> result = new HashMap<>();
        try {
            FaultRepair faultRepair = new FaultRepair();
            faultRepair.setFaultId(Long.valueOf(requestMap.get("faultId").toString()));
            faultRepair.setRepairName((String) requestMap.get("repairName"));
            faultRepair.setAssignUser((String) requestMap.get("assignUser"));
            faultRepair.setAssignChargeUser((String) requestMap.get("assignChargeUser"));
            faultRepair.setRepairPlan((String) requestMap.get("repairPlan"));

            // 处理时间字段，支持字符串格式
            if (requestMap.containsKey("planStartTime") && requestMap.get("planStartTime") != null) {
                Object startTimeObj = requestMap.get("planStartTime");
                if (startTimeObj instanceof String) {
                    try {
                        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                        faultRepair.setPlanStartTime(sdf.parse((String) startTimeObj));
                    } catch (ParseException e) {
                        throw new RuntimeException("计划开始时间格式错误: " + startTimeObj);
                    }
                } else if (startTimeObj instanceof Date) {
                    faultRepair.setPlanStartTime((Date) startTimeObj);
                }
            }

            if (requestMap.containsKey("planEndTime") && requestMap.get("planEndTime") != null) {
                Object endTimeObj = requestMap.get("planEndTime");
                if (endTimeObj instanceof String) {
                    try {
                        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                        faultRepair.setPlanEndTime(sdf.parse((String) endTimeObj));
                    } catch (ParseException e) {
                        throw new RuntimeException("计划结束时间格式错误: " + endTimeObj);
                    }
                } else if (endTimeObj instanceof Date) {
                    faultRepair.setPlanEndTime((Date) endTimeObj);
                }
            }

            int rows = insertFaultRepair(faultRepair);

            result.put("success", rows > 0);
            result.put("message", rows > 0 ? "维修任务创建成功" : "维修任务创建失败");
            result.put("repairId", faultRepair.getRepairId());

        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "维修任务创建失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 查询待接收维修任务数量
     *
     * @param userId 用户ID
     * @return 待接收维修任务数量
     */
    @Override
    public int selectPendingRepairCount(String userId) {
        return faultRepairMapper.selectPendingRepairCount(userId);
    }

    /**
     * 查询进行中维修任务数量
     *
     * @param userId 用户ID
     * @return 进行中维修任务数量
     */
    @Override
    public int selectProcessingRepairCount(String userId) {
        return faultRepairMapper.selectProcessingRepairCount(userId);
    }

    /**
     * 查询已完成维修任务数量
     *
     * @param userId 用户ID
     * @return 已完成维修任务数量
     */
    @Override
    public int selectCompletedRepairCount(String userId) {
        return faultRepairMapper.selectCompletedRepairCount(userId);
    }

    /**
     * 查询已审核维修任务数量
     *
     * @param userId 用户ID
     * @return 已审核维修任务数量
     */
    @Override
    public int selectReviewedRepairCount(String userId) {
        return faultRepairMapper.selectReviewedRepairCount(userId);
    }
}
