package com.ruoyi.project.system.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.project.system.mapper.StationMapper;
import com.ruoyi.project.system.domain.Station;
import com.ruoyi.project.system.service.IStationService;

/**
 * 电站信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-04-21
 */
@Service
public class StationServiceImpl implements IStationService 
{
    @Autowired
    private StationMapper stationMapper;

    /**
     * 查询电站信息
     * 
     * @param stationId 电站信息主键
     * @return 电站信息
     */
    @Override
    public Station selectStationByStationId(Long stationId)
    {
        return stationMapper.selectStationByStationId(stationId);
    }

    /**
     * 查询电站信息列表
     * 
     * @param station 电站信息
     * @return 电站信息
     */
    @Override
    public List<Station> selectStationList(Station station)
    {
        return stationMapper.selectStationList(station);
    }

    /**
     * 新增电站信息
     * 
     * @param station 电站信息
     * @return 结果
     */
    @Override
    public int insertStation(Station station)
    {
        station.setCreateBy(SecurityUtils.getUsername());
        station.setCreateTime(DateUtils.getNowDate());
        return stationMapper.insertStation(station);
    }

    /**
     * 修改电站信息
     * 
     * @param station 电站信息
     * @return 结果
     */
    @Override
    public int updateStation(Station station)
    {
        station.setUpdateBy(SecurityUtils.getUsername());
        station.setUpdateTime(DateUtils.getNowDate());
        return stationMapper.updateStation(station);
    }

    /**
     * 批量删除电站信息
     * 
     * @param stationIds 需要删除的电站信息主键
     * @return 结果
     */
    @Override
    public int deleteStationByStationIds(Long[] stationIds)
    {
        return stationMapper.deleteStationByStationIds(stationIds);
    }

    /**
     * 删除电站信息信息
     * 
     * @param stationId 电站信息主键
     * @return 结果
     */
    @Override
    public int deleteStationByStationId(Long stationId)
    {
        return stationMapper.deleteStationByStationId(stationId);
    }
}
