package com.ruoyi.project.system.mapper;

import java.util.List;
import com.ruoyi.project.system.domain.Station;

/**
 * 电站信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-04-21
 */
public interface StationMapper 
{
    /**
     * 查询电站信息
     * 
     * @param stationId 电站信息主键
     * @return 电站信息
     */
    public Station selectStationByStationId(Long stationId);

    /**
     * 查询电站信息列表
     * 
     * @param station 电站信息
     * @return 电站信息集合
     */
    public List<Station> selectStationList(Station station);

    /**
     * 新增电站信息
     * 
     * @param station 电站信息
     * @return 结果
     */
    public int insertStation(Station station);

    /**
     * 修改电站信息
     * 
     * @param station 电站信息
     * @return 结果
     */
    public int updateStation(Station station);

    /**
     * 删除电站信息
     * 
     * @param stationId 电站信息主键
     * @return 结果
     */
    public int deleteStationByStationId(Long stationId);

    /**
     * 批量删除电站信息
     * 
     * @param stationIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteStationByStationIds(Long[] stationIds);
}
