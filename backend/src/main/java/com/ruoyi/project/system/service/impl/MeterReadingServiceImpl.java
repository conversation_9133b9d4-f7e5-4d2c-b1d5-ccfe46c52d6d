package com.ruoyi.project.system.service.impl;

import java.util.*;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.project.system.mapper.MeterReadingMapper;
import com.ruoyi.project.system.mapper.MeterReadingEquipmentMapper;
import com.ruoyi.project.system.domain.MeterReading;
import com.ruoyi.project.system.domain.MeterReadingEquipment;
import com.ruoyi.project.system.service.IMeterReadingService;

/**
 * 电量统计任务Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */
@Service
public class MeterReadingServiceImpl implements IMeterReadingService
{
    @Autowired
    private MeterReadingMapper meterReadingMapper;

    @Autowired
    private MeterReadingEquipmentMapper meterReadingEquipmentMapper;

    /**
     * 查询电量统计任务
     * 
     * @param meterReadingId 电量统计任务主键
     * @return 电量统计任务
     */
    @Override
    public MeterReading selectMeterReadingByMeterReadingId(Long meterReadingId)
    {
        return meterReadingMapper.selectMeterReadingByMeterReadingId(meterReadingId);
    }

    /**
     * 查询电量统计任务列表
     * 
     * @param meterReading 电量统计任务
     * @return 电量统计任务
     */
    @Override
    public List<MeterReading> selectMeterReadingList(MeterReading meterReading)
    {
        return meterReadingMapper.selectMeterReadingList(meterReading);
    }

    /**
     * 新增电量统计任务
     * 
     * @param meterReading 电量统计任务
     * @return 结果
     */
    @Override
    public int insertMeterReading(MeterReading meterReading)
    {
        meterReading.setCreateTime(DateUtils.getNowDate());
        return meterReadingMapper.insertMeterReading(meterReading);
    }

    /**
     * 修改电量统计任务
     * 
     * @param meterReading 电量统计任务
     * @return 结果
     */
    @Override
    public int updateMeterReading(MeterReading meterReading)
    {
        meterReading.setUpdateTime(DateUtils.getNowDate());
        return meterReadingMapper.updateMeterReading(meterReading);
    }

    /**
     * 批量删除电量统计任务
     * 
     * @param meterReadingIds 需要删除的电量统计任务主键
     * @return 结果
     */
    @Override
    public int deleteMeterReadingByMeterReadingIds(Long[] meterReadingIds)
    {
        return meterReadingMapper.deleteMeterReadingByMeterReadingIds(meterReadingIds);
    }

    /**
     * 删除电量统计任务信息
     * 
     * @param meterReadingId 电量统计任务主键
     * @return 结果
     */
    @Override
    public int deleteMeterReadingByMeterReadingId(Long meterReadingId)
    {
        return meterReadingMapper.deleteMeterReadingByMeterReadingId(meterReadingId);
    }

    /**
     * App查询电量统计任务列表
     *
     * @param meterReading 电量统计任务
     * @param userId 用户ID
     * @return 电量统计任务集合
     */
    @Override
    public List<MeterReading> selectMeterReadingAppList(MeterReading meterReading, String userId) {
        return meterReadingMapper.selectMeterReadingAppList(meterReading, userId);
    }

    /**
     * 查询电量统计任务统计信息
     *
     * @param userId 用户ID
     * @return 统计信息
     */
    @Override
    public Map<String, Object> selectMeterReadingStats(String userId) {
        return meterReadingMapper.selectMeterReadingStats(userId);
    }

    /**
     * 查询电量统计任务关联的设备列表
     *
     * @param meterReadingId 电量统计任务ID
     * @return 设备列表
     */
    @Override
    public List<MeterReadingEquipment> selectMeterReadingEquipmentList(Long meterReadingId) {
        return meterReadingEquipmentMapper.selectMeterReadingEquipmentList(meterReadingId);
    }

    /**
     * 更新设备电量统计信息
     *
     * @param requestMap 请求参数
     * @return 结果
     */
    @Override
    @Transactional
    public Map<String, Object> updateEquipmentMeterReading(Map<String, Object> requestMap) {
        Map<String, Object> result = new HashMap<>();
        try {
            Long meterReadingId = Long.valueOf(requestMap.get("meterReadingId").toString());
            Long equipmentId = Long.valueOf(requestMap.get("equipmentId").toString());
            Long userId = Long.valueOf(requestMap.get("userId").toString());

            // 根据实际字段获取参数
            java.math.BigDecimal lastMonthReverseActive = null;
            java.math.BigDecimal totalReverseActive = null;
            if (requestMap.get("lastMonthReverseActive") != null) {
                lastMonthReverseActive = new java.math.BigDecimal(requestMap.get("lastMonthReverseActive").toString());
            }
            if (requestMap.get("totalReverseActive") != null) {
                totalReverseActive = new java.math.BigDecimal(requestMap.get("totalReverseActive").toString());
            }

            String photos = (String) requestMap.get("photos");
            String meterReadingRemark = (String) requestMap.get("meterReadingRemark");

            MeterReadingEquipment meterReadingEquipment = new MeterReadingEquipment();
            meterReadingEquipment.setMeterReadingId(meterReadingId);
            meterReadingEquipment.setEquipmentId(equipmentId);
            meterReadingEquipment.setUserId(userId);
            meterReadingEquipment.setLastMonthReverseActive(lastMonthReverseActive);
            meterReadingEquipment.setTotalReverseActive(totalReverseActive);
            meterReadingEquipment.setPhotos(photos);
            meterReadingEquipment.setMeterReadingRemark(meterReadingRemark);
            meterReadingEquipment.setMeterReadingTime(new Date());

            int rows = meterReadingEquipmentMapper.updateMeterReadingEquipment(meterReadingEquipment);

            result.put("success", rows > 0);
            result.put("message", rows > 0 ? "更新成功" : "更新失败");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "操作失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 提交电量统计任务
     *
     * @param requestMap 请求参数
     * @return 结果
     */
    @Override
    @Transactional
    public Map<String, Object> submitMeterReading(Map<String, Object> requestMap) {
        Map<String, Object> result = new HashMap<>();
        try {
            Long meterReadingId = Long.valueOf(requestMap.get("meterReadingId").toString());

            // 更新电量统计任务状态为已提交
            MeterReading meterReading = new MeterReading();
            meterReading.setMeterReadingId(meterReadingId);
            meterReading.setStatus("4"); // 已提交
            meterReading.setSubmitTime(new Date());
            meterReading.setUpdateBy(SecurityUtils.getUsername());
            meterReading.setUpdateTime(DateUtils.getNowDate());

            int rows = meterReadingMapper.updateMeterReading(meterReading);

            result.put("success", rows > 0);
            result.put("message", rows > 0 ? "提交成功" : "提交失败");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "操作失败: " + e.getMessage());
        }
        return result;
    }
}
