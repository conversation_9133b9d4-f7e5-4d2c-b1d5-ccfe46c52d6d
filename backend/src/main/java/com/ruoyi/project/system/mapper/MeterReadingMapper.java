package com.ruoyi.project.system.mapper;

import java.util.List;
import java.util.Map;
import org.apache.ibatis.annotations.Param;
import com.ruoyi.project.system.domain.MeterReading;

/**
 * 电量统计任务Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */
public interface MeterReadingMapper 
{
    /**
     * 查询电量统计任务
     * 
     * @param meterReadingId 电量统计任务主键
     * @return 电量统计任务
     */
    public MeterReading selectMeterReadingByMeterReadingId(Long meterReadingId);

    /**
     * 查询电量统计任务列表
     * 
     * @param meterReading 电量统计任务
     * @return 电量统计任务集合
     */
    public List<MeterReading> selectMeterReadingList(MeterReading meterReading);

    /**
     * 新增电量统计任务
     * 
     * @param meterReading 电量统计任务
     * @return 结果
     */
    public int insertMeterReading(MeterReading meterReading);

    /**
     * 修改电量统计任务
     * 
     * @param meterReading 电量统计任务
     * @return 结果
     */
    public int updateMeterReading(MeterReading meterReading);

    /**
     * 删除电量统计任务
     * 
     * @param meterReadingId 电量统计任务主键
     * @return 结果
     */
    public int deleteMeterReadingByMeterReadingId(Long meterReadingId);

    /**
     * 批量删除电量统计任务
     *
     * @param meterReadingIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMeterReadingByMeterReadingIds(Long[] meterReadingIds);

    /**
     * App查询电量统计任务列表
     *
     * @param meterReading 电量统计任务
     * @param userId 用户ID
     * @return 电量统计任务集合
     */
    public List<MeterReading> selectMeterReadingAppList(@Param("meterReading") MeterReading meterReading, @Param("userId") String userId);

    /**
     * 查询电量统计任务统计信息
     *
     * @param userId 用户ID
     * @return 统计信息
     */
    public Map<String, Object> selectMeterReadingStats(String userId);
}
