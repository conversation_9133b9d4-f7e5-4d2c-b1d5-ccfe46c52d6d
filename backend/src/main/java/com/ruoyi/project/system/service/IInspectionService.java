package com.ruoyi.project.system.service;

import java.util.List;
import java.util.Map;
import com.ruoyi.project.system.domain.Inspection;

/**
 * 巡检任务Service接口
 *
 * <AUTHOR>
 * @date 2025-04-27
 */
public interface IInspectionService
{
    /**
     * 查询巡检任务
     *
     * @param inspectionId 巡检任务主键
     * @return 巡检任务
     */
    public Inspection selectInspectionByInspectionId(Long inspectionId);

    /**
     * 查询巡检任务列表
     *
     * @param inspection 巡检任务
     * @return 巡检任务集合
     */
    public List<Inspection> selectInspectionList(Inspection inspection);

    /**
     * 新增巡检任务
     *
     * @param inspection 巡检任务
     * @return 结果
     */
    public int insertInspection(Inspection inspection);

    /**
     * 修改巡检任务
     *
     * @param inspection 巡检任务
     * @return 结果
     */
    public int updateInspection(Inspection inspection);

    /**
     * 批量删除巡检任务
     *
     * @param inspectionIds 需要删除的巡检任务主键集合
     * @return 结果
     */
    public int deleteInspectionByInspectionIds(Long[] inspectionIds);

    /**
     * 删除巡检任务信息
     *
     * @param inspectionId 巡检任务主键
     * @return 结果
     */
    public int deleteInspectionByInspectionId(Long inspectionId);

    /**
     * App查询巡检任务列表
     *
     * @param inspection 巡检任务
     * @param userId 用户ID
     * @return 巡检任务集合
     */
    List<Inspection> selectInspectionAppList(Inspection inspection, String userId);

    /**
     * 查询巡检任务统计信息
     *
     * @param userId 用户ID
     * @return 统计信息
     */
    Map<String, Object> selectInspectionStats(String userId);
}
