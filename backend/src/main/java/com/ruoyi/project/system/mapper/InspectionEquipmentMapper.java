package com.ruoyi.project.system.mapper;

import java.util.List;
import com.ruoyi.project.system.domain.InspectionEquipment;

/**
 * 巡检和设备关联Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-29
 */
public interface InspectionEquipmentMapper
{
    /**
     * 查询巡检和设备关联，包含设备和巡检任务信息
     *
     * @param inspectionId 巡检和设备关联主键
     * @return 巡检和设备关联
     */
    public InspectionEquipment selectInspectionEquipmentByInspectionId(Long inspectionId);

    /**
     * 查询巡检和设备关联列表，包含设备和巡检任务信息
     *
     * @param inspectionEquipment 巡检和设备关联
     * @return 巡检和设备关联集合
     */
    public List<InspectionEquipment> selectInspectionEquipmentList(InspectionEquipment inspectionEquipment);

    /**
     * 新增巡检和设备关联
     *
     * @param inspectionEquipment 巡检和设备关联
     * @return 结果
     */
    public int insertInspectionEquipment(InspectionEquipment inspectionEquipment);

    /**
     * 修改巡检和设备关联
     *
     * @param inspectionEquipment 巡检和设备关联
     * @return 结果
     */
    public int updateInspectionEquipment(InspectionEquipment inspectionEquipment);

    /**
     * 删除巡检和设备关联
     *
     * @param inspectionId 巡检和设备关联主键
     * @return 结果
     */
    public int deleteInspectionEquipmentByInspectionId(Long inspectionId);

    /**
     * 批量删除巡检和设备关联
     *
     * @param inspectionIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteInspectionEquipmentByInspectionIds(Long[] inspectionIds);
}
