package com.ruoyi.project.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.project.system.domain.InspectionEquipment;
import com.ruoyi.project.system.service.IInspectionEquipmentService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import com.ruoyi.framework.aspectj.lang.annotation.Log;
import com.ruoyi.framework.aspectj.lang.enums.BusinessType;
import com.ruoyi.project.system.domain.Equipment;
import com.ruoyi.project.system.service.IEquipmentService;
import com.ruoyi.framework.web.controller.BaseController;
import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.framework.web.page.TableDataInfo;

/**
 * 设备信息Controller
 * 
 * <AUTHOR>
 * @date 2025-04-23
 */
@RestController
@RequestMapping("/system/equipment")
public class EquipmentController extends BaseController
{
    @Autowired
    private IEquipmentService equipmentService;

    @Autowired
    private IInspectionEquipmentService inspectionEquipmentService;

    /**
     * 查询设备信息列表
     */
    @PreAuthorize("@ss.hasPermi('system:equipment:list')")
    @GetMapping("/list")
    public TableDataInfo list(Equipment equipment)
    {
        startPage();
        List<Equipment> list = equipmentService.selectEquipmentList(equipment);
        return getDataTable(list);
    }

    /**
     * 根据电站ID查询设备信息列表
     */
    @PreAuthorize("@ss.hasPermi('system:equipment:list')")
    @GetMapping("/listByStationId/{stationId}")
    public AjaxResult listByStationId(@PathVariable("stationId") Long stationId)
    {
        Equipment equipment = new Equipment();
        equipment.setStationId(stationId);
        List<Equipment> list = equipmentService.selectEquipmentList(equipment);
        return success(list);
    }

    /**
     * 导出设备信息列表
     */
    @PreAuthorize("@ss.hasPermi('system:equipment:export')")
    @Log(title = "设备信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Equipment equipment)
    {
        List<Equipment> list = equipmentService.selectEquipmentList(equipment);
        ExcelUtil<Equipment> util = new ExcelUtil<Equipment>(Equipment.class);
        util.exportExcel(response, list, "设备信息数据");
    }

    /**
     * 导入设备信息
     */
    @Log(title = "设备信息", businessType = BusinessType.IMPORT)
    @PreAuthorize("@ss.hasPermi('system:equipment:import')")
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport, Long stationId) throws Exception
    {
        ExcelUtil<Equipment> util = new ExcelUtil<Equipment>(Equipment.class);
        List<Equipment> equipmentList = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = equipmentService.importEquipment(equipmentList, updateSupport, operName, stationId);
        return success(message);
    }

    /**
     * 下载设备信息导入模板
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response)
    {
        ExcelUtil<Equipment> util = new ExcelUtil<Equipment>(Equipment.class);
        util.importTemplateExcel(response, "设备信息数据");
    }

    /**
     * 获取设备信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:equipment:query')")
    @GetMapping(value = "/{equipmentId}")
    public AjaxResult getInfo(@PathVariable("equipmentId") Long equipmentId)
    {
        return success(equipmentService.selectEquipmentByEquipmentId(equipmentId));
    }

    /**
     * 新增设备信息
     */
    @PreAuthorize("@ss.hasPermi('system:equipment:add')")
    @Log(title = "设备信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Equipment equipment)
    {
        return toAjax(equipmentService.insertEquipment(equipment));
    }

    /**
     * 修改设备信息
     */
    @PreAuthorize("@ss.hasPermi('system:equipment:edit')")
    @Log(title = "设备信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Equipment equipment)
    {
        return toAjax(equipmentService.updateEquipment(equipment));
    }

    /**
     * 删除设备信息
     */
    @PreAuthorize("@ss.hasPermi('system:equipment:remove')")
    @Log(title = "设备信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{equipmentIds}")
    public AjaxResult remove(@PathVariable Long[] equipmentIds)
    {
        return toAjax(equipmentService.deleteEquipmentByEquipmentIds(equipmentIds));
    }

    // ============================ APP接口 ============================
    /**
     * 查询设备信息列表
     */
    @GetMapping("/app_list")
    public TableDataInfo appList(Equipment equipment)
    {
        startPage();
        List<Equipment> list = equipmentService.selectEquipmentList(equipment);
        return getDataTable(list);
    }

    /**
     * 根据电站ID查询设备信息列表
     */
    @GetMapping("/app_listByStationId/{stationId}")
    public AjaxResult appListByStationId(@PathVariable("stationId") Long stationId)
    {
        Equipment equipment = new Equipment();
        equipment.setStationId(stationId);
        List<Equipment> list = equipmentService.selectEquipmentList(equipment);
        return success(list);
    }

    /**
     * 获取设备信息详细信息
     */
    @GetMapping(value = "/app/{equipmentId}")
    public AjaxResult appGetInfo(@PathVariable("equipmentId") Long equipmentId)
    {
        return success(equipmentService.selectEquipmentByEquipmentId(equipmentId));
    }

    /**
     * APP获取设备巡检信息
     */
    @GetMapping("/app_inspection/{equipmentId}")
    public AjaxResult appGetEquipmentInspection(@PathVariable("equipmentId") Long equipmentId) {
        InspectionEquipment inspectionEquipment = new InspectionEquipment();
        inspectionEquipment.setEquipmentId(equipmentId);
        return success(inspectionEquipmentService.selectInspectionEquipmentList(inspectionEquipment));
    }

}
