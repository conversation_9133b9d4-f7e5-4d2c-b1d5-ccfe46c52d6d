package com.ruoyi.project.system.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.framework.aspectj.lang.annotation.Excel;
import com.ruoyi.framework.web.domain.BaseEntity;

/**
 * 清洗和设备关联对象 cleaning_equipment
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
public class CleaningEquipment extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 清洗ID */
    private Long cleaningId;

    /** 设备ID */
    private Long equipmentId;

    /** 清洗人员ID */
    @Excel(name = "清洗人员ID")
    private Long userId;

    /** 清洗前照片 */
    @Excel(name = "清洗前照片")
    private String beforeImage;

    /** 清洗后照片 */
    @Excel(name = "清洗后照片")
    private String afterImage;

    /** 清洗方式 0机器清洗 1手工清洗 */
    @Excel(name = "清洗方式")
    private String cleaningMethod;

    /** 积尘厚度 0少量 1中等 2严重 */
    @Excel(name = "积尘厚度")
    private String cleaningDustThickness;

    /** 清洗时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Excel(name = "清洗时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date cleaningTime;

    /** 清洗备注 */
    @Excel(name = "清洗备注")
    private String cleaningRemark;

    /** 关联的设备信息 */
    private Equipment equipment;

    /** 关联的清洗任务信息 */
    private Cleaning cleaning;

    public void setCleaningId(Long cleaningId) 
    {
        this.cleaningId = cleaningId;
    }

    public Long getCleaningId() 
    {
        return cleaningId;
    }
    public void setEquipmentId(Long equipmentId) 
    {
        this.equipmentId = equipmentId;
    }

    public Long getEquipmentId() 
    {
        return equipmentId;
    }
    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }
    public void setBeforeImage(String beforeImage) 
    {
        this.beforeImage = beforeImage;
    }

    public String getBeforeImage() 
    {
        return beforeImage;
    }
    public void setAfterImage(String afterImage) 
    {
        this.afterImage = afterImage;
    }

    public String getAfterImage() 
    {
        return afterImage;
    }
    public void setCleaningMethod(String cleaningMethod)
    {
        this.cleaningMethod = cleaningMethod;
    }

    public String getCleaningMethod()
    {
        return cleaningMethod;
    }

    public void setCleaningDustThickness(String cleaningDustThickness)
    {
        this.cleaningDustThickness = cleaningDustThickness;
    }

    public String getCleaningDustThickness()
    {
        return cleaningDustThickness;
    }
    public void setCleaningTime(Date cleaningTime) 
    {
        this.cleaningTime = cleaningTime;
    }

    public Date getCleaningTime() 
    {
        return cleaningTime;
    }
    public void setCleaningRemark(String cleaningRemark) 
    {
        this.cleaningRemark = cleaningRemark;
    }

    public String getCleaningRemark() 
    {
        return cleaningRemark;
    }

    public Equipment getEquipment() 
    {
        return equipment;
    }

    public void setEquipment(Equipment equipment) 
    {
        this.equipment = equipment;
    }

    public Cleaning getCleaning() 
    {
        return cleaning;
    }

    public void setCleaning(Cleaning cleaning) 
    {
        this.cleaning = cleaning;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("cleaningId", getCleaningId())
            .append("equipmentId", getEquipmentId())
            .append("userId", getUserId())
            .append("beforeImage", getBeforeImage())
            .append("afterImage", getAfterImage())
            .append("cleaningMethod", getCleaningMethod())
            .append("cleaningDustThickness", getCleaningDustThickness())
            .append("cleaningTime", getCleaningTime())
            .append("cleaningRemark", getCleaningRemark())
            .toString();
    }
}
