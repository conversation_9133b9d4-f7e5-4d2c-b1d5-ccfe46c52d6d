package com.ruoyi.project.system.service;

import java.util.List;
import java.util.Map;
import com.ruoyi.project.system.domain.Fault;

/**
 * 故障信息Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-14
 */
public interface IFaultService 
{
    /**
     * 查询故障信息
     * 
     * @param faultId 故障信息主键
     * @return 故障信息
     */
    public Fault selectFaultByFaultId(Long faultId);

    /**
     * 查询故障信息列表
     * 
     * @param fault 故障信息
     * @return 故障信息集合
     */
    public List<Fault> selectFaultList(Fault fault);

    /**
     * 新增故障信息
     * 
     * @param fault 故障信息
     * @return 结果
     */
    public int insertFault(Fault fault);

    /**
     * 修改故障信息
     * 
     * @param fault 故障信息
     * @return 结果
     */
    public int updateFault(Fault fault);

    /**
     * 批量删除故障信息
     * 
     * @param faultIds 需要删除的故障信息主键集合
     * @return 结果
     */
    public int deleteFaultByFaultIds(Long[] faultIds);

    /**
     * 删除故障信息
     *
     * @param faultId 故障信息主键
     * @return 结果
     */
    public int deleteFaultByFaultId(Long faultId);

    /**
     * App查询故障信息列表
     *
     * @param fault 故障信息
     * @return 故障信息集合
     */
    public List<Fault> selectFaultAppList(Fault fault);

    /**
     * 查询故障信息统计
     *
     * @param userId 用户ID
     * @return 统计信息
     */
    public Map<String, Object> selectFaultStats(String userId);

    /**
     * 根据电站ID查询故障信息列表
     *
     * @param stationId 电站ID
     * @return 故障信息集合
     */
    public List<Fault> selectFaultListByStationId(Long stationId);

    /**
     * 根据设备ID查询故障信息列表
     *
     * @param equipmentId 设备ID
     * @return 故障信息集合
     */
    public List<Fault> selectFaultListByEquipmentId(Long equipmentId);

    /**
     * 根据故障来源查询故障信息列表
     *
     * @param faultSource 故障来源
     * @return 故障信息集合
     */
    public List<Fault> selectFaultListBySource(String faultSource);

    /**
     * 审核故障信息
     *
     * @param fault 故障信息
     * @return 结果
     */
    public int reviewFault(Fault fault);

    /**
     * 更新故障状态
     *
     * @param faultId 故障ID
     * @param status 状态
     * @return 结果
     */
    public int updateFaultStatus(Long faultId, String status);

    /**
     * 故障上报（从巡检/清洗发现）
     *
     * @param requestMap 请求参数
     * @return 结果
     */
    public Map<String, Object> reportFault(Map<String, Object> requestMap);

    /**
     * 查询待处理故障数量
     *
     * @param userId 用户ID
     * @return 待处理故障数量
     */
    public int selectPendingFaultCount(String userId);

    /**
     * 查询处理中故障数量
     *
     * @param userId 用户ID
     * @return 处理中故障数量
     */
    public int selectProcessingFaultCount(String userId);

    /**
     * 查询已完成故障数量
     *
     * @param userId 用户ID
     * @return 已完成故障数量
     */
    public int selectCompletedFaultCount(String userId);
}
