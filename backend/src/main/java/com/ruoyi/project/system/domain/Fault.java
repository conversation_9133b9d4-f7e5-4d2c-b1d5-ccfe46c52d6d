package com.ruoyi.project.system.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.framework.aspectj.lang.annotation.Excel;
import com.ruoyi.framework.web.domain.BaseEntity;

/**
 * 故障信息对象 fault
 * 
 * <AUTHOR>
 * @date 2025-01-14
 */
public class Fault extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 故障ID */
    private Long faultId;

    /** 电站ID */
    @Excel(name = "电站ID")
    private Long stationId;

    /** 设备ID列表 */
    @Excel(name = "设备ID列表")
    private String equipmentListId;

    /** 故障名称 */
    @Excel(name = "故障名称")
    private String faultName;

    /** 故障类型 1设备故障 2线路故障 3系统故障 4环境故障 5其他故障 */
    @Excel(name = "故障类型", dictType = "fault_type")
    private String faultType;

    /** 故障等级 0轻微 1一般 2严重 3紧急 */
    @Excel(name = "故障等级", dictType = "fault_level")
    private String faultLevel;

    /** 故障描述 */
    @Excel(name = "故障描述")
    private String faultDescription;

    /** 故障照片 */
    @Excel(name = "故障照片")
    private String faultImages;

    /** 故障来源  1巡检发现 2清洗发现 3监控发现 4用户报告 5定期检查 6其他 */
    @Excel(name = "故障来源", dictType = "fault_source")
    private String faultSource;

    /** 发现人员ID */
    @Excel(name = "发现人员ID")
    private Long discoverUserId;

    /** 发现时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "发现时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date discoverTime;

    /** 状态 0待处理 1已驳回 2已确认 3已关闭 */
    @Excel(name = "状态", dictType = "fault_status")
    private String status;

    /** 审核人员 */
    @Excel(name = "审核人员")
    private String reviewUser;

    /** 审核时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "审核时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date reviewTime;

    /** 审核意见 */
    @Excel(name = "审核意见")
    private String reviewComment;

    /** 删除标志 */
    private String delFlag;

    /** 关联的电站信息 */
    private Station station;

    /** 关联的用户信息 */
    private SysUser user;

    /** 审核人员名称 */
    @Excel(name = "审核人员名称")
    private String reviewUserName;

    /** 发现人员名称 */
    @Excel(name = "发现人员名称")
    private String discoverUserName;

    /** 用户ID (筛选使用) */
    private String userId;

    public void setFaultId(Long faultId) 
    {
        this.faultId = faultId;
    }

    public Long getFaultId() 
    {
        return faultId;
    }

    public void setStationId(Long stationId) 
    {
        this.stationId = stationId;
    }

    public Long getStationId() 
    {
        return stationId;
    }

    public void setEquipmentListId(String equipmentListId) 
    {
        this.equipmentListId = equipmentListId;
    }

    public String getEquipmentListId() 
    {
        return equipmentListId;
    }

    public void setFaultName(String faultName) 
    {
        this.faultName = faultName;
    }

    public String getFaultName() 
    {
        return faultName;
    }

    public void setFaultType(String faultType) 
    {
        this.faultType = faultType;
    }

    public String getFaultType() 
    {
        return faultType;
    }

    public void setFaultLevel(String faultLevel) 
    {
        this.faultLevel = faultLevel;
    }

    public String getFaultLevel() 
    {
        return faultLevel;
    }

    public void setFaultDescription(String faultDescription) 
    {
        this.faultDescription = faultDescription;
    }

    public String getFaultDescription() 
    {
        return faultDescription;
    }

    public void setFaultImages(String faultImages) 
    {
        this.faultImages = faultImages;
    }

    public String getFaultImages()
    {
        return faultImages;
    }

    public void setFaultSource(String faultSource)
    {
        this.faultSource = faultSource;
    }

    public String getFaultSource()
    {
        return faultSource;
    }

    public void setDiscoverUserId(Long discoverUserId)
    {
        this.discoverUserId = discoverUserId;
    }

    public Long getDiscoverUserId()
    {
        return discoverUserId;
    }

    public void setDiscoverTime(Date discoverTime)
    {
        this.discoverTime = discoverTime;
    }

    public Date getDiscoverTime()
    {
        return discoverTime;
    }

    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    public void setReviewUser(String reviewUser) 
    {
        this.reviewUser = reviewUser;
    }

    public String getReviewUser() 
    {
        return reviewUser;
    }

    public void setReviewTime(Date reviewTime) 
    {
        this.reviewTime = reviewTime;
    }

    public Date getReviewTime() 
    {
        return reviewTime;
    }

    public void setReviewComment(String reviewComment) 
    {
        this.reviewComment = reviewComment;
    }

    public String getReviewComment() 
    {
        return reviewComment;
    }

    public void setDelFlag(String delFlag) 
    {
        this.delFlag = delFlag;
    }

    public String getDelFlag() 
    {
        return delFlag;
    }

    public void setStation(Station station) 
    {
        this.station = station;
    }

    public Station getStation() 
    {
        return station;
    }

    public void setUser(SysUser user) 
    {
        this.user = user;
    }

    public SysUser getUser() 
    {
        return user;
    }

    public void setReviewUserName(String reviewUserName) 
    {
        this.reviewUserName = reviewUserName;
    }

    public String getReviewUserName()
    {
        return reviewUserName;
    }

    public void setDiscoverUserName(String discoverUserName)
    {
        this.discoverUserName = discoverUserName;
    }

    public String getDiscoverUserName()
    {
        return discoverUserName;
    }

    public void setUserId(String userId)
    {
        this.userId = userId;
    }

    public String getUserId()
    {
        return userId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("faultId", getFaultId())
            .append("stationId", getStationId())
            .append("equipmentListId", getEquipmentListId())
            .append("faultName", getFaultName())
            .append("faultType", getFaultType())
            .append("faultLevel", getFaultLevel())
            .append("faultDescription", getFaultDescription())
            .append("faultImages", getFaultImages())
            .append("faultSource", getFaultSource())
            .append("discoverUserId", getDiscoverUserId())
            .append("discoverTime", getDiscoverTime())
            .append("status", getStatus())
            .append("reviewUser", getReviewUser())
            .append("reviewTime", getReviewTime())
            .append("reviewComment", getReviewComment())
            .append("delFlag", getDelFlag())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
