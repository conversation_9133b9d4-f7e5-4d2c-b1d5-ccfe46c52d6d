package com.ruoyi.project.system.service.impl;

import java.util.*;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.project.system.mapper.CleaningMapper;
import com.ruoyi.project.system.mapper.CleaningEquipmentMapper;
import com.ruoyi.project.system.domain.Cleaning;
import com.ruoyi.project.system.domain.CleaningEquipment;
import com.ruoyi.project.system.service.ICleaningService;

/**
 * 组件清洗任务Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
@Service
public class CleaningServiceImpl implements ICleaningService
{
    @Autowired
    private CleaningMapper cleaningMapper;

    @Autowired
    private CleaningEquipmentMapper cleaningEquipmentMapper;

    /**
     * 查询组件清洗任务
     * 
     * @param cleaningId 组件清洗任务主键
     * @return 组件清洗任务
     */
    @Override
    public Cleaning selectCleaningByCleaningId(Long cleaningId)
    {
        return cleaningMapper.selectCleaningByCleaningId(cleaningId);
    }

    /**
     * 查询组件清洗任务列表
     * 
     * @param cleaning 组件清洗任务
     * @return 组件清洗任务
     */
    @Override
    public List<Cleaning> selectCleaningList(Cleaning cleaning)
    {
        return cleaningMapper.selectCleaningList(cleaning);
    }

    /**
     * 新增组件清洗任务
     *
     * @param cleaning 组件清洗任务
     * @return 结果
     */
    @Override
    public int insertCleaning(Cleaning cleaning)
    {
        cleaning.setCreateBy(SecurityUtils.getUsername());
        cleaning.setCreateTime(DateUtils.getNowDate());
        cleaning.setDelFlag("0"); // 设置删除标志为未删除
        return cleaningMapper.insertCleaning(cleaning);
    }

    /**
     * 修改组件清洗任务
     * 
     * @param cleaning 组件清洗任务
     * @return 结果
     */
    @Override
    public int updateCleaning(Cleaning cleaning)
    {
        cleaning.setUpdateBy(SecurityUtils.getUsername());
        cleaning.setUpdateTime(DateUtils.getNowDate());
        return cleaningMapper.updateCleaning(cleaning);
    }

    /**
     * 批量删除组件清洗任务
     * 
     * @param cleaningIds 需要删除的组件清洗任务主键
     * @return 结果
     */
    @Override
    public int deleteCleaningByCleaningIds(Long[] cleaningIds)
    {
        return cleaningMapper.deleteCleaningByCleaningIds(cleaningIds);
    }

    /**
     * 删除组件清洗任务信息
     *
     * @param cleaningId 组件清洗任务主键
     * @return 结果
     */
    @Override
    public int deleteCleaningByCleaningId(Long cleaningId)
    {
        return cleaningMapper.deleteCleaningByCleaningId(cleaningId);
    }

    /**
     * App查询清洗任务列表
     *
     * @param cleaning 清洗任务
     * @param userId 用户ID
     * @return 清洗任务集合
     */
    @Override
    public List<Cleaning> selectCleaningAppList(Cleaning cleaning, String userId) {
        return cleaningMapper.selectCleaningAppList(cleaning, userId);
    }

    /**
     * 查询清洗任务统计信息
     *
     * @param userId 用户ID
     * @return 统计信息
     */
    @Override
    public Map<String, Object> selectCleaningStats(String userId) {
        return cleaningMapper.selectCleaningStats(userId);
    }

    /**
     * 查询清洗任务关联的设备列表
     *
     * @param cleaningId 清洗任务ID
     * @return 设备列表
     */
    @Override
    public List<CleaningEquipment> selectCleaningEquipmentList(Long cleaningId) {
        return cleaningEquipmentMapper.selectCleaningEquipmentList(cleaningId);
    }

    /**
     * 更新设备清洗信息
     *
     * @param requestMap 请求参数
     * @return 结果
     */
    @Override
    @Transactional
    public Map<String, Object> updateEquipmentCleaning(Map<String, Object> requestMap) {
        Map<String, Object> result = new HashMap<>();
        try {
            Long cleaningId = Long.valueOf(requestMap.get("cleaningId").toString());
            Long equipmentId = Long.valueOf(requestMap.get("equipmentId").toString());
            Long userId = Long.valueOf(requestMap.get("userId").toString());
            String beforeImage = (String) requestMap.get("beforeImage");
            String afterImage = (String) requestMap.get("afterImage");
            String cleaningMethod = (String) requestMap.get("cleaningMethod");
            String cleaningDustThickness = (String) requestMap.get("cleaningDustThickness");
            String cleaningRemark = (String) requestMap.get("cleaningRemark");

            CleaningEquipment cleaningEquipment = new CleaningEquipment();
            cleaningEquipment.setCleaningId(cleaningId);
            cleaningEquipment.setEquipmentId(equipmentId);
            cleaningEquipment.setUserId(userId);
            cleaningEquipment.setBeforeImage(beforeImage);
            cleaningEquipment.setAfterImage(afterImage);
            cleaningEquipment.setCleaningMethod(cleaningMethod);
            cleaningEquipment.setCleaningDustThickness(cleaningDustThickness);
            cleaningEquipment.setCleaningRemark(cleaningRemark);
            cleaningEquipment.setCleaningTime(new Date());

            int rows = cleaningEquipmentMapper.updateCleaningEquipment(cleaningEquipment);

            result.put("success", rows > 0);
            result.put("message", rows > 0 ? "更新成功" : "更新失败");

        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "更新失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 提交清洗任务
     *
     * @param requestMap 请求参数
     * @return 结果
     */
    @Override
    @Transactional
    public Map<String, Object> submitCleaning(Map<String, Object> requestMap) {
        Map<String, Object> result = new HashMap<>();
        try {
            Long cleaningId = Long.valueOf(requestMap.get("cleaningId").toString());

            // 更新清洗任务状态为已完成
            Cleaning cleaning = new Cleaning();
            cleaning.setCleaningId(cleaningId);
            cleaning.setStatus("4"); // 已提交
            cleaning.setSubmitTime(new Date());
            cleaning.setUpdateBy(SecurityUtils.getUsername());
            cleaning.setUpdateTime(DateUtils.getNowDate());

            int rows = cleaningMapper.updateCleaning(cleaning);

            result.put("success", rows > 0);
            result.put("message", rows > 0 ? "提交成功" : "提交失败");

        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "提交失败: " + e.getMessage());
        }
        return result;
    }
}
