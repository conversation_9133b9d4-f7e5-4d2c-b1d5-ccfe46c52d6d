package com.ruoyi.project.system.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.framework.aspectj.lang.annotation.Log;
import com.ruoyi.framework.aspectj.lang.enums.BusinessType;
import com.ruoyi.framework.web.controller.BaseController;
import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.framework.web.page.TableDataInfo;
import com.ruoyi.project.system.domain.Cleaning;
import com.ruoyi.project.system.domain.CleaningEquipment;
import com.ruoyi.project.system.service.ICleaningEquipmentService;
import com.ruoyi.project.system.service.ICleaningService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 组件清洗任务Controller
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
@RestController
@RequestMapping("/system/cleaning")
public class CleaningController extends BaseController {
    private static final Logger logger = LoggerFactory.getLogger(CleaningController.class);

    @Autowired
    private ICleaningService cleaningService;

    @Autowired
    private ICleaningEquipmentService cleaningEquipmentService;

    /**
     * 查询组件清洗任务列表
     */
    @PreAuthorize("@ss.hasPermi('system:cleaning:list')")
    @GetMapping("/list")
    public TableDataInfo list(Cleaning cleaning) {
        startPage();
        List<Cleaning> list = cleaningService.selectCleaningList(cleaning);
        return getDataTable(list);
    }

    /**
     * 导出组件清洗任务列表
     */
    @PreAuthorize("@ss.hasPermi('system:cleaning:export')")
    @Log(title = "组件清洗任务", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Cleaning cleaning) {
        List<Cleaning> list = cleaningService.selectCleaningList(cleaning);
        ExcelUtil<Cleaning> util = new ExcelUtil<Cleaning>(Cleaning.class);
        util.exportExcel(response, list, "组件清洗任务数据");
    }

    /**
     * 获取组件清洗任务详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:cleaning:query')")
    @GetMapping(value = "/{cleaningId}")
    public AjaxResult getInfo(@PathVariable("cleaningId") Long cleaningId) {
        return success(cleaningService.selectCleaningByCleaningId(cleaningId));
    }

    /**
     * 新增组件清洗任务
     */
    @PreAuthorize("@ss.hasPermi('system:cleaning:add')")
    @Log(title = "组件清洗任务", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody java.util.Map<String, Object> requestMap) {
        try {
            // 从请求中提取cleaning和equipments字段
            Cleaning cleaning = null;
            List<CleaningEquipment> equipments = null;

            if (requestMap.containsKey("cleaning")) {
                ObjectMapper objectMapper = new ObjectMapper();
                cleaning = objectMapper.convertValue(requestMap.get("cleaning"), Cleaning.class);
            }

            if (cleaning == null) {
                return AjaxResult.error("缺少清洗任务信息");
            }

            // 设置默认状态为已下发
            cleaning.setStatus("0");

            // 保存清洗任务
            int result = cleaningService.insertCleaning(cleaning);
            if (result <= 0) {
                return AjaxResult.error("保存清洗任务失败");
            }

            // 处理设备关联信息
            if (requestMap.containsKey("equipments") && requestMap.get("equipments") != null) {
                try {
                    ObjectMapper objectMapper = new ObjectMapper();
                    List<Map<String, Object>> equipmentMaps = (List<Map<String, Object>>) requestMap.get("equipments");
                    equipments = new ArrayList<>();

                    for (Map<String, Object> equipmentMap : equipmentMaps) {
                        CleaningEquipment equipment = new CleaningEquipment();
                        equipment.setCleaningId(cleaning.getCleaningId());
                        equipment.setEquipmentId(Long.valueOf(equipmentMap.get("equipmentId").toString()));

                        equipments.add(equipment);
                    }

                    // 保存设备关联信息
                    cleaningEquipmentService.batchSaveCleaningEquipments(cleaning.getCleaningId(), equipments);
                } catch (Exception e) {
                    return AjaxResult.error("处理设备关联信息失败: " + e.getMessage());
                }
            }

            return success(cleaning.getCleaningId());
        } catch (Exception e) {
            return AjaxResult.error("操作失败: " + e.getMessage());
        }
    }

    /**
     * 修改组件清洗任务
     */
    @PreAuthorize("@ss.hasPermi('system:cleaning:edit')")
    @Log(title = "组件清洗任务", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody java.util.Map<String, Object> requestMap) {
        try {
            // 从请求中提取cleaning和equipments字段
            Cleaning cleaning = null;
            List<CleaningEquipment> equipments = null;

            if (requestMap.containsKey("cleaning")) {
                ObjectMapper objectMapper = new ObjectMapper();
                cleaning = objectMapper.convertValue(requestMap.get("cleaning"), Cleaning.class);
            }

            if (cleaning == null) {
                return AjaxResult.error("缺少清洗任务信息");
            }

            // 更新清洗任务
            int rows = cleaningService.updateCleaning(cleaning);

            // 处理设备关联信息
            if (requestMap.containsKey("equipments") && requestMap.get("equipments") != null) {
                try {
                    ObjectMapper objectMapper = new ObjectMapper();
                    List<Map<String, Object>> equipmentMaps = (List<Map<String, Object>>) requestMap.get("equipments");
                    equipments = new ArrayList<>();

                    for (Map<String, Object> equipmentMap : equipmentMaps) {
                        CleaningEquipment equipment = new CleaningEquipment();
                        equipment.setCleaningId(cleaning.getCleaningId());
                        equipment.setEquipmentId(Long.valueOf(equipmentMap.get("equipmentId").toString()));

                        equipments.add(equipment);
                    }

                    // 保存设备关联信息
                    cleaningEquipmentService.batchSaveCleaningEquipments(cleaning.getCleaningId(), equipments);
                } catch (Exception e) {
                    return AjaxResult.error("处理设备关联信息失败: " + e.getMessage());
                }
            }

            return toAjax(rows);
        } catch (Exception e) {
            return AjaxResult.error("操作失败: " + e.getMessage());
        }
    }

    /**
     * 删除组件清洗任务
     */
    @PreAuthorize("@ss.hasPermi('system:cleaning:remove')")
    @Log(title = "组件清洗任务", businessType = BusinessType.DELETE)
    @DeleteMapping("/{cleaningIds}")
    public AjaxResult remove(@PathVariable Long[] cleaningIds) {
        return toAjax(cleaningService.deleteCleaningByCleaningIds(cleaningIds));
    }

    /**
     * 审核清洗任务
     */
    @PreAuthorize("@ss.hasPermi('system:cleaning:review')")
    @Log(title = "审核清洗任务", businessType = BusinessType.UPDATE)
    @PutMapping("/review")
    public AjaxResult reviewCleaning(@RequestBody Cleaning cleaning) {
        try {
            if (cleaning == null || cleaning.getCleaningId() == null) {
                return AjaxResult.error("缺少清洗任务ID");
            }

            // 设置审核时间为当前时间
            cleaning.setReviewTime(new java.util.Date());

            // 状态由前端传入，支持审核通过(6)和审核不通过
            if (cleaning.getStatus() == null) {
                cleaning.setStatus("6"); // 默认为审核通过
            }

            // 更新清洗任务信息
            int rows = cleaningService.updateCleaning(cleaning);

            return toAjax(rows);
        } catch (Exception e) {
            logger.error("处理审核清洗任务请求时出错", e);
            return AjaxResult.error("操作失败: " + e.getMessage());
        }
    }

    // ==================== 移动端API接口 ====================

    /**
     * App查询清洗任务列表
     */
    @GetMapping("/app_list/{userId}")
    public TableDataInfo appList(Cleaning cleaning, @PathVariable String userId) {
        startPage();
        List<Cleaning> list = cleaningService.selectCleaningAppList(cleaning, userId);
        return getDataTable(list);
    }

    /**
     * APP根据电站ID查询清洗任务列表
     */
    @GetMapping("/app/listByStationId")
    public TableDataInfo appListByStationId(Cleaning cleaning) {
        startPage();
        List<Cleaning> list = cleaningService.selectCleaningList(cleaning);
        return getDataTable(list);
    }

    /**
     * APP查询清洗任务详细信息
     */
    @GetMapping("/app/{cleaningId}")
    public AjaxResult appGetInfo(@PathVariable("cleaningId") Long cleaningId) {
        return success(cleaningService.selectCleaningByCleaningId(cleaningId));
    }

    /**
     * 获取清洗任务关联的设备列表
     */
    @PreAuthorize("@ss.hasPermi('system:cleaning:query')")
    @GetMapping("/equipments/{cleaningId}")
    public AjaxResult getCleaningEquipments(@PathVariable Long cleaningId) {
        return success(cleaningService.selectCleaningEquipmentList(cleaningId));
    }

    /**
     * 获取清洗任务关联的设备列表 - APP接口
     */
    @GetMapping("/equipments/app/{cleaningId}")
    public AjaxResult getCleaningEquipmentsApp(@PathVariable Long cleaningId) {
        return success(cleaningService.selectCleaningEquipmentList(cleaningId));
    }

    /**
     * 获取清洗任务统计信息
     */
    @GetMapping("/stats")
    public AjaxResult getCleaningStats(String userId) {
        return success(cleaningService.selectCleaningStats(userId));
    }

    /**
     * 更新设备清洗信息
     */
    @Log(title = "更新设备清洗信息", businessType = BusinessType.UPDATE)
    @PostMapping("/app_updateEquipmentCleaning")
    public AjaxResult updateEquipmentCleaning(@RequestBody java.util.Map<String, Object> requestMap) {
        try {
            return success(cleaningService.updateEquipmentCleaning(requestMap));
        } catch (Exception e) {
            logger.error("处理更新设备清洗信息请求时出错", e);
            return AjaxResult.error("操作失败: " + e.getMessage());
        }
    }

    /**
     * 接收清洗任务
     */
    @Log(title = "接收清洗任务", businessType = BusinessType.UPDATE)
    @PutMapping("/app_receive")
    public AjaxResult receiveCleaning(@RequestBody Cleaning cleaning) {
        try {
            if (cleaning == null || cleaning.getCleaningId() == null) {
                return AjaxResult.error("缺少清洗任务ID");
            }

            // 设置接收时间为当前时间
            cleaning.setReceiveTime(new java.util.Date());
            cleaning.setStatus("2"); // 设置为进行中状态

            // 更新清洗任务信息
            int rows = cleaningService.updateCleaning(cleaning);

            return toAjax(rows);
        } catch (Exception e) {
            logger.error("处理接收清洗任务请求时出错", e);
            return AjaxResult.error("操作失败: " + e.getMessage());
        }
    }

    /**
     * 提交清洗任务
     */
    @Log(title = "提交清洗任务", businessType = BusinessType.UPDATE)
    @PostMapping("/app_submit")
    public AjaxResult submitCleaning(@RequestBody java.util.Map<String, Object> requestMap) {
        try {
            return success(cleaningService.submitCleaning(requestMap));
        } catch (Exception e) {
            logger.error("处理提交清洗任务请求时出错", e);
            return AjaxResult.error("操作失败: " + e.getMessage());
        }
    }
}
