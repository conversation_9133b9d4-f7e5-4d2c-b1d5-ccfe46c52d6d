package com.ruoyi.project.system.service;

import java.util.List;
import java.util.Map;
import com.ruoyi.project.system.domain.FaultRepair;

/**
 * 维修任务Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-14
 */
public interface IFaultRepairService 
{
    /**
     * 查询维修任务
     * 
     * @param repairId 维修任务主键
     * @return 维修任务
     */
    public FaultRepair selectFaultRepairByRepairId(Long repairId);

    /**
     * 查询维修任务列表
     * 
     * @param faultRepair 维修任务
     * @return 维修任务集合
     */
    public List<FaultRepair> selectFaultRepairList(FaultRepair faultRepair);

    /**
     * 新增维修任务
     * 
     * @param faultRepair 维修任务
     * @return 结果
     */
    public int insertFaultRepair(FaultRepair faultRepair);

    /**
     * 修改维修任务
     * 
     * @param faultRepair 维修任务
     * @return 结果
     */
    public int updateFaultRepair(FaultRepair faultRepair);

    /**
     * 批量删除维修任务
     * 
     * @param repairIds 需要删除的维修任务主键集合
     * @return 结果
     */
    public int deleteFaultRepairByRepairIds(Long[] repairIds);

    /**
     * 删除维修任务
     *
     * @param repairId 维修任务主键
     * @return 结果
     */
    public int deleteFaultRepairByRepairId(Long repairId);

    /**
     * App查询维修任务列表
     *
     * @param faultRepair 维修任务
     * @param userId 用户ID
     * @return 维修任务集合
     */
    public List<FaultRepair> selectFaultRepairAppList(FaultRepair faultRepair, String userId);

    /**
     * 查询维修任务统计
     *
     * @param userId 用户ID
     * @return 统计信息
     */
    public Map<String, Object> selectFaultRepairStats(String userId);

    /**
     * 根据故障ID查询维修任务列表
     *
     * @param faultId 故障ID
     * @return 维修任务集合
     */
    public List<FaultRepair> selectFaultRepairListByFaultId(Long faultId);

    /**
     * 根据电站ID查询维修任务列表
     *
     * @param stationId 电站ID
     * @return 维修任务集合
     */
    public List<FaultRepair> selectFaultRepairListByStationId(Long stationId);

    /**
     * 接收维修任务
     *
     * @param faultRepair 维修任务
     * @return 结果
     */
    public int receiveFaultRepair(FaultRepair faultRepair);

    /**
     * 提交维修任务
     *
     * @param requestMap 请求参数
     * @return 结果
     */
    public Map<String, Object> submitFaultRepair(Map<String, Object> requestMap);

    /**
     * 审核维修任务
     *
     * @param faultRepair 维修任务
     * @return 结果
     */
    public int reviewFaultRepair(FaultRepair faultRepair);

    /**
     * 更新维修任务状态
     *
     * @param repairId 维修任务ID
     * @param status 状态
     * @return 结果
     */
    public int updateFaultRepairStatus(Long repairId, String status);

    /**
     * 创建维修任务（从故障创建）
     *
     * @param requestMap 请求参数
     * @return 结果
     */
    public Map<String, Object> createRepairFromFault(Map<String, Object> requestMap);

    /**
     * 查询待接收维修任务数量
     *
     * @param userId 用户ID
     * @return 待接收维修任务数量
     */
    public int selectPendingRepairCount(String userId);

    /**
     * 查询进行中维修任务数量
     *
     * @param userId 用户ID
     * @return 进行中维修任务数量
     */
    public int selectProcessingRepairCount(String userId);

    /**
     * 查询已完成维修任务数量
     *
     * @param userId 用户ID
     * @return 已完成维修任务数量
     */
    public int selectCompletedRepairCount(String userId);

    /**
     * 查询已审核维修任务数量
     *
     * @param userId 用户ID
     * @return 已审核维修任务数量
     */
    public int selectReviewedRepairCount(String userId);
}
