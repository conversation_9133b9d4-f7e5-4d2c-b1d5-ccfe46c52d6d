package com.ruoyi.project.system.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.framework.aspectj.lang.annotation.Excel;
import com.ruoyi.framework.web.domain.BaseEntity;

/**
 * 维修任务对象 fault_repair
 * 
 * <AUTHOR>
 * @date 2025-01-14
 */
public class FaultRepair extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 维修ID */
    private Long repairId;

    /** 故障ID */
    @Excel(name = "故障ID")
    private Long faultId;

    /** 电站ID */
    private Long stationId;

    /** 维修任务名称 */
    @Excel(name = "维修任务名称")
    private String repairName;

    /** 指派维修人员 */
    @Excel(name = "指派维修人员")
    private String assignUser;

    /** 指派负责人 */
    @Excel(name = "指派负责人")
    private String assignChargeUser;

    /** 实际维修人员 */
    @Excel(name = "实际维修人员")
    private String repairUser;

    /** 计划开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "计划开始时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date planStartTime;

    /** 计划结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "计划结束时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date planEndTime;

    /** 接收时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "接收时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date receiveTime;

    /** 维修方案 */
    @Excel(name = "维修方案")
    private String repairPlan;

    /** 更换器件记录 */
    @Excel(name = "更换器件记录")
    private String replacedParts;

    /** 维修前照片 */
    @Excel(name = "维修前照片")
    private String beforeImages;

    /** 维修后照片 */
    @Excel(name = "维修后照片")
    private String afterImages;

    /** 维修备注 */
    @Excel(name = "维修备注")
    private String repairRemark;

    /** 维修过程记录 */
    @Excel(name = "维修过程记录")
    private String repairProcess;

    /** 提交时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "提交时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date submitTime;

    /** 状态 0已下发 2已接收 4已提交 6已审核 */
    @Excel(name = "状态", dictType = "fault_repair_status")
    private String status;

    /** 审核人员 */
    @Excel(name = "审核人员")
    private String reviewUser;

    /** 审核时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "审核时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date reviewTime;

    /** 审核意见 */
    @Excel(name = "审核意见")
    private String reviewComment;

    /** 删除标志 */
    private String delFlag;

    /** 关联的故障信息 */
    private Fault fault;

    /** 关联的用户信息 */
    private SysUser user;

    /** 指派维修人员名称 */
    @Excel(name = "指派维修人员名称")
    private String assignUserName;

    /** 指派负责人名称 */
    @Excel(name = "指派负责人名称")
    private String assignChargeUserName;

    /** 实际维修人员名称 */
    @Excel(name = "实际维修人员名称")
    private String repairUserName;

    /** 审核人员名称 */
    @Excel(name = "审核人员名称")
    private String reviewUserName;

    /** 用户ID (筛选使用) */
    private String userId;

    public void setRepairId(Long repairId) 
    {
        this.repairId = repairId;
    }

    public Long getRepairId() 
    {
        return repairId;
    }

    public void setFaultId(Long faultId) 
    {
        this.faultId = faultId;
    }

    public Long getFaultId()
    {
        return faultId;
    }

    public void setStationId(Long stationId)
    {
        this.stationId = stationId;
    }

    public Long getStationId()
    {
        return stationId;
    }

    public void setRepairName(String repairName)
    {
        this.repairName = repairName;
    }

    public String getRepairName() 
    {
        return repairName;
    }

    public void setAssignUser(String assignUser) 
    {
        this.assignUser = assignUser;
    }

    public String getAssignUser() 
    {
        return assignUser;
    }

    public void setAssignChargeUser(String assignChargeUser) 
    {
        this.assignChargeUser = assignChargeUser;
    }

    public String getAssignChargeUser() 
    {
        return assignChargeUser;
    }

    public void setRepairUser(String repairUser) 
    {
        this.repairUser = repairUser;
    }

    public String getRepairUser() 
    {
        return repairUser;
    }

    public void setPlanStartTime(Date planStartTime) 
    {
        this.planStartTime = planStartTime;
    }

    public Date getPlanStartTime() 
    {
        return planStartTime;
    }

    public void setPlanEndTime(Date planEndTime) 
    {
        this.planEndTime = planEndTime;
    }

    public Date getPlanEndTime() 
    {
        return planEndTime;
    }

    public void setReceiveTime(Date receiveTime) 
    {
        this.receiveTime = receiveTime;
    }

    public Date getReceiveTime() 
    {
        return receiveTime;
    }

    public void setRepairPlan(String repairPlan) 
    {
        this.repairPlan = repairPlan;
    }

    public String getRepairPlan() 
    {
        return repairPlan;
    }

    public void setReplacedParts(String replacedParts) 
    {
        this.replacedParts = replacedParts;
    }

    public String getReplacedParts() 
    {
        return replacedParts;
    }

    public void setBeforeImages(String beforeImages) 
    {
        this.beforeImages = beforeImages;
    }

    public String getBeforeImages() 
    {
        return beforeImages;
    }

    public void setAfterImages(String afterImages) 
    {
        this.afterImages = afterImages;
    }

    public String getAfterImages() 
    {
        return afterImages;
    }

    public void setRepairRemark(String repairRemark)
    {
        this.repairRemark = repairRemark;
    }

    public String getRepairRemark()
    {
        return repairRemark;
    }

    public void setRepairProcess(String repairProcess)
    {
        this.repairProcess = repairProcess;
    }

    public String getRepairProcess()
    {
        return repairProcess;
    }

    public void setSubmitTime(Date submitTime)
    {
        this.submitTime = submitTime;
    }

    public Date getSubmitTime() 
    {
        return submitTime;
    }

    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    public void setReviewUser(String reviewUser) 
    {
        this.reviewUser = reviewUser;
    }

    public String getReviewUser() 
    {
        return reviewUser;
    }

    public void setReviewTime(Date reviewTime) 
    {
        this.reviewTime = reviewTime;
    }

    public Date getReviewTime() 
    {
        return reviewTime;
    }

    public void setReviewComment(String reviewComment) 
    {
        this.reviewComment = reviewComment;
    }

    public String getReviewComment() 
    {
        return reviewComment;
    }

    public void setDelFlag(String delFlag) 
    {
        this.delFlag = delFlag;
    }

    public String getDelFlag() 
    {
        return delFlag;
    }

    public void setFault(Fault fault) 
    {
        this.fault = fault;
    }

    public Fault getFault() 
    {
        return fault;
    }

    public void setUser(SysUser user) 
    {
        this.user = user;
    }

    public SysUser getUser() 
    {
        return user;
    }

    public void setAssignUserName(String assignUserName) 
    {
        this.assignUserName = assignUserName;
    }

    public String getAssignUserName() 
    {
        return assignUserName;
    }

    public void setAssignChargeUserName(String assignChargeUserName) 
    {
        this.assignChargeUserName = assignChargeUserName;
    }

    public String getAssignChargeUserName() 
    {
        return assignChargeUserName;
    }

    public void setRepairUserName(String repairUserName) 
    {
        this.repairUserName = repairUserName;
    }

    public String getRepairUserName() 
    {
        return repairUserName;
    }

    public void setReviewUserName(String reviewUserName) 
    {
        this.reviewUserName = reviewUserName;
    }

    public String getReviewUserName()
    {
        return reviewUserName;
    }

    public void setUserId(String userId)
    {
        this.userId = userId;
    }

    public String getUserId()
    {
        return userId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("repairId", getRepairId())
            .append("faultId", getFaultId())
            .append("repairName", getRepairName())
            .append("assignUser", getAssignUser())
            .append("assignChargeUser", getAssignChargeUser())
            .append("repairUser", getRepairUser())
            .append("planStartTime", getPlanStartTime())
            .append("planEndTime", getPlanEndTime())
            .append("receiveTime", getReceiveTime())
            .append("repairPlan", getRepairPlan())
            .append("replacedParts", getReplacedParts())
            .append("beforeImages", getBeforeImages())
            .append("afterImages", getAfterImages())
            .append("repairRemark", getRepairRemark())
            .append("repairProcess", getRepairProcess())
            .append("submitTime", getSubmitTime())
            .append("status", getStatus())
            .append("reviewUser", getReviewUser())
            .append("reviewTime", getReviewTime())
            .append("reviewComment", getReviewComment())
            .append("delFlag", getDelFlag())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
