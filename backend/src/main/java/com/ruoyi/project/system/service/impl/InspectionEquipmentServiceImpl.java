package com.ruoyi.project.system.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.project.system.mapper.InspectionEquipmentMapper;
import com.ruoyi.project.system.domain.InspectionEquipment;
import com.ruoyi.project.system.service.IInspectionEquipmentService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 巡检和设备关联Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-29
 */
@Service
public class InspectionEquipmentServiceImpl implements IInspectionEquipmentService
{
    @Autowired
    private InspectionEquipmentMapper inspectionEquipmentMapper;

    /**
     * 查询巡检和设备关联
     *
     * @param inspectionId 巡检和设备关联主键
     * @return 巡检和设备关联
     */
    @Override
    public InspectionEquipment selectInspectionEquipmentByInspectionId(Long inspectionId)
    {
        return inspectionEquipmentMapper.selectInspectionEquipmentByInspectionId(inspectionId);
    }

    /**
     * 查询巡检和设备关联列表
     *
     * @param inspectionEquipment 巡检和设备关联
     * @return 巡检和设备关联
     */
    @Override
    public List<InspectionEquipment> selectInspectionEquipmentList(InspectionEquipment inspectionEquipment)
    {
        return inspectionEquipmentMapper.selectInspectionEquipmentList(inspectionEquipment);
    }

    /**
     * 新增巡检和设备关联
     *
     * @param inspectionEquipment 巡检和设备关联
     * @return 结果
     */
    @Override
    public int insertInspectionEquipment(InspectionEquipment inspectionEquipment)
    {
        return inspectionEquipmentMapper.insertInspectionEquipment(inspectionEquipment);
    }

    /**
     * 修改巡检和设备关联
     *
     * @param inspectionEquipment 巡检和设备关联
     * @return 结果
     */
    @Override
    public int updateInspectionEquipment(InspectionEquipment inspectionEquipment)
    {
        return inspectionEquipmentMapper.updateInspectionEquipment(inspectionEquipment);
    }

    /**
     * 批量删除巡检和设备关联
     *
     * @param inspectionIds 需要删除的巡检和设备关联主键
     */
    @Override
    public void deleteInspectionEquipmentByInspectionIds(Long[] inspectionIds)
    {
        inspectionEquipmentMapper.deleteInspectionEquipmentByInspectionIds(inspectionIds);
    }

    /**
     * 删除巡检和设备关联信息
     *
     * @param inspectionId 巡检和设备关联主键
     * @return 结果
     */
    @Override
    public int deleteInspectionEquipmentByInspectionId(Long inspectionId)
    {
        return inspectionEquipmentMapper.deleteInspectionEquipmentByInspectionId(inspectionId);
    }

    /**
     * 批量保存巡检和设备关联
     *
     * @param inspectionId 巡检ID
     * @param equipments 设备列表
     * @return 结果
     */
    @Override
    @Transactional
    public int batchSaveInspectionEquipments(Long inspectionId, List<InspectionEquipment> equipments)
    {
        // 先删除原有关联
        inspectionEquipmentMapper.deleteInspectionEquipmentByInspectionId(inspectionId);

        // 批量插入新关联
        int rows = 0;
        if (equipments != null && !equipments.isEmpty())
        {
            for (InspectionEquipment equipment : equipments)
            {
                equipment.setInspectionId(inspectionId);
                rows += inspectionEquipmentMapper.insertInspectionEquipment(equipment);
            }
        }
        return rows;
    }
}
