package com.ruoyi.project.system.utils;

import com.alibaba.fastjson2.JSON;

import java.util.*;

/**
 * 设备类型预设检查项配置工具类
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
public class InspectionPresetUtils {

    /**
     * 设备类型代码：
     * 0 - 组件
     * 1 - 逆变器
     * 2 - 直流电缆
     * 4 - 桥架
     * 5 - 支架
     * 6 - 消防系统
     * 7 - 并网柜
     * 8 - 汇流箱
     * 9 - 交流电缆
     * 10 - 变压器
     * 11 - 开关柜
     * 12 - 箱变
     * 13 - 气象仪
     * 14 - 接地系统
     * 15 - 计量表
     */

    // 设备类型预设检查项配置
    private static final Map<String, List<Map<String, Object>>> INSPECTION_ITEMS_CONFIG = new HashMap<>();

    static {
        // 组件
        INSPECTION_ITEMS_CONFIG.put("0", Arrays.asList(
                createInspectionItem(1, "组件是否松动，检查中压边压螺栓紧固度", "radio", Arrays.asList("合格", "不合格")),
                createInspectionItem(2, "无破损或明显变形", "radio", Arrays.asList("合格", "不合格")),
                createInspectionItem(3, "组件表面有无遮挡物及积灰程度", "radio", Arrays.asList("合格", "不合格")),
                createInspectionItem(4, "组件是否存在热斑现象，最高温度<90℃", "radio", Arrays.asList("合格", "不合格"))
        ));

        // 逆变器
        INSPECTION_ITEMS_CONFIG.put("1", Arrays.asList(
                createInspectionItem(1, "安装牢固、可靠，柜体无明显变形", "radio", Arrays.asList("合格", "不合格")),
                createInspectionItem(2, "散热是否良好", "radio", Arrays.asList("合格", "不合格")),
                createInspectionItem(3, "MC4插头有无松动、锈蚀、灼烧痕迹", "radio", Arrays.asList("合格", "不合格")),
                createInspectionItem(4, "接地线连接是否松动、断开", "radio", Arrays.asList("合格", "不合格")),
                createInspectionItem(5, "通讯是否正常连接", "radio", Arrays.asList("合格", "不合格")),
                createInspectionItem(6, "是否有告警", "radio", Arrays.asList("合格", "不合格")),
                createInspectionItem(7, "逆变器电压500-1100v", "radio", Arrays.asList("合格", "不合格")),
                createInspectionItem(8, "逆变器电流<240A", "radio", Arrays.asList("合格", "不合格")),
                createInspectionItem(9, "逆变器功率<110kw", "radio", Arrays.asList("合格", "不合格"))
        ));

        // 直流电缆
        INSPECTION_ITEMS_CONFIG.put("2", Arrays.asList(
                createInspectionItem(1, "电缆连接是否松动、否老化、破皮", "radio", Arrays.asList("合格", "不合格")),
                createInspectionItem(2, "最高温度是否<80℃", "radio", Arrays.asList("合格", "不合格")),
                createInspectionItem(3, "组件侧MC4插头有无松动、锈蚀、灼烧痕迹", "radio", Arrays.asList("合格", "不合格"))
        ));

        // 桥架
        INSPECTION_ITEMS_CONFIG.put("4", Arrays.asList(
                createInspectionItem(1, "桥架盖板有无脱落", "radio", Arrays.asList("合格", "不合格")),
                createInspectionItem(2, "桥架固定是否牢固", "radio", Arrays.asList("合格", "不合格")),
                createInspectionItem(3, "连接地线是否有锈蚀，螺栓是否松动", "radio", Arrays.asList("合格", "不合格"))
        ));

        // 支架
        INSPECTION_ITEMS_CONFIG.put("5", Arrays.asList(
                createInspectionItem(1, "支架是否牢固、可靠，螺丝是否松动", "radio", Arrays.asList("合格", "不合格")),
                createInspectionItem(2, "支架固定处屋面做防水处理是否存在老化", "radio", Arrays.asList("合格", "不合格")),
                createInspectionItem(3, "支架是否有锈蚀情况", "radio", Arrays.asList("合格", "不合格"))
        ));

        // 消防系统
        INSPECTION_ITEMS_CONFIG.put("6", Arrays.asList(
                createInspectionItem(1, "灭火器箱无损坏", "radio", Arrays.asList("合格", "不合格")),
                createInspectionItem(2, "灭火器日期合格", "radio", Arrays.asList("合格", "不合格")),
                createInspectionItem(3, "填写灭火器巡检卡", "radio", Arrays.asList("合格", "不合格"))
        ));

        // 并网柜
        INSPECTION_ITEMS_CONFIG.put("7", Arrays.asList(
                createInspectionItem(1, "断路器状态处于合位", "radio", Arrays.asList("合格", "不合格")),
                createInspectionItem(2, "电缆终端连接处温度<80℃", "radio", Arrays.asList("合格", "不合格")),
                createInspectionItem(3, "交流电压<400v", "radio", Arrays.asList("合格", "不合格")),
                createInspectionItem(4, "电流<240A", "radio", Arrays.asList("合格", "不合格")),
                createInspectionItem(5, "柜体无明显变形、柜内无灼烧痕迹", "radio", Arrays.asList("合格", "不合格"))
        ));

        // 汇流箱
        INSPECTION_ITEMS_CONFIG.put("8", Arrays.asList(
                createInspectionItem(1, "汇流箱外观检查", "radio", Arrays.asList("合格", "不合格")),
                createInspectionItem(2, "箱体密封性", "radio", Arrays.asList("合格", "不合格")),
                createInspectionItem(3, "熔断器状态", "radio", Arrays.asList("合格", "不合格")),
                createInspectionItem(4, "防雷器状态", "radio", Arrays.asList("合格", "不合格")),
                createInspectionItem(5, "接线端子紧固度", "radio", Arrays.asList("合格", "不合格")),
                createInspectionItem(6, "输出电流测试值", "text", null)
        ));

        // 交流电缆
        INSPECTION_ITEMS_CONFIG.put("9", Arrays.asList(
                createInspectionItem(1, "电缆连接是否松动、老化、破皮", "radio", Arrays.asList("合格", "不合格")),
                createInspectionItem(2, "最高温度是否<80℃", "radio", Arrays.asList("合格", "不合格"))
        ));

        // 变压器
        INSPECTION_ITEMS_CONFIG.put("10", Arrays.asList(
                createInspectionItem(1, "变压器无异响", "radio", Arrays.asList("合格", "不合格")),
                createInspectionItem(2, "变压器温度<100℃", "radio", Arrays.asList("合格", "不合格"))
        ));

        // 开关柜
        INSPECTION_ITEMS_CONFIG.put("11", Arrays.asList(
                createInspectionItem(1, "开关柜外观状态", "radio", Arrays.asList("合格", "不合格")),
                createInspectionItem(2, "柜内清洁度", "radio", Arrays.asList("合格", "不合格")),
                createInspectionItem(3, "开关操作机构", "radio", Arrays.asList("合格", "不合格")),
                createInspectionItem(4, "指示仪表状态", "radio", Arrays.asList("合格", "不合格")),
                createInspectionItem(5, "接地状态", "radio", Arrays.asList("合格", "不合格"))
        ));

        // 箱变
        INSPECTION_ITEMS_CONFIG.put("12", Arrays.asList(
                createInspectionItem(1, "无明显变形，无掉漆锈蚀", "radio", Arrays.asList("合格", "不合格")),
                createInspectionItem(2, "变压器箱体门可以正常打开关闭，无渗水、漏水情况", "radio", Arrays.asList("合格", "不合格")),
                createInspectionItem(3, "围栏无损坏，无倒塌，可以实现阻拦作用", "radio", Arrays.asList("合格", "不合格"))
        ));

        // 气象仪
        INSPECTION_ITEMS_CONFIG.put("13", Arrays.asList(
                createInspectionItem(1, "气象仪外观状态", "radio", Arrays.asList("合格", "不合格")),
                createInspectionItem(2, "传感器清洁度", "radio", Arrays.asList("合格", "不合格")),
                createInspectionItem(3, "数据显示状态", "radio", Arrays.asList("合格", "不合格")),
                createInspectionItem(4, "当前辐照度", "text", null),
                createInspectionItem(5, "环境温度", "text", null),
                createInspectionItem(6, "风速", "text", null)
        ));

        // 接地系统
        INSPECTION_ITEMS_CONFIG.put("14", Arrays.asList(
                createInspectionItem(1, "接地扁铁连接是否正常，连接螺栓无松动", "radio", Arrays.asList("合格", "不合格")),
                createInspectionItem(2, "支架连接小黄线有无锈蚀、线鼻脱落、螺栓松动", "radio", Arrays.asList("合格", "不合格"))
        ));

        // 计量表
        INSPECTION_ITEMS_CONFIG.put("15", Arrays.asList(
                createInspectionItem(1, "电表无报警", "radio", Arrays.asList("合格", "不合格"))
        ));
    }

    /**
     * 创建检查项
     */
    private static Map<String, Object> createInspectionItem(int id, String content, String type, List<String> options) {
        Map<String, Object> item = new HashMap<>();
        item.put("id", id);
        item.put("content", content);
        item.put("type", type);
        if (options != null) {
            item.put("options", options);
        }
        return item;
    }

    /**
     * 根据设备类型获取预设检查项
     *
     * @param equipmentType 设备类型
     * @return 检查项列表
     */
    public static List<Map<String, Object>> getInspectionItemsByType(String equipmentType) {
        List<Map<String, Object>> items = INSPECTION_ITEMS_CONFIG.get(equipmentType);
        if (items == null) {
            // 如果没有找到对应的设备类型，返回空列表
            return new ArrayList<>();
        }
        return new ArrayList<>(items);
    }

    /**
     * 根据设备类型生成巡检规则JSON字符串
     *
     * @param equipmentType 设备类型
     * @return JSON字符串
     */
    public static String generateInspectionRuleJson(String equipmentType) {
        List<Map<String, Object>> items = getInspectionItemsByType(equipmentType);
        return JSON.toJSONString(items);
    }
}
