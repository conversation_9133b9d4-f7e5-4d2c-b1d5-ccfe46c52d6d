package com.ruoyi.project.system.service;

import java.util.List;
import com.ruoyi.project.system.domain.Equipment;

/**
 * 设备信息Service接口
 * 
 * <AUTHOR>
 * @date 2025-04-23
 */
public interface IEquipmentService 
{
    /**
     * 查询设备信息
     * 
     * @param equipmentId 设备信息主键
     * @return 设备信息
     */
    public Equipment selectEquipmentByEquipmentId(Long equipmentId);

    /**
     * 查询设备信息列表
     * 
     * @param equipment 设备信息
     * @return 设备信息集合
     */
    public List<Equipment> selectEquipmentList(Equipment equipment);

    /**
     * 新增设备信息
     * 
     * @param equipment 设备信息
     * @return 结果
     */
    public int insertEquipment(Equipment equipment);

    /**
     * 修改设备信息
     * 
     * @param equipment 设备信息
     * @return 结果
     */
    public int updateEquipment(Equipment equipment);

    /**
     * 批量删除设备信息
     * 
     * @param equipmentIds 需要删除的设备信息主键集合
     * @return 结果
     */
    public int deleteEquipmentByEquipmentIds(Long[] equipmentIds);

    /**
     * 删除设备信息信息
     *
     * @param equipmentId 设备信息主键
     * @return 结果
     */
    public int deleteEquipmentByEquipmentId(Long equipmentId);

    /**
     * 导入设备信息数据
     *
     * @param equipmentList 设备信息数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @param stationId 指定的电站ID，如果提供则覆盖Excel中的电站ID
     * @return 结果
     */
    public String importEquipment(List<Equipment> equipmentList, Boolean isUpdateSupport, String operName, Long stationId);
}
