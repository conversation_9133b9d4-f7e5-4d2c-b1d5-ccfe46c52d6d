package com.ruoyi.project.system.service;

import java.util.List;
import com.ruoyi.project.system.domain.MeterReadingEquipment;

/**
 * 电量统计和设备关联Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */
public interface IMeterReadingEquipmentService 
{
    /**
     * 查询电量统计和设备关联
     * 
     * @param meterReadingId 电量统计ID
     * @param equipmentId 设备ID
     * @return 电量统计和设备关联
     */
    public MeterReadingEquipment selectMeterReadingEquipmentByIds(Long meterReadingId, Long equipmentId);

    /**
     * 查询电量统计任务关联的设备列表
     * 
     * @param meterReadingId 电量统计任务ID
     * @return 电量统计和设备关联集合
     */
    public List<MeterReadingEquipment> selectMeterReadingEquipmentList(Long meterReadingId);

    /**
     * 新增电量统计和设备关联
     * 
     * @param meterReadingEquipment 电量统计和设备关联
     * @return 结果
     */
    public int insertMeterReadingEquipment(MeterReadingEquipment meterReadingEquipment);

    /**
     * 修改电量统计和设备关联
     * 
     * @param meterReadingEquipment 电量统计和设备关联
     * @return 结果
     */
    public int updateMeterReadingEquipment(MeterReadingEquipment meterReadingEquipment);

    /**
     * 删除电量统计和设备关联
     * 
     * @param meterReadingId 电量统计ID
     * @param equipmentId 设备ID
     * @return 结果
     */
    public int deleteMeterReadingEquipmentByIds(Long meterReadingId, Long equipmentId);

    /**
     * 批量删除电量统计和设备关联
     * 
     * @param meterReadingId 电量统计任务ID
     * @return 结果
     */
    public int deleteMeterReadingEquipmentByMeterReadingId(Long meterReadingId);

    /**
     * 批量保存电量统计和设备关联
     *
     * @param meterReadingId 电量统计ID
     * @param equipments 设备列表
     * @return 结果
     */
    public int batchSaveMeterReadingEquipments(Long meterReadingId, List<MeterReadingEquipment> equipments);
}
