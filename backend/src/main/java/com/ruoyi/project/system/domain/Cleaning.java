package com.ruoyi.project.system.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.framework.aspectj.lang.annotation.Excel;
import com.ruoyi.framework.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 组件清洗任务对象 cleaning
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
public class Cleaning extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 清洗任务ID
     */
    private Long cleaningId;

    /**
     * 清洗任务名称
     */
    @Excel(name = "清洗任务名称")
    private String cleaningName;

    /**
     * 电站ID
     */
    private Long stationId;

    /**
     * 计划开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "计划开始时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date planStartTime;

    /**
     * 计划结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "计划结束时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date planEndTime;

    /**
     * 设定清洗人员
     */
    private String assignUser;

    /**
     * 设定负责人
     */
    private String assignChargeUser;

    /**
     * 设定清洗人员名称
     */
    @Excel(name = "设定清洗人员名称")
    private String assignUserName;

    /**
     * 设定负责人名称
     */
    @Excel(name = "设定负责人名称")
    private String assignChargeUserName;

    /**
     * 实际清洗人员
     */
    private String cleaningUser;

    /**
     * 实际清洗人员名称
     */
    @Excel(name = "实际清洗人员名称")
    private String cleaningUserName;

    /**
     * 接收时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Excel(name = "接收时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date receiveTime;

    /**
     * 提交时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Excel(name = "提交时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date submitTime;

    /**
     * 审核时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Excel(name = "审核时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date reviewTime;

    /**
     * 审核备注
     */
    @Excel(name = "审核备注")
    private String reviewRemark;

    /**
     * 状态 0已下发 2已接收 4已提交 6已审核
     */
    @Excel(name = "状态", dictType = "cleaning_status")
    private String status;

    /**
     * 删除标志 0存在 2删除
     */
    private String delFlag;
    /**
     * 关联的电站信息
     */
    private Station station;
    /**
     * 关联的用户信息
     */
    private SysUser user;

    public Long getCleaningId() {
        return cleaningId;
    }

    public void setCleaningId(Long cleaningId) {
        this.cleaningId = cleaningId;
    }

    public String getCleaningName() {
        return cleaningName;
    }

    public void setCleaningName(String cleaningName) {
        this.cleaningName = cleaningName;
    }

    public Long getStationId() {
        return stationId;
    }

    public void setStationId(Long stationId) {
        this.stationId = stationId;
    }

    public Date getPlanStartTime() {
        return planStartTime;
    }

    public void setPlanStartTime(Date planStartTime) {
        this.planStartTime = planStartTime;
    }

    public Date getPlanEndTime() {
        return planEndTime;
    }

    public void setPlanEndTime(Date planEndTime) {
        this.planEndTime = planEndTime;
    }

    public String getAssignUser() {
        return assignUser;
    }

    public void setAssignUser(String assignUser) {
        this.assignUser = assignUser;
    }

    public String getAssignChargeUser() {
        return assignChargeUser;
    }

    public void setAssignChargeUser(String assignChargeUser) {
        this.assignChargeUser = assignChargeUser;
    }

    public String getAssignUserName() {
        return assignUserName;
    }

    public void setAssignUserName(String assignUserName) {
        this.assignUserName = assignUserName;
    }

    public String getAssignChargeUserName() {
        return assignChargeUserName;
    }

    public void setAssignChargeUserName(String assignChargeUserName) {
        this.assignChargeUserName = assignChargeUserName;
    }

    public String getCleaningUser() {
        return cleaningUser;
    }

    public void setCleaningUser(String cleaningUser) {
        this.cleaningUser = cleaningUser;
    }

    public String getCleaningUserName() {
        return cleaningUserName;
    }

    public void setCleaningUserName(String cleaningUserName) {
        this.cleaningUserName = cleaningUserName;
    }

    public Date getReceiveTime() {
        return receiveTime;
    }

    public void setReceiveTime(Date receiveTime) {
        this.receiveTime = receiveTime;
    }

    public Date getSubmitTime() {
        return submitTime;
    }

    public void setSubmitTime(Date submitTime) {
        this.submitTime = submitTime;
    }

    public Date getReviewTime() {
        return reviewTime;
    }

    public void setReviewTime(Date reviewTime) {
        this.reviewTime = reviewTime;
    }

    public String getReviewRemark() {
        return reviewRemark;
    }

    public void setReviewRemark(String reviewRemark) {
        this.reviewRemark = reviewRemark;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    public Station getStation() {
        return station;
    }

    public void setStation(Station station) {
        this.station = station;
    }

    public SysUser getUser() {
        return user;
    }

    public void setUser(SysUser user) {
        this.user = user;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("cleaningId", getCleaningId())
                .append("cleaningName", getCleaningName())
                .append("stationId", getStationId())
                .append("planStartTime", getPlanStartTime())
                .append("planEndTime", getPlanEndTime())
                .append("assignUser", getAssignUser())
                .append("assignChargeUser", getAssignChargeUser())
                .append("assignUserName", getAssignUserName())
                .append("assignChargeUserName", getAssignChargeUserName())
                .append("cleaningUser", getCleaningUser())
                .append("cleaningUserName", getCleaningUserName())
                .append("receiveTime", getReceiveTime())
                .append("submitTime", getSubmitTime())
                .append("reviewTime", getReviewTime())
                .append("reviewRemark", getReviewRemark())
                .append("status", getStatus())
                .append("delFlag", getDelFlag())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("remark", getRemark())
                .toString();
    }
}
