package com.ruoyi.project.system.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.framework.aspectj.lang.annotation.Excel;
import com.ruoyi.framework.web.domain.BaseEntity;

/**
 * 设备信息对象 equipment
 *
 * <AUTHOR>
 * @date 2025-04-23
 */
public class Equipment extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 设备ID */
    @Excel(name = "设备ID", type = Excel.Type.EXPORT)
    private Long equipmentId;

    /**
     * 所属电站ID
     * 为0时表示闲置
     */
    @Excel(name = "所属电站ID", type = Excel.Type.EXPORT)
    private Long stationId;

    /** 设备名称 */
    @Excel(name = "设备名称")
    private String equipmentName;

    /** 设备类型 */
    @Excel(name = "设备类型", dictType = "equipment_type")
    private String equipmentType;

    /** 设备编号 */
    @Excel(name = "设备编号")
    private String equipmentCode;

    /** 生产厂家 */
    @Excel(name = "生产厂家")
    private String manufacturer;

    /** 设备型号 */
    @Excel(name = "设备型号")
    private String model;

    /** 设备数量 */
    @Excel(name = "设备数量")
    private String quantity;

    /** 附件路径 */
    @Excel(name = "附件路径", type = Excel.Type.EXPORT)
    private String attachments;

    /** 巡检规则 */
    @Excel(name = "巡检规则", type = Excel.Type.EXPORT)
    private String inspectionRule;

    /** 设备状态 0正常 1停用 */
    @Excel(name = "设备状态", dictType = "equipment_status", type = Excel.Type.EXPORT)
    private String status;

    /** 删除标志 0存在 2删除 */
    private String delFlag;

    /** 所属电站 */
    @Excel(name = "所属电站", targetAttr = "stationName", type = Excel.Type.EXPORT)
    private Station station;

    /** 备注 */
    @Excel(name = "备注")
    private String remark;

    public void setEquipmentId(Long equipmentId)
    {
        this.equipmentId = equipmentId;
    }

    public Long getEquipmentId()
    {
        return equipmentId;
    }

    public void setStationId(Long stationId)
    {
        this.stationId = stationId;
    }

    public Long getStationId()
    {
        return stationId;
    }

    public void setEquipmentCode(String equipmentCode)
    {
        this.equipmentCode = equipmentCode;
    }

    public String getEquipmentCode()
    {
        return equipmentCode;
    }

    public void setEquipmentName(String equipmentName)
    {
        this.equipmentName = equipmentName;
    }

    public String getEquipmentName()
    {
        return equipmentName;
    }

    public void setEquipmentType(String equipmentType)
    {
        this.equipmentType = equipmentType;
    }

    public String getEquipmentType()
    {
        return equipmentType;
    }

    public void setManufacturer(String manufacturer)
    {
        this.manufacturer = manufacturer;
    }

    public String getManufacturer()
    {
        return manufacturer;
    }

    public void setModel(String model)
    {
        this.model = model;
    }

    public String getModel()
    {
        return model;
    }

    public void setQuantity(String quantity)
    {
        this.quantity = quantity;
    }

    public String getQuantity()
    {
        return quantity;
    }

    public void setAttachments(String attachments)
    {
        this.attachments = attachments;
    }

    public String getAttachments()
    {
        return attachments;
    }

    public void setInspectionRule(String inspectionRule)
    {
        this.inspectionRule = inspectionRule;
    }

    public String getInspectionRule()
    {
        return inspectionRule;
    }

    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getStatus()
    {
        return status;
    }

    public void setDelFlag(String delFlag)
    {
        this.delFlag = delFlag;
    }

    public String getDelFlag()
    {
        return delFlag;
    }

    public void setStation(Station station)
    {
        this.station = station;
    }

    public Station getStation()
    {
        return station;
    }

    public void setRemark(String remark)
    {
        this.remark = remark;
    }

    public String getRemark()
    {
        return remark;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("equipmentId", getEquipmentId())
            .append("stationId", getStationId())
            .append("equipmentCode", getEquipmentCode())
            .append("equipmentName", getEquipmentName())
            .append("equipmentType", getEquipmentType())
            .append("manufacturer", getManufacturer())
            .append("model", getModel())
            .append("quantity", getQuantity())
            .append("attachments", getAttachments())
            .append("inspectionRule", getInspectionRule())
            .append("status", getStatus())
            .append("delFlag", getDelFlag())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .append("station", getStation())
            .toString();
    }
}
