package com.ruoyi.project.system.service.impl;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.project.system.mapper.FaultMapper;
import com.ruoyi.project.system.domain.Fault;
import com.ruoyi.project.system.service.IFaultService;

/**
 * 故障信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-14
 */
@Service
public class FaultServiceImpl implements IFaultService 
{
    @Autowired
    private FaultMapper faultMapper;

    /**
     * 查询故障信息
     * 
     * @param faultId 故障信息主键
     * @return 故障信息
     */
    @Override
    public Fault selectFaultByFaultId(Long faultId)
    {
        return faultMapper.selectFaultByFaultId(faultId);
    }

    /**
     * 查询故障信息列表
     * 
     * @param fault 故障信息
     * @return 故障信息
     */
    @Override
    public List<Fault> selectFaultList(Fault fault)
    {
        return faultMapper.selectFaultList(fault);
    }

    /**
     * 新增故障信息
     *
     * @param fault 故障信息
     * @return 结果
     */
    @Override
    public int insertFault(Fault fault)
    {
        fault.setCreateBy(SecurityUtils.getUsername());
        fault.setCreateTime(DateUtils.getNowDate());
        fault.setDelFlag("0"); // 设置删除标志为未删除
        
        // 设置默认状态为待确认
        if (fault.getStatus() == null) {
            fault.setStatus("0");
        }
        
        // 设置发现时间为当前时间（如果未设置）
        if (fault.getDiscoverTime() == null) {
            fault.setDiscoverTime(DateUtils.getNowDate());
        }
        
        return faultMapper.insertFault(fault);
    }

    /**
     * 修改故障信息
     * 
     * @param fault 故障信息
     * @return 结果
     */
    @Override
    public int updateFault(Fault fault)
    {
        fault.setUpdateBy(SecurityUtils.getUsername());
        fault.setUpdateTime(DateUtils.getNowDate());
        return faultMapper.updateFault(fault);
    }

    /**
     * 批量删除故障信息
     * 
     * @param faultIds 需要删除的故障信息主键
     * @return 结果
     */
    @Override
    public int deleteFaultByFaultIds(Long[] faultIds)
    {
        return faultMapper.deleteFaultByFaultIds(faultIds);
    }

    /**
     * 删除故障信息
     *
     * @param faultId 故障信息主键
     * @return 结果
     */
    @Override
    public int deleteFaultByFaultId(Long faultId)
    {
        return faultMapper.deleteFaultByFaultId(faultId);
    }

    /**
     * App查询故障信息列表
     *
     * @param fault 故障信息
     * @return 故障信息集合
     */
    @Override
    public List<Fault> selectFaultAppList(Fault fault) {
        return faultMapper.selectFaultAppList(fault);
    }

    /**
     * 查询故障信息统计
     *
     * @param userId 用户ID
     * @return 统计信息
     */
    @Override
    public Map<String, Object> selectFaultStats(String userId) {
        return faultMapper.selectFaultStats(userId);
    }

    /**
     * 根据电站ID查询故障信息列表
     *
     * @param stationId 电站ID
     * @return 故障信息集合
     */
    @Override
    public List<Fault> selectFaultListByStationId(Long stationId) {
        return faultMapper.selectFaultListByStationId(stationId);
    }

    /**
     * 根据设备ID查询故障信息列表
     *
     * @param equipmentId 设备ID
     * @return 故障信息集合
     */
    @Override
    public List<Fault> selectFaultListByEquipmentId(Long equipmentId) {
        return faultMapper.selectFaultListByEquipmentId(equipmentId);
    }

    /**
     * 根据故障来源查询故障信息列表
     *
     * @param faultSource 故障来源
     * @return 故障信息集合
     */
    @Override
    public List<Fault> selectFaultListBySource(String faultSource) {
        return faultMapper.selectFaultListBySource(faultSource);
    }

    /**
     * 审核故障信息
     *
     * @param fault 故障信息
     * @return 结果
     */
    @Override
    @Transactional
    public int reviewFault(Fault fault) {
        fault.setReviewTime(DateUtils.getNowDate());
        fault.setUpdateBy(SecurityUtils.getUsername());
        fault.setUpdateTime(DateUtils.getNowDate());

        // 支持确认(4)和驳回(2)两种审核结果
        if ("4".equals(fault.getStatus())) {
            fault.setStatus("4"); // 已确认
        } else if ("2".equals(fault.getStatus())) {
            fault.setStatus("2"); // 已驳回
        } else {
            fault.setStatus("4"); // 默认为已确认
        }

        return faultMapper.updateFault(fault);
    }

    /**
     * 更新故障状态
     *
     * @param faultId 故障ID
     * @param status 状态
     * @return 结果
     */
    @Override
    public int updateFaultStatus(Long faultId, String status) {
        Fault fault = new Fault();
        fault.setFaultId(faultId);
        fault.setStatus(status);
        fault.setUpdateBy(SecurityUtils.getUsername());
        fault.setUpdateTime(DateUtils.getNowDate());
        return faultMapper.updateFault(fault);
    }

    /**
     * 故障上报（从巡检/清洗发现）
     *
     * @param requestMap 请求参数
     * @return 结果
     */
    @Override
    @Transactional
    public Map<String, Object> reportFault(Map<String, Object> requestMap) {
        Map<String, Object> result = new HashMap<>();
        try {
            Fault fault = new Fault();
            fault.setStationId(Long.valueOf(requestMap.get("stationId").toString()));
            fault.setEquipmentListId((String) requestMap.get("equipmentListId"));
            fault.setFaultName((String) requestMap.get("faultName"));
            fault.setFaultType((String) requestMap.get("faultType"));
            fault.setFaultLevel((String) requestMap.get("faultLevel"));
            fault.setFaultDescription((String) requestMap.get("faultDescription"));
            fault.setFaultImages((String) requestMap.get("faultImages"));
            fault.setFaultSource((String) requestMap.get("faultSource"));
            fault.setDiscoverUserId(Long.valueOf(requestMap.get("discoverUserId").toString()));

            // 处理发现时间
            if (requestMap.get("discoverTime") != null && !requestMap.get("discoverTime").toString().isEmpty()) {
                try {
                    String discoverTimeStr = requestMap.get("discoverTime").toString();
                    Date discoverTime = DateUtils.parseDate(discoverTimeStr, "yyyy-MM-dd");
                    fault.setDiscoverTime(discoverTime);
                } catch (Exception e) {
                    fault.setDiscoverTime(DateUtils.getNowDate());
                }
            } else {
                fault.setDiscoverTime(DateUtils.getNowDate());
            }

            fault.setStatus("0"); // 待确认

            int rows = insertFault(fault);

            result.put("success", rows > 0);
            result.put("message", rows > 0 ? "故障上报成功" : "故障上报失败");
            result.put("faultId", fault.getFaultId());

        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "故障上报失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 查询待处理故障数量
     *
     * @param userId 用户ID
     * @return 待处理故障数量
     */
    @Override
    public int selectPendingFaultCount(String userId) {
        return faultMapper.selectPendingFaultCount(userId);
    }

    /**
     * 查询处理中故障数量
     *
     * @param userId 用户ID
     * @return 处理中故障数量
     */
    @Override
    public int selectProcessingFaultCount(String userId) {
        return faultMapper.selectProcessingFaultCount(userId);
    }

    /**
     * 查询已完成故障数量
     *
     * @param userId 用户ID
     * @return 已完成故障数量
     */
    @Override
    public int selectCompletedFaultCount(String userId) {
        return faultMapper.selectCompletedFaultCount(userId);
    }
}
