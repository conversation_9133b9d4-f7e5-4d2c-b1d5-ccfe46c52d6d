package com.ruoyi.project.system.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.framework.aspectj.lang.annotation.Excel;
import com.ruoyi.framework.web.domain.BaseEntity;

/**
 * 电量统计和设备关联对象 meter_reading_equipment
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
public class MeterReadingEquipment extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 电量统计ID */
    private Long meterReadingId;

    /** 设备ID */
    private Long equipmentId;

    /** 电量统计人员ID */
    @Excel(name = "电量统计人员ID")
    private Long userId;

    /** 上一月反向有功 */
    @Excel(name = "上一月反向有功")
    private java.math.BigDecimal lastMonthReverseActive;

    /** 反向有功总 */
    @Excel(name = "反向有功总")
    private java.math.BigDecimal totalReverseActive;

    /** 照片 */
    @Excel(name = "照片")
    private String photos;

    /** 电量统计时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "电量统计时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date meterReadingTime;

    /** 电量统计备注 */
    @Excel(name = "电量统计备注")
    private String meterReadingRemark;



    /**
     * 关联的设备信息
     */
    private Equipment equipment;

    /**
     * 关联的电量统计任务信息
     */
    private MeterReading meterReading;

    public Long getMeterReadingId() {
        return meterReadingId;
    }

    public void setMeterReadingId(Long meterReadingId) {
        this.meterReadingId = meterReadingId;
    }

    public Long getEquipmentId() {
        return equipmentId;
    }

    public void setEquipmentId(Long equipmentId) {
        this.equipmentId = equipmentId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public java.math.BigDecimal getLastMonthReverseActive() {
        return lastMonthReverseActive;
    }

    public void setLastMonthReverseActive(java.math.BigDecimal lastMonthReverseActive) {
        this.lastMonthReverseActive = lastMonthReverseActive;
    }

    public java.math.BigDecimal getTotalReverseActive() {
        return totalReverseActive;
    }

    public void setTotalReverseActive(java.math.BigDecimal totalReverseActive) {
        this.totalReverseActive = totalReverseActive;
    }

    public String getPhotos() {
        return photos;
    }

    public void setPhotos(String photos) {
        this.photos = photos;
    }

    public Date getMeterReadingTime() {
        return meterReadingTime;
    }

    public void setMeterReadingTime(Date meterReadingTime) {
        this.meterReadingTime = meterReadingTime;
    }

    public String getMeterReadingRemark() {
        return meterReadingRemark;
    }

    public void setMeterReadingRemark(String meterReadingRemark) {
        this.meterReadingRemark = meterReadingRemark;
    }



    public Equipment getEquipment() {
        return equipment;
    }

    public void setEquipment(Equipment equipment) {
        this.equipment = equipment;
    }

    public MeterReading getMeterReading() {
        return meterReading;
    }

    public void setMeterReading(MeterReading meterReading) {
        this.meterReading = meterReading;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("meterReadingId", getMeterReadingId())
                .append("equipmentId", getEquipmentId())
                .append("userId", getUserId())
                .append("lastMonthReverseActive", getLastMonthReverseActive())
                .append("totalReverseActive", getTotalReverseActive())
                .append("photos", getPhotos())
                .append("meterReadingTime", getMeterReadingTime())
                .append("meterReadingRemark", getMeterReadingRemark())
                .toString();
    }
}
