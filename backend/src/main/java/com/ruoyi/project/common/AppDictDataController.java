package com.ruoyi.project.common;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.ruoyi.framework.web.controller.BaseController;
import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.project.system.domain.SysDictType;
import com.ruoyi.project.system.domain.SysDictData;
import com.ruoyi.project.system.service.ISysDictTypeService;

@RestController
@RequestMapping("/common")
public class AppDictDataController extends BaseController {

    @Autowired
    private ISysDictTypeService dictTypeService;

    @GetMapping("/app_dicts")
    public AjaxResult getDictData() {
        List<SysDictType> dictTypes = dictTypeService.selectDictTypeList(new SysDictType());
        Map<String, List<SysDictData>> dictMap = new HashMap<>();
        for (SysDictType type : dictTypes) {
            List<SysDictData> dictData = dictTypeService.selectDictDataByType(type.getDictType());
            dictMap.put(type.getDictType(), dictData);
        }
        return success(dictMap);
    }
}