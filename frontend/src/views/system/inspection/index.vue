<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="电站" prop="stationId">
        <el-select v-model="queryParams.stationId" placeholder="请选择电站" clearable style="width: 150px">
          <el-option v-for="item in stationOptions" :key="item.stationId" :label="item.stationName"
            :value="item.stationId" />
        </el-select>
      </el-form-item>
      <el-form-item label="任务名称" prop="inspectionName">
        <el-input v-model="queryParams.inspectionName" placeholder="请输入任务名称" clearable style="width: 150px" />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable style="width: 150px">
          <el-option v-for="dict in inspection_status" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd"
          v-hasPermi="['system:inspection:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate"
          v-hasPermi="['system:inspection:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['system:inspection:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="handleExport"
          v-hasPermi="['system:inspection:export']">导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="inspectionList" @selection-change="handleSelectionChange" stripe>
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="电站" align="center" prop="station.stationName" width="140" />
      <el-table-column label="任务名称" align="center" prop="inspectionName" width="140" />
      <el-table-column label="计划时间" align="center" width="180">
        <template #default="scope">
          <span>
            {{ parseTime(scope.row.assignStartTime, '{y}-{m}-{d}') }} - {{ parseTime(scope.row.assignEndTime, '{y}-{m}-{d}') }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="设定人员" align="center" prop="assignUserName" width="100" />
      <el-table-column label="设定负责人" align="center" prop="assignChargeUserName" width="100" />
      <el-table-column label="实际人员" align="center" prop="inspectorUserName" width="100" />
      <el-table-column label="接收时间" align="center" prop="receiveTime" width="120">
        <template #default="scope">
          <span>{{ parseTime(scope.row.receiveTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="提交时间" align="center" prop="submitTime" width="120">
        <template #default="scope">
          <span>{{ parseTime(scope.row.submitTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="审核时间" align="center" prop="reviewTime" width="120">
        <template #default="scope">
          <span>{{ parseTime(scope.row.reviewTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status" width="80">
        <template #default="scope">
          <dict-tag :options="inspection_status" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column label="创建人" align="center" prop="createBy" width="70"></el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="120"
        show-overflow-tooltip></el-table-column>
      <el-table-column label="审核意见" align="center" prop="reviewComment" show-overflow-tooltip />
      <el-table-column label="备注" align="center" prop="remark" show-overflow-tooltip />
      <el-table-column label="操作" align="center" fixed="right" width="80">
        <template #default="scope">
          <el-dropdown>
            <el-button type="primary" size="small">
              操作<el-icon class="el-icon--right"><arrow-down /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <!-- 查看按钮 -->
                <el-dropdown-item @click="handleView(scope.row)">

                  <el-icon>
                    <View />
                  </el-icon>查看
                </el-dropdown-item>
                <!-- 状态操作：4-已提交时显示审核按钮 -->
                <div v-hasPermi="['system:inspection:review']">
                  <div v-if="scope.row.status === '4'">
                    <el-dropdown-item @click="handleReview(scope.row)">
                      <el-icon>
                        <check /> </el-icon
                      >审核
                    </el-dropdown-item>
                  </div>
                </div>
                <div v-hasPermi="['system:inspection:edit']">
                  <el-dropdown-item @click="handleUpdate(scope.row)">
                    <el-icon>
                      <edit />
                    </el-icon>修改
                  </el-dropdown-item>
                </div>
                <div v-hasPermi="['system:inspection:remove']">
                  <el-dropdown-item @click="handleDelete(scope.row)">
                    <el-icon>
                      <delete />
                    </el-icon>删除
                  </el-dropdown-item>
                </div>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize" @pagination="getList" />

    <!-- 添加或修改巡检任务对话框 -->
    <InspectionForm v-model:visible="open" :title="title" :inspection-data="form" @submit="handleFormSubmit"
      @cancel="cancel" />

    <!-- 审核对话框 -->
    <el-dialog title="审核巡检任务" v-model="reviewDialogVisible" width="500px" append-to-body>
      <el-form ref="reviewFormRef" :model="reviewForm" label-width="80px">
        <el-form-item label="审核意见" prop="reviewComment">
          <el-input
            v-model="reviewForm.reviewComment"
            type="textarea"
            :rows="4"
            placeholder="请输入审核意见"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="reviewDialogVisible = false">取消</el-button>
          <el-button type="primary" :loading="reviewLoading" @click="submitReview">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 查看巡检详情对话框 -->
    <el-dialog title="巡检详情" v-model="detailDialogVisible" width="1000px" append-to-body>
      <InspectionDetail
        v-if="detailDialogVisible"
        :inspection-id="selectedInspectionId"
      />
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailDialogVisible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Inspection">
import {
  listInspection,
  getInspection,
  delInspection,
  addInspection,
  updateInspection,
  reviewInspection
} from '@/api/system/inspection';
import { listStation } from '@/api/system/station';
import {ArrowDown, Edit, Delete, Check, View} from '@element-plus/icons-vue';
import InspectionForm from './InspectionForm.vue';
import InspectionDetail from './InspectionDetail.vue';

const { proxy } = getCurrentInstance();
const { inspection_status } = proxy.useDict('inspection_status');

const inspectionList = ref([]);
const stationOptions = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref('');
const daterangeAssignTime = ref([]);

// 审核相关变量
const reviewDialogVisible = ref(false);
const reviewForm = ref({
  inspectionId: null,
  reviewComment: ''
});
const reviewLoading = ref(false);

// 详情弹窗相关变量
const detailDialogVisible = ref(false);
const selectedInspectionId = ref(null);

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    stationId: null,
    inspectionName: null,
    assignStartTime: null,
    assignEndTime: null,
    assignUser: null,
    inspectorUser: null,
    receiveTime: null,
    submitTime: null,
    reviewTime: null,
    status: null
  },
  rules: {
    assignUser: [{ required: true, message: '分配巡检人员不能为空', trigger: 'blur' }],
    inspectionName: [{ required: true, message: '任务名称不能为空', trigger: 'blur' }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询巡检任务列表 */
function getList() {
  loading.value = true;
  queryParams.value.params = {};
  listInspection(queryParams.value).then((response) => {
    inspectionList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 获取电站列表 */
function getStationList() {
  listStation({ pageSize: 999 }).then((response) => {
    stationOptions.value = response.rows;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
  getList();
}

// 表单重置
function reset() {
  form.value = {
    inspectionId: null,
    stationId: null,
    inspectionName: null,
    assignStartTime: null,
    assignEndTime: null,
    assignUser: null,
    inspectorUser: null,
    receiveTime: null,
    submitTime: null,
    reviewTime: null,
    reviewComment: null,
    status: null,
    delFlag: null,
    createBy: null,
    createTime: null,
    updateBy: null,
    updateTime: null,
    remark: null
  };
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  daterangeAssignTime.value = [];
  proxy.resetForm('queryRef');
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.inspectionId);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = '添加巡检任务';
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _inspectionId = row.inspectionId || ids.value;
  getInspection(_inspectionId).then((response) => {
    form.value = response.data;
    open.value = true;
    title.value = '修改巡检任务';
  });
}

/** 处理表单提交 */
function handleFormSubmit(formData) {
  // 提取巡检设备信息
  const { equipments, ...inspectionData } = formData;

  // 如果表单数据中已经包含设备信息，则不需要再次检查
  // 这种情况可能是从设备关联保存按钮提交的
  if (equipments && equipments.length > 0) {
    // 直接处理提交
  }
  // 否则检查是否选择了设备
  else if (!equipments || equipments.length === 0) {
    proxy.$modal.msgWarning('请至少选择一个设备进行巡检');
    return;
  }

  if (inspectionData.inspectionId != null) {
    // 修改巡检任务，包含设备信息
    updateInspection({
      inspection: inspectionData,
      equipments: equipments
    }).then((response) => {
      proxy.$modal.msgSuccess('修改成功');
      open.value = false;
      getList();
    });
  } else {
    // 新增巡检任务，包含设备信息
    addInspection({
      inspection: inspectionData,
      equipments: equipments
    }).then((response) => {
      proxy.$modal.msgSuccess('新增成功');
      open.value = false;
      getList();
    });
  }
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _inspectionIds = row.inspectionId || ids.value;
  proxy.$modal
    .confirm('是否确认删除巡检任务编号为"' + _inspectionIds + '"的数据项？')
    .then(function () {
      return delInspection(_inspectionIds);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess('删除成功');
    })
    .catch(() => { });
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    'system/inspection/export',
    {
      ...queryParams.value
    },
    `inspection_${new Date().getTime()}.xlsx`
  );
}

/** 审核按钮操作 */
function handleReview(row) {
  reviewForm.value = {
    inspectionId: row.inspectionId,
    reviewComment: ''
  };
  reviewDialogVisible.value = true;
}

/** 提交审核 */
function submitReview() {
  if (!reviewForm.value.reviewComment.trim()) {
    proxy.$modal.msgWarning('请填写审核意见');
    return;
  }

  reviewLoading.value = true;

  const reviewData = {
    inspectionId: reviewForm.value.inspectionId,
    reviewComment: reviewForm.value.reviewComment
    // 不发送reviewTime和status，让后端自动设置
  };

  reviewInspection(reviewData).then((response) => {
    proxy.$modal.msgSuccess('审核成功');
    reviewDialogVisible.value = false;
    reviewLoading.value = false;
    getList(); // 刷新列表
  }).catch(() => {
    reviewLoading.value = false;
  });
}

/** 查看按钮操作 */
function handleView(row) {
  selectedInspectionId.value = row.inspectionId;
  detailDialogVisible.value = true;
}

getStationList();
getList();
</script>
